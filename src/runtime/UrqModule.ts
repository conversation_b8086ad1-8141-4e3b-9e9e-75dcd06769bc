import type { char_t, int16_t, int32_t, int8_t, pointer_t, reference_t, uint32_t, uint8_t } from "./wasm";

export interface UrqModule {
    /**
     * 主函数
     *
     * @param id 上下文的 id
     * @param w 宽度
     * @param h 高度
     * @param beyond_display 是否超越显示
     */
    // _urq_web_main(root: reference_t<char_t>, id: int32_t, w: int16_t, h: int16_t): void;

    _urq_web_main(root: reference_t<char_t>, project_id: int32_t, display_id: int16_t, main_page_id: int16_t, w: int16_t, h: int16_t, beyond_display: boolean): void;

    /**
     * 设置错误码
     * @param errno 错误码
     */
    _urq_web_set_errno(errno: int32_t): void;

    /**
     * 获取错误码
     * @returns 错误码
     */
    _urq_web_get_errno(): int32_t;

    /**
     * 申请内存
     * @param size 需要申请的大小
     * @returns 内存指针
     */
    _urq_web_malloc(size: uint32_t): pointer_t;

    /**
     * 释放内存
     * @param ptr 内存指针
     */
    _urq_web_free(ptr: pointer_t): void;

    /**
     * 重新加载 display
     */
    _urq_web_reload_display(display_id: int16_t, main_page_id: int16_t): void;

    /**
     * 重新加载页面
     */
    _urq_web_reload_page(): void;

    /**
     * 重新加载项目
     */
    _urq_web_reload_project(project_id: int32_t, display_id: int16_t, main_page_id: int16_t, w: int16_t, h: int16_t, beyond_display: boolean): void;

    /**
     * 时间增加
     */
    _urq_web_time_inc(): void;

    /**
     * 修改画面
     */
    _urq_web_set_page(page_id: int32_t, force: int8_t, state: int8_t): void;

    /**
     * 设置画面透明度
     */
    _urq_web_set_overlay_page(page_id: int32_t, opa: uint8_t): void;

    /**
     * 设置组件大小
     */
    _urq_web_set_widget_size(page_id: int32_t, widget_id: int32_t, w: int16_t, h: int16_t): void;

    /**
     * 设置组件位置
     */
    _urq_web_set_widget_pos(page_id: int32_t, widget_id: int32_t, x: int16_t, y: int16_t): void;

    /**
     * 设置项目样式
     */
    _urq_web_set_project_theme(theme_id: int16_t): void;

    /**
     * 设置项目语言
     */
    _urq_web_set_project_language(language_id: int16_t): void;

    /**
     * 设置项目背景颜色
     * @param r 红色
     * @param g 绿色
     * @param b 蓝色
     * @param a 透明度
     */
    _urq_web_set_project_bg_color(r: uint8_t, g: uint8_t, b: uint8_t, a: uint8_t): void;

    /**
     * 设置页面状态
     *
     * @param state 状态
     */
    _urq_web_set_state(state: int8_t): void;
    
    /**
     * 设置画布背景颜色
     * @param color 颜色
     */
    _urq_web_set_project_canvas_background(color: uint32_t): void;
}
