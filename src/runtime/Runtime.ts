// import { numberInput1 } from "../project-1/page/1";
import type { EditorRuntime } from "./EditorRuntime";
import { err, Errno, ok, type Result } from "./Errno";
import type { FsHook } from "./FsHook";
import type { char_t, int32_t, pointer_t, reference_t, uint32_t, uint8_t, Wasm } from "./wasm";

const PATH_MAX_LENGTH = 255;

export class Runtime implements EditorRuntime {
    /** 上下文计数器 */
    private static _counter: number = 0;
    /** id 与 Context 的表 */
    private static _map: Map<int32_t, Runtime> = new Map();
    /** 上下文的 id */
    readonly id: int32_t;
    /** wasm 模块 */
    readonly wasm: Wasm;
    /** 画布 */
    readonly canvas: HTMLCanvasElement;
    /** 文件系统钩子 */
    readonly fs: FsHook;

    constructor(wasm: Wasm, fsHook: FsHook) {
        const canvas = document.createElement("canvas");
        canvas.style.backgroundColor = "black";
        canvas.addEventListener("contextmenu", (e) => {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
        });

        this.id = ++Runtime._counter as int32_t;
        this.canvas = canvas;
        this.wasm = wasm;
        this.fs = fsHook;
        Runtime._map.set(this.id, this);
    }

    static find(id: int32_t) {
        const ctx = Runtime._map.get(id);
        if (ctx === undefined) {
            throw new Error("240904104557591, invalid context: " + id);
        }
        return ctx;
    }

    reloadProject(displayId: number, pageId: number, width: number, height: number, beyondDisplay: boolean) {
        this.wasm._urq_web_reload_project(this.id, displayId, pageId, width, height, beyondDisplay);
    }

    reloadPage() {
        this.wasm._urq_web_reload_page();
    }

    reloadDisplay(displayId: number, pageId: number) {
        this.wasm._urq_web_reload_display(displayId, pageId);
    }

    setPage(id: number, force: boolean = false, state: number = -2): void {
        const f = force === true ? 1 : 0;
        if (!Number.isInteger(state)) {
            console.error("240904104557591, invalid state: " + state);
            return;
        }
        this.wasm._urq_web_set_page(id, f, state);
    }

    update() {
        this.wasm._urq_web_time_inc();
    }

    setProjectBgColor(r: number, g: number, b: number, a: number): void {
        this.wasm._urq_web_set_project_bg_color(r, g, b, a);
    }

    setState(state: number): void {
        if (state < 0 || state > 127) {
            throw new Error("240904104557591, invalid state: " + state);
        }
        this.wasm._urq_web_set_state(state);
    }

    setWidgetSize(pageId: number, widgetId: number, width: number, height: number): void {
        this.wasm._urq_web_set_widget_size(pageId, widgetId, width, height);
    }

    setLanguage(language_id: number): void {
        this.wasm._urq_web_set_project_language(language_id);
    }

    setWidgetPos(pageId: number, widgetId: number, left: number, top: number): void {
        this.wasm._urq_web_set_widget_pos(pageId, widgetId, left, top);
    }

    setTheme(theme_no: number): void {
        this.wasm._urq_web_set_project_theme(theme_no);
    }

    setLang(lang_no: number): void {
        this.wasm._urq_web_set_project_language(lang_no);
    }

    // --------------------------------------------------------------------------

    free(ptr: pointer_t) {
        this.wasm._urq_web_free(ptr);
    }

    malloc(size: number): pointer_t {
        return this.wasm._urq_web_malloc(size);
    }

    main(projectId: number, displayId: number, pageId: number, w: number, h: number, beyondDisplay: boolean): void {
        this.wasm._urq_web_main(0, projectId, displayId, pageId, w, h, beyondDisplay);
    }

    readBuffer(ptr: pointer_t, len: uint32_t): Uint8Array {
        return this.wasm.HEAPU8.subarray(ptr, ptr + len);
    }

    readI32(ptr: reference_t<int32_t>): int32_t {
        return this.wasm.HEAP32[ptr >> 2] as int32_t;
    }

    readU32(ptr: reference_t<uint32_t>): uint32_t {
        return this.wasm.HEAPU32[ptr >> 2] as uint32_t;
    }

    readString(ptr: reference_t<char_t>): Result<string> {
        for (let i = ptr; i < ptr + PATH_MAX_LENGTH; i++) {
            if (this.wasm.HEAPU8[i] === 0) {
                const decoder = new TextDecoder();
                const path_str = decoder.decode(this.wasm.HEAPU8.subarray(ptr, i));
                return ok(path_str);
            }
        }
        return err(Errno.ArgIllegal);
    }

    setErrno(ecode: Errno): -1 {
        this.wasm._urq_web_set_errno(ecode);
        return -1;
    }

    writeI32(ptr: reference_t<int32_t>, value: int32_t): Errno {
        this.wasm.HEAP32[ptr >> 2] = value;
        return Errno.Ok;
    }

    writeU32(ptr: reference_t<uint32_t>, value: number): Errno {
        this.wasm.HEAPU32[ptr >> 2] = value;
        return Errno.Ok;
    }

    writePtr(ptr: reference_t<pointer_t>, value: pointer_t): Errno {
        this.wasm.HEAPU32[ptr >> 2] = value;
        return Errno.Ok;
    }

    writePointer(ptr: reference_t<pointer_t>, value: pointer_t) {
        this.wasm.HEAPU32[ptr >> 2] = value;
    }

    /**
     * 为 buffer 分配内存，并将分配的指针写入 ptr
     * @param ptr    目标指针
     * @param buffer 源数据
     * @returns 错误码
     */
    writeBufferPtr(ptr: reference_t<uint8_t>, buffer: Uint8Array): Errno {
        const p = this.malloc(buffer.length);
        if (p === 0) {
            return Errno.ENOMEM;
        }
        for (let i = 0; i < buffer.length; i++) {
            this.wasm.HEAPU8[p + i] = buffer[i]!;
        }
        this.writePointer(ptr, p);
        return Errno.Ok;
    }

    writeBuffer(ptr: reference_t<uint8_t>, buffer: Uint8Array): Errno {
        for (let i = 0; i < buffer.length; i++) {
            this.wasm.HEAPU8[ptr + i] = buffer[i]!;
        }
        return Errno.Ok;
    }
}
