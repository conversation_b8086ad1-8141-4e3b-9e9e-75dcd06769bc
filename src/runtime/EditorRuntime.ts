/**
 * 编辑器使用的运行时
 */
export interface EditorRuntime {
    /**
     * 当前编辑器使用的画布
     */
    readonly canvas: HTMLCanvasElement;

    /**
     * 更新画布
     */
    update(): void;

    //重新加载工程
    reloadProject(displayId: number, pageId: number, width: number, height: number, beyondDisplay: boolean): void;

    /**
     * 重新加载 display
     */
    reloadDisplay(displayId: number, pageId: number): void;

    /**
     * 设置画面
     * @param page  画面ID
     * @param force 是否强制切换
     * @param state 状态，如果状态为 -2 则不发生变化
     */
    setPage(page: number, force?: boolean, state?: number): void;

    /**
     * 设置工程背景颜色
     */
    setProjectBgColor(r: number, g: number, b: number, a: number): void;

    /**
     * 设置当前显示页面所有图元的状态
     * @param state 状态，状态的值只能为 0-127
     */
    setState(state: number): void;

    /**
     * 设置工程语言
     * @param language_id 语言ID
     */
    setLanguage(language_id: number): void;

    /**
     * 设置工程主题
     * @param theme_no 主题编号
     */
    setTheme(theme_no: number): void;
}
