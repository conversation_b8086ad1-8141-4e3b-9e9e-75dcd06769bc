import { create } from "../runtime/index";
import type { Runtime } from "../runtime/Runtime";
import { fetchFs } from "./fetchFs";

const PROJECT_WIDTH = 1600;
const PROJECT_HEIGHT = 1200;

function createBtn(parent: HTMLElement, name: string, cb: () => void) {
    const btn = document.createElement("button");
    btn.textContent = name;
    btn.addEventListener("click", cb);
    parent.appendChild(btn);
}

function header(rt: Runtime) {
    const div = document.createElement("div");
    createBtn(div, "重新加载工程", () => {
        rt.reloadProject(-1, 0, PROJECT_WIDTH, PROJECT_HEIGHT, true);
    });
    createBtn(div, "重新加载画面1", () => {
        rt.setPage(1, true);
    });
    createBtn(div, "重新加载画面2", () => {
        rt.setPage(2, true);
    });
    createBtn(div, "设置状态0", () => {
        rt.setState(0);
    });
    createBtn(div, "设置状态1", () => {
        rt.setState(1);
    });
    createBtn(div, "设置状态2", () => {
        rt.setState(2);
    });
    createBtn(div, "设置状态3", () => {
        rt.setState(3);
    });
    createBtn(div, "设置主题0", () => {
        rt.setTheme(0);
    });
    createBtn(div, "设置主题1", () => {
        rt.setTheme(1);
    });
    createBtn(div, "设置主题2", () => {
        rt.setTheme(2);
    });
    createBtn(div, "设置主题3", () => {
        rt.setTheme(3);
    });
    createBtn(div, "设置语言0", () => {
        rt.setLang(0);
    });
    createBtn(div, "设置语言1", () => {
        rt.setLang(1);
    });
    createBtn(div, "设置语言2", () => {
        rt.setLang(2);
    });
    document.body.insertBefore(div, document.body.firstChild);
}

async function main() {
    const runtimePath = "/urq/ui/build/editor/debug/ui/";
    const res = await fetch(runtimePath + "/editor.js");
    window.znd_runtime_path = res.headers.get("Content-Type") === "application/javascript" ? runtimePath : "/";
    window.znd_runtime_debug_c = true; // 是否输出来自 c 语言的调试信息

    const runtime = await create(PROJECT_WIDTH, PROJECT_HEIGHT, fetchFs, document.body);
    header(runtime as Runtime);
    Reflect.defineProperty(window, "rt", { value: runtime });

    function sleep(ms: number) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }

    while (true) {
        runtime.update();
        await sleep(1000 / 60);
    }
}

main().then(() => {
    console.log("main end");
});
