import { DataFormatType, ScrollDirection, WidgetPropertyEnum } from "../../proto";
import { defineAddress<PERSON>ey, defineModbusAddress, defineP<PERSON>, ModbusRegister } from "../../helper/define";
import { posOf, sizeOf, type FullWidget } from "../../helper/utils";
import { ImageTemplates } from "../property/image";
import { StyleTemplates } from "../property/style";
// import { texts } from "../texts";
import * as widget from "../../../proto/dev/widget";
// import { WidgetCalendar_CalendarType } from "../../../proto/dev/widget";
import { DateFormatType, TimeFormatType,ListDataResourceType, SymbolType, Direction } from "../../../proto/dev/common";
import { TextTemplates } from "../property/text";
import { ButtonKeyCode} from "../../../proto/dev/widget";
import { lineTemplates } from "../property/line";
import { pointTemplates } from "../property/point";
import { NumberValueTemplates, ColorTemplates } from "../property/util";
// // 导入刻度尺工厂函数和示例
// import {
//     createHorizontalRuler,
//     createVerticalRuler,
//     createArcRuler,
//     // rulerExamples
// } from "./ruler-examples";

// 文本显示
export const label1: FullWidget = {
    ui: {
        idU16: 1,
        location: posOf(300, 300),
        size: sizeOf(50, 50),
        confirmParam: {
            confirmPageIdU16: 3,
        },
        style: [StyleTemplates.fixedColorID1(), StyleTemplates.card()],
        text: [
            TextTemplates.text_tagid_multi(2),
        ],
        actionsU16: [1, 2, 3],
        widget: {
            $case: "widgetText",

            value: {
            },
        },
    },
    attributes: {
        [defineAddressKey(WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_BIT_WRITE_ADDRESS, 1)]: defineModbusAddress(
            1,
            0x601,
            { reg: ModbusRegister.Coil }
        ),
    },
    actions: [],
};

// 图片
export const image1: FullWidget = {
    ui: {
        idU16: 2,
        location: posOf(100, 300),
        size: sizeOf(200, 200),
        // style: [StyleTemplates.card()],
        graphic: [ImageTemplates.image3(), ImageTemplates.image1()],
        stateProperty: {
            noStateWay: widget.NoStateWay.NO_STATE_WAY_USE_BLANK
        },
        widget: {
            $case: "widgetGraphic",
            value: {},
        },
    },
    attributes: {
        // [0x0201]: defineModbusAddress(1, 258, ModbusRegister.Coil),
        // [0x0202]: defineModbusAddress(1, 259, ModbusRegister.Coil),
        // [0x0102]: defineZndAddress(1, ZhenDianRegister.Hb),
        // [0x0103]: defineZndAddress(0, ZhenDianRegister.Hw),
        // [0x0104]: defineZndAddress(1, ZhenDianRegister.Hw),
        // [0x0203]: defineModbusAddress(1, 256, ModbusRegister.HoldingRegister),
        // [0x0204]: defineModbusAddress(1, 257, ModbusRegister.HoldingRegister),
        // [defineAddressKey(WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_ADDRESS, 0)]: defineModbusAddress(
        //     1,
        //     258
        // ),
    },
    actions: [
        // {
        //     actionTiming: ActionTiming.ACTION_TIMING_PRESS,
        //     wordAddValue: {
        //         addValue: {
        //             valueU16: 1,
        //         },
        //     },
        //     waitResult: false,
        // },
    ],
};

// 字符串输入
export const stringInput1: FullWidget = {
    ui: {
        idU16: 3,
        location: posOf(100, 400),
        size: sizeOf(200, 200),
        widget: {
            $case: "widgetString",
            value: {
                supportInput: true,
                currentValue: "1234567890",
                maxLengthU16: 10,
                minLengthU16: 1,
                showScrollbar: true,
                passwordMode: false,
                keyboardPageIdU16: 3,
                dataFormat: DataFormatType.DATA_FORMAT_TYPE_UTF8,
            },
        },
    },
    attributes: {},
    actions: [],
};

// 位显示
export const bit1: FullWidget = {
    ui: {
        idU16: 5,
        location: posOf(100, 0),
        size: sizeOf(150, 200),
        minPressTimeU16: 0,
        minPressIntervalU16: 0,
        stateProperty: {
            stateCountU8: 2,
            noStateWay: widget.NoStateWay.NO_STATE_WAY_USE_BLANK
        },
        graphic: [ImageTemplates.image0(), ImageTemplates.image1(), ImageTemplates.image2()],
        style: [StyleTemplates.card(), StyleTemplates.transparent()],
        widget: {
            $case: "widgetBit",
            value: {
                text: [TextTemplates.font_textpad()],
            },
        },
    },
    attributes: {},
    actions: [],
};

// 数值输入
export const numberInput1: FullWidget = {
    ui: {
        idU16: 4,
        location: posOf(300, 200),
        size: sizeOf(150, 200),
        confirmParam: {
            confirmPageIdU16: 2,
            //confirmWaitTimeU16: 1000,
            //confirmTimeoutRun: true,
        },
        widget: {
            $case: "widgetNumber",
            value: {
                supportInput: true,
                currentValue: NumberValueTemplates.numberValue("valueDouble", 100.12),
                maxValue: NumberValueTemplates.numberValue("valueI32", 255),
                minValue: NumberValueTemplates.numberValue("valueI32", 0),
                integerDigitsU8: 3,
                decimalDigitsU8: 2,
                hideLeadingZero: true,
                hideTrailingZero: true,
                showPlusSign: true,
                lessThanMinColor: ColorTemplates.colorValue(0xff0000ff),
                greaterThanMaxColor: ColorTemplates.colorValue(0x00ff00ff),
                lessThanMinFlashTimeU8: 10000,
                greaterThanMaxFlashTimeU8: 10000,
                passwordMode: false,
                keyboardPageIdU16: 3,
                dataFormat: DataFormatType.DATA_FORMAT_TYPE_UTF8,
            },
        },
    },
    attributes: {},
    actions: [],
};

// 克隆
export const clone1: FullWidget = {
    ui: {
        idU16: 6,
        location: posOf(400, 200),
        size: sizeOf(150, 200),
        style: [StyleTemplates.transparent()],
        widget: {
            $case: "widgetClone",
            value: {
                pageIdU16: 1,
                widgetIdU16: 8,
            },
        },
        confirmParam: {
            confirmPageIdU16: 4,
            //confirmWaitTimeU16: 1000,
            //confirmTimeoutRun: true,
        },
    },
    attributes: {},
    actions: [],
};

export const curve1: widget.WidgetCurve = {
    curveType: widget.CurveType.CURVE_TYPE_BAR,
    seriesCountU8: 1,
    xMode: {
        $case: "pointCountU16",
        value: 10,
    },
    drawDirection: ScrollDirection.SCROLL_DIRECTION_LEFT_TO_RIGHT,
    enableCursor: true,
    cursorWidthU8: 5,
    cursorColor: ColorTemplates.colorValue(0xff0000ff),
    seriesConfig: [
        {
            color: ColorTemplates.colorValue(0xff0000ff),
            pointConfig: pointTemplates.point1(),
            lineConfig: lineTemplates.line1(),
            supportScale: false,
            showFadeMask: true,
        },
        // 2
        {
            color: ColorTemplates.colorValue(0x0ff00ff),
            pointConfig: pointTemplates.point_square(),
            lineConfig: lineTemplates.line_dash(),
            supportScale: false,
            showFadeMask: false,
        },
    ],
    scaleValueConfigY2: {
        show: true,
        gridShowType: widget.ScaleConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
        scaleMain: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: ColorTemplates.colorValue(0xff0000ff),
        },
        scaleSec: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: ColorTemplates.colorValue(0xff0000ff),
        },
        maxValue: NumberValueTemplates.numberValue("valueI32", 100),
        minValue: NumberValueTemplates.numberValue("valueI32", 0),
    },
    scaleValueConfigY: {
        show: true,
        gridShowType: widget.ScaleConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
        scaleMain: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: ColorTemplates.colorValue(0xff0000ff),
        },
        scaleSec: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: ColorTemplates.colorValue(0xff0000ff),
        },
        maxValue: NumberValueTemplates.numberValue("valueI32", 100),
        minValue: NumberValueTemplates.numberValue("valueI32", 0),
        gridLineConfig: {
            lineType: widget.LineType.LINE_TYPE_LINE,
            roundEnd: false,
            roundStart: false,
            dashWidthU8: 5,
            dashGapU8: 3,
            widthU8: 2,
            color: ColorTemplates.colorValue(0xf02f00ff),
            lengthU8: 6,
        },
    },
    scaleValueConfigX: {
        show: true,
        gridShowType: widget.ScaleConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
        scaleMain: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: ColorTemplates.colorValue(0xff0000ff),
        },
        scaleSec: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: ColorTemplates.colorValue(0xff0000ff),
        },
        gridLineConfig: {
            lineType: widget.LineType.LINE_TYPE_LINE,
            dashWidthU8: 5,
            dashGapU8: 3,
            widthU8: 2,
            color: ColorTemplates.colorValue(0xff2000ff),
            roundStart: false,
            roundEnd: false,
            lengthU8: 9
        },
    },
    useCurrentTime: false,
    timeFormat: TimeFormatType.TIME_FORMAT_TYPE_HM,
    dateFormat: DateFormatType.DATE_FORMAT_TYPE_YMD,
};
export const curve2: widget.WidgetCurve = {
    curveType: widget.CurveType.CURVE_TYPE_BAR,
    seriesCountU8: 1,
    xMode: {
        $case: "pointCountU16",
        value: 10,
    },
    drawDirection: ScrollDirection.SCROLL_DIRECTION_LEFT_TO_RIGHT,
    enableCursor: true,
    cursorWidthU8: 5,
    cursorColor: ColorTemplates.colorValue(0xff0000ff),
    seriesConfig: [
        {
            color: ColorTemplates.colorValue(0xff0000ff),
            lineConfig: lineTemplates.line_dot(),
            supportScale: false,
            showFadeMask: false,
        },
        // 2
        {
            color: ColorTemplates.colorValue(0x0ff00ff),
            supportScale: false,
            showFadeMask: false,
        },
    ],
    scaleValueConfigY2: {
        show: true,
        gridShowType: widget.ScaleConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
        scaleMain: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: ColorTemplates.colorValue(0xff0000ff),
        },
        scaleSec: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: ColorTemplates.colorValue(0xff0000ff),
        },
    },
    scaleValueConfigY: {
        show: true,
        gridShowType: widget.ScaleConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
        scaleMain: {
            scaleCountU8: 10,   
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: ColorTemplates.colorValue(0xff0000ff),
        },
        scaleSec: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: ColorTemplates.colorValue(0xff0000ff),
        },
        maxValue: NumberValueTemplates.numberValue("valueI32", 100),
        minValue: NumberValueTemplates.numberValue("valueI32", 0),
        gridLineConfig: {
            lineType: widget.LineType.LINE_TYPE_LINE,
            roundEnd: false,
            roundStart: false,
            dashWidthU8: 5,
            dashGapU8: 3,
            widthU8: 2,
            lengthU8: 6,
            color: ColorTemplates.colorValue(0xf02f00ff),
        },
    },
    scaleValueConfigX: {
        show: true,
        gridShowType: widget.ScaleConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
        scaleMain: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: ColorTemplates.colorValue(0xff0000ff),
        },
        scaleSec: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: ColorTemplates.colorValue(0xff0000ff),
        },
        gridLineConfig: {
            lineType: widget.LineType.LINE_TYPE_LINE,
            dashWidthU8: 5,
            dashGapU8: 3,
            widthU8: 2,
            lengthU8: 6,
            color: ColorTemplates.colorValue(0xff2000ff),
            roundStart: false,
            roundEnd: false,
        },
    },
    useCurrentTime: false,
    timeFormat: TimeFormatType.TIME_FORMAT_TYPE_HM,
    dateFormat: DateFormatType.DATE_FORMAT_TYPE_YMD,
};

export const trendCurve1: FullWidget = {
    ui: {
        idU16: 17,
        location: posOf(100, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetTrendCurve",
            value: {
                curve: curve1,
                pauseResumeTimeU8: 10,
                isHistory: false,
                useRelativeTime: false,
                useTimeLabel: false,
                timeFormat: TimeFormatType.TIME_FORMAT_TYPE_HM,
                dateFormat: DateFormatType.DATE_FORMAT_TYPE_YMD,
            },
        },
    },
    attributes: {},
    actions: [],
};


export const label2: FullWidget = {
    ui: {
        idU16: 8,
        location: posOf(200, 250),
        size: sizeOf(120, 100),
        style: [StyleTemplates.card()],
        text: [
            TextTemplates.text_input("label2"),
        ],
        widget: {
            $case: "widgetText",
            value: {},
        },
    },
    attributes: {},
    actions: [],
};

export const calendar1: FullWidget = {
    ui: {
        idU16: 18,
        location: posOf(100, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetCalendar",
            value: {
                calendarType: widget.WidgetCalendar_CalendarType.CALENDAR_TYPE_DROPDOWN,
                weekBgColor: ColorTemplates.colorValue(0xff0000ff),
                weekFontColor: ColorTemplates.colorValue(0x00ff00ff),
                todayBgColor: ColorTemplates.colorValue(0x0000ffff),
                todayFontColor: ColorTemplates.colorValue(0x0000ffff),
                highlightColor: ColorTemplates.colorValue(0x0000ffff),
                highlightFontColor: ColorTemplates.colorValue(0x0000ffff),
                highlightDateConfig: [
                    {
                        yearU16: 2025,
                        monthU8: 5,
                        dayU8: 1,
                    },
                ],
            },
        },
    },
};

export const xyCurve1: FullWidget = {
    ui: {
        idU16: 19,
        location: posOf(100, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetXyCurve",
            value: {
                curve: curve1,
            },
        },
    },
    attributes: {},
    actions: [],
};


// export const roller1: FullWidget = {    
//     ui: {
//         idU16: 20,
//         location: posOf(100, 100),
//         size: sizeOf(60, 120),
//         widget: {
//             $case: "widgetRoller",
//             value: {
//                 options: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"],
//                 defIndexU8: 0,
//                 viewCountU8: 5,
//                 font: {
//                     fontIdU8: 16,
//                     textColor: ColorTemplates.colorValue(0x0000ffff),
//                 },
//             },
//         },
//     },
//     attributes: {},
//     actions: [],
// };

export const group: FullWidget = {
    ui: {
        idU16: 21,
        location: posOf(100, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetGroup",
            value: {
                subWidgets: [label1.ui!, label2.ui!]
            },
        },
    },
    attributes: {},
    actions: [],
};

export const okBtn: FullWidget = {
    ui: {
        idU16: 22,
        location: posOf(50, 300),
        size: sizeOf(80, 80),
        // style: [{}],
        graphic: [ImageTemplates.image2(), ImageTemplates.image3()],
        widget : {
            $case: "widgetButton",
            value: {
                buttonKeyCode: ButtonKeyCode.BUTTON_KEY_CODE_OK,
            },
        },
    },
    actions: [],
    attributes: {},
};

export const trendCurve2: FullWidget = {
    ui: {
        idU16: 23,
        location: posOf(200, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetTrendCurve",
            value: {
                curve: curve1,
                pauseResumeTimeU8: 10,
                isHistory: false,
                useRelativeTime: false,
                useTimeLabel: false,
                timeFormat: TimeFormatType.TIME_FORMAT_TYPE_HM,
                dateFormat: DateFormatType.DATE_FORMAT_TYPE_YMD,
            },
        },
    },
    attributes: {},
    actions: [],
};

export const options: widget.WidgetOptionList = {
    dataResourceType: ListDataResourceType.LIST_DATA_RESOURCE_TYPE_UNSPECIFIED,
    options: ["1", "2", "3"],
    optionsValue: ["1", "2", "3"],
    selectedColor: ColorTemplates.colorValue(0x0000ffff),
    rowSpacingU8: 5,
}
export const roller1: FullWidget = {
    ui: {
        idU16: 24,
        location: posOf(200, 100),
        size: sizeOf(80, 80),
        widget: {
            $case: "widgetRollerList",
            value: {
                rollerListMode: widget.WidgetRollerList_RollerListMode.ROLLER_LIST_MODE_LIMITED,
                list: options,
                viewCountU8: 5,
                defIndexU8: 0,
            },
        },
    },
    attributes: {},
    actions: [],
};

export const dropDown: FullWidget = {
    ui: {
        idU16: 25,
        location: posOf(200, 300),
        size: sizeOf(80, 40),
        widget: {
            $case: "widgetDropList",
            value: {
                list: options,
                direction: Direction.DIRECTION_UP,
                alwaysOpen: true,
                listBgColor: ColorTemplates.colorValue(0x0000ffff),
                dropListSymbol: SymbolType.SYMBOL_TYPE_UP,
            },
        },
    },
    attributes: {},
    actions: [],
};

export const barCurve: FullWidget = {
    ui: {
        idU16: 26,
        location: posOf(100, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetBarCurve",
            value: {
                curve: curve1,
            },
        },
    },
    attributes: {},
    actions: [],
};

export const scatterCurve: FullWidget = {
    ui: {
        idU16: 27,
        location: posOf(100, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetScatterCurve",
            value: {
                curve: curve1,
            },
        },
    },
    attributes: {},
    actions: [],
};

export const meter: FullWidget = {
    ui: {
        idU16: 28,
        location: posOf(100, 100),
        size: sizeOf(200, 200),
        widget: {
            $case: "widgetMeter",
            value: {
                lowerLimitU16: 0,
                upperLimitU16: 0,
                limitWidthU8: 1,
                limitRadiusU8: 0,
                lowerLimitColor: ColorTemplates.colorValue(0xFFFFFFff),
                upperLimitColor: ColorTemplates.colorValue(0x00f0ffff),
                middleColor: ColorTemplates.colorValue(0x000000FF),
                scaleConfig: {
                    scaleMain: {
                        scaleCountU8: 4,
                        scaleWidthU8: 5,
                        scaleDrawLen: 10,
                        color: ColorTemplates.colorValue(0x00ff00ff),
                    },
                    scaleSec: {
                        scaleCountU8: 10,
                        scaleWidthU8: 5,
                        scaleDrawLen: 10,
                        color: ColorTemplates.colorValue(0xff000fff),
                    },
                    minValue: NumberValueTemplates.numberValue("valueI32", 0),
                    maxValue: NumberValueTemplates.numberValue("valueI32", 100),
                },
            },
        },
    },
    attributes: {},
    actions: [],
};


export const linear1: FullWidget = {    
    ui: {
        idU16: 29,
        location: posOf(50, 100),
        size: sizeOf(300, 100),
        widget: {
            $case: "widgetLinear",
            value: {
                linearType: widget.WidgetLinear_LinearType.LINEAR_TYPE_LINE,
                points: [
                    {xI16: 20, yI16: 20},
                    {xI16: 200, yI16: 200},
                ],
                line: {
           
                widthU8: 10,
                color: ColorTemplates.colorValue(0x0000ffff),
                },

            },
        },
    },
    attributes: {},
    actions: [],
};

export const linear2: FullWidget = {    
    ui: {
        idU16: 30,
        location: posOf(50, 100),
        size: sizeOf(300, 400),
        widget: {
            $case: "widgetLinear",
            value: {
                linearType: widget.WidgetLinear_LinearType.LINEAR_TYPE_POLYGON,
                points: [
                    {xI16: 20, yI16: 20},
                    {xI16: 200, yI16: 200},
                    {xI16: 30, yI16: 80},
                    {xI16: 80, yI16: 100},
                    {xI16: 20, yI16: 20},
                ],
                line: {
           
                widthU8: 10,
                color: ColorTemplates.colorValue(0x0000ffff),
                },

            },
        },
    },
    attributes: {},
    actions: [],
};

export const arcScale1: FullWidget = {    
    ui: {
        idU16: 31,
        location: posOf(50, 100),
        size: sizeOf(300, 400),
        widget: {
            $case: "widgetScale",
            value: {
                direction: widget.WidgetScale_ScaleDirection.SCALE_DIRECTION_BOTTOM_RIGHT,
                scaleConfig: {
                    minValue: NumberValueTemplates.numberValue("valueI32", 0),
                    maxValue: NumberValueTemplates.numberValue("valueI32", 100),
                    scaleMain: {
                        scaleCountU8: 10,
                        scaleWidthU8: 1,
                        scaleDrawLen: 10,
                        color: ColorTemplates.colorValue(0x00ff00ff),
                    },
                    scaleSec: {
                        scaleCountU8: 40,
                        scaleWidthU8: 1,
                        scaleDrawLen: 10,
                        color: ColorTemplates.colorValue(0x00ff00ff),
                    },
                },
                scaleType: {
                    $case: "arc",
                    value: {
                        startAngleI16: 0,
                        endAngleI16: 360,
                        sRadiusI16: 150,
                        lRadiusI16: 200,
                    },
                },
            },
        },
    },
    attributes: {},
    actions: [],
};

export const lineScale1: FullWidget = {    
    ui: {
        idU16: 32,
        location: posOf(50, 100),
        size: sizeOf(300, 400),
        widget: {
            $case: "widgetScale",
            value: {
                direction: widget.WidgetScale_ScaleDirection.SCALE_DIRECTION_BOTTOM_RIGHT,
                scaleConfig: {
                    minValue: NumberValueTemplates.numberValue("valueI32", 0),
                    maxValue: NumberValueTemplates.numberValue("valueI32", 100),
                    scaleMain: {
                        scaleCountU8: 5,
                        scaleWidthU8: 1,
                        scaleDrawLen: 10,
                        color: ColorTemplates.colorValue(0x00ff00ff),
                    },
                    scaleSec: {
                        scaleCountU8: 5,
                        scaleWidthU8: 1,
                        scaleDrawLen: 10,
                        color: ColorTemplates.colorValue(0x00ff00ff),
                    },
                },
                scaleType: {
                    $case: "line",
                    value: lineTemplates.line1(),
                },
                points: [
                    {xI16: 20, yI16: 20},
                    {xI16: 200, yI16: 200},
                ],
            },
        },
    },
    attributes: {},
    actions: [],
};



export default definePage(() => {
    // 返回各种刻度尺示例

    // 基础刻度尺组件
    // return [horizontalRuler, verticalRuler];

    // 使用工厂函数创建的动态刻度尺
    // return [dynamicHorizontalRuler, dynamicVerticalRuler, dynamicArcRuler];

    // 使用预定义的示例
    // return [rulerExamples.thermometer, rulerExamples.pressureGauge, rulerExamples.lengthRuler];

    // 混合使用
    // return [horizontalRuler, rulerExamples.thermometer, dynamicArcRuler];

    // 其他组件示例
    // return [image1]
    // return [stringInput1, numberInput1];
    // return [label1, image1, stringInput1, bit1, numberInput1, clone1, calendar1];
    // return [image1, stringInput1, bit1, numberInput1]
    return [lineScale1];
    // return [bit1, okBtn];
    // return [label1, label2, clone1];
    // return [scale1];
    // return [dropDown];
    // return [trendCurve1];
    // return [bit1];
    // return [label1, trendCurve1]
});
