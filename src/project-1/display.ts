import { DisplayList, PageType, type Display, type PageInfo } from "../proto";
import { encode } from "../helper/encode";
import type { PartOf } from "../helper/types";
import { randomColor, toColor } from "../helper/utils";

function createDisplay() {
    const page1: PartOf<PageInfo> = {
        name: "page-1",
        pageType: PageType.PAGE_TYPE_NORMAL,
        size: {
          widthI16: 800,
          heightI16: 600,
        },
        // style: {
        //     backgroundColor: {
        //         color: {
        //             $case: "colorVarIdU8",
        //             value: 1
        //         }
        //     }
        // },
        stackPageIds: [],
        jumpPageIds: [],
    };

    const page2: PartOf<PageInfo> = {
        name: "page-2",
        // pageType: PageType.PAGE_TYPE_MESSAGE,
        pageType: PageType.PAGE_TYPE_NORMAL,
        size: {
            widthI16: 800,
            heightI16: 600,
        },
        stackPageIds: [],
        jumpPageIds: [],
        style: {
            backgroundColor: randomColor(),
        },
    };

    const page3: PartOf<PageInfo> = {
        name: "page-3",
        pageType: PageType.PAGE_TYPE_NORMAL,
        size: {
            widthI16: 600,
            heightI16: 600,
        },
        // stackPageIds: [1, 2],
        jumpPageIds: [],
        style: {
            backgroundColor: randomColor(),
        },
        keyboardInfo: {
            currentValueWidgetId: 4,
            maxValueWidgetId: 5,
            minValueWidgetId: 6,
            //    candidateLocation: { x: 0, y: 0 },
        },
    };

    const page4: PartOf<PageInfo> = {
        name: "page-4",
        pageType: PageType.PAGE_TYPE_NORMAL,
        size: {
            widthI16: 300,
            heightI16: 200,
        },
        stackPageIds: [],
        jumpPageIds: [],
        style: {
            backgroundColor: toColor(0x0000ffff),
        },
        // keyboardInfo: {
        //     // maxValueWidgetId: 1,
        //     // minValueWidgetId: 2,
        //     currentValueWidgetId: 1,
        //  //    candidateLocation: { x: 0, y: 0 },
        // },
    };

    const display1: PartOf<Display> = {
        name: "display-1",
        mainPageIdU16: 1,
        size: {
            widthI16: 800,
            heightI16: 1600,
        },
        style: {
            // backgroundColor: toColor(0x0000FFff),
            backgroundColor: {
                color: {
                    $case: "colorVarIdU8",
                    value: 1
                }
            }
        }
    };

    const display: PartOf<DisplayList> = {
        // TODO
        displays: { 1: display1 },
        pageInfos: { 1: page1, 2: page2, 3: page3, 4: page4 },
        defaultDisplayIdU8: 1,
    };
    return display;
}

export function display() {
    return encode(DisplayList, createDisplay());
}
