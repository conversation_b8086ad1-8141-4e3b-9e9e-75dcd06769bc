import * as variable from "../../../proto/dev/variable";
import * as style from "../../../proto/dev/style";


export const NumberValueTemplates= {
    numberValue: (type: "valueI8" | "valueU8" | "valueI16" | "valueU16" | "valueI32" | "valueU32" | "valueI64" | "valueU64" | "valueFloat" | "valueDouble", value: number): variable.NumberValue => {
        return {
            from: {
                $case: type,
                value: value,
            },
        };
    },
}

export const ColorTemplates= {
    colorValue: (value: number): style.ColorReference => {
        return {
            color: {
                $case: "colorValueU32",
                value: value,
            },
        };
    },

    colorTagId: (value: number): style.ColorReference => {
        return {
            color: {
                $case: "colorVarIdU8",
                value: value,
            },
        };
    },
}