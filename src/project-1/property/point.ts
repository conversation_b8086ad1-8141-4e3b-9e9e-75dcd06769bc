
import * as widget from "../../../proto/dev/widget";

export const pointTemplates= {
    point1: (): widget.PointConfig => {
        return {
            pointType: widget.PointConfig_PointType.POINT_TYPE_CIRCLE,
            pointSize: {
                $case: "radiusU8",
                value: 5,
            },
            style: {
                backgroundColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x00ff00ff,
                    },
                },
            },
        };
    },

    point_square: (): widget.PointConfig => {
        return {
            pointType: widget.PointConfig_PointType.POINT_TYPE_SQUARE,
            pointSize: {
                $case: "size",
                value: {
                    widthI16: 5,
                    heightI16: 5,
                }
            },
            style: {
                backgroundColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x00ff00ff,
                    },
                },
            },
        };
    },
}
