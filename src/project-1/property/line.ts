
import * as widget from "../../../proto/dev/widget";

export const lineTemplates= {
    line1: (): widget.LineConfig => {
        return {
            lineType: widget.LineType.LINE_TYPE_LINE,
            roundEnd: false,
            roundStart: false,
            widthU8: 5,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0x000ff0ff,
                },
            },
            lengthU8: 5,
        };
    },

    line_dash: (): widget.LineConfig => {
        return {
            lineType: widget.LineType.LINE_TYPE_DASH,
            roundEnd: false,
            roundStart: false,
            dashWidthU8: 5,
            dashGapU8: 5,
            widthU8: 3,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0x000ff0ff,
                },
            },
            lengthU8: 5,
        };
    },

    line_dot: (): widget.LineConfig => {
        return {
            lineType: widget.LineType.LINE_TYPE_DOT,
            roundEnd: false,
            roundStart: false,
            dashWidthU8: 1,
            dashGapU8: 1,
            widthU8: 5,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0x000ff0ff,
                },
            },
            lengthU8: 5,
        };
    }
}
