import { TextReference } from "../../proto";

export const TextTemplates = {
    font_textpad: (): TextReference => ({
        from: {
            $case: "input",
            value: {
                from: {
                    $case: "singleLang",
                    value: "text",
                },
                classifyIdU16: 0,
                multiLine: false,
            },
        },
    }),

    text_tagid_multi: (tagid: number): TextReference => ({
        from: {
            $case: "tagIdU16",
            value: tagid,
        },
    }),

    text_input: (text: string): TextReference => ({
        from: {
            $case: "input",
            value: {
                from: {
                    $case: "singleLang",
                    value: text,
                },
                classifyIdU16: 0,
                multiLine: false,
            },
        },
    }),

    text_input_multi: (langId: number, text: string): TextReference => ({
        from: {
            $case: "input",
            value: {
                from: {
                    $case: "multiLang",
                    value: {
                        content: {
                            [langId]: text,
                        },
                    },
                },
                classifyIdU16: 0,
                multiLine: false,
            },
        },
    }),
};
