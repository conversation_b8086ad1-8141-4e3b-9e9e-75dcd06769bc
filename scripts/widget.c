#include "widget.h"
#include "lvgl.h"
#include "lvgl/src/core/lv_obj.h"
#include "urq/log/verbose.h"
#include "urq/lv/lv_event_code.h"
#include "urq/user_data.h"
#include "urq_conf/widget/type.h"

#define Self urq_template_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_TEMPLATE_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    // user data
    urq_obj_set_user_data(obj, user_data);
    user_data->type = URQ_WIDGET_TYPE_TEMPLATE_WIDGET;
    user_data->conf = urq_template_widget_config;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj) {}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (lv_obj_event_base(class_p, e) != LV_RES_OK) {
        log_w("template_widget error: %s\n", urq_lv_event_code(e->code));
        return;
    }
    log_v("template_widget event: %s\n", urq_lv_event_code(e->code));
    if (e->code == LV_EVENT_SIZE_CHANGED) {
        lv_area_t area;
        lv_obj_get_coords(e->target, &area);
        log_d(
            "resize, x: %d, y: %d width: %d, height: %d\n", area.x1, area.y1,
            area.x2 - area.x1, area.y2 - area.y1);
    }
}

lv_obj_t *urq_template_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj;

    obj = lv_obj_class_create_obj(&URQ_TEMPLATE_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

void urq_template_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    log_e("config: template_widget\n");
}