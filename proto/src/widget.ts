// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/widget.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { ActionList } from "./active";
import {
  DateFormatType,
  dateFormatTypeFromJSON,
  dateFormatTypeToJSON,
  Direction,
  directionFromJSON,
  directionToJSON,
  ListDataResourceType,
  listDataResourceTypeFromJSON,
  listDataResourceTypeToJSON,
  Location,
  Point,
  SaveLocation,
  saveLocationFromJSON,
  saveLocationToJSON,
  ScrollDirection,
  scrollDirectionFromJSON,
  scrollDirectionToJSON,
  Size,
  SymbolType,
  symbolTypeFromJSON,
  symbolTypeToJSON,
  TimeFormatType,
  timeFormatTypeFromJSON,
  timeFormatTypeToJSON,
} from "./common";
import { EnableControl } from "./control";
import { ColorReference, GraphicReference, StyleProperties } from "./style";
import { TextReference } from "./text";
import {
  DataFormatType,
  dataFormatTypeFromJSON,
  dataFormatTypeToJSON,
  NumberValue,
  VariableReference,
} from "./variable";

export const protobufPackage = "znd.project.v1";

/** 读写类型 */
export enum ReadWriteMode {
  /** READ_WRITE_MODE_UNSPECIFIED - 未设置 */
  READ_WRITE_MODE_UNSPECIFIED = 0,
  /** READ_WRITE_MODE_READ_ONLY - 只读 */
  READ_WRITE_MODE_READ_ONLY = 1,
  /** READ_WRITE_MODE_WRITE_ONLY - 只写 */
  READ_WRITE_MODE_WRITE_ONLY = 2,
  /** READ_WRITE_MODE_READ_WRITE_SAME - 读写同一个地址 */
  READ_WRITE_MODE_READ_WRITE_SAME = 3,
  /** READ_WRITE_MODE_READ_WRITE_DIFFERENT - 读写不同地址 */
  READ_WRITE_MODE_READ_WRITE_DIFFERENT = 4,
  UNRECOGNIZED = -1,
}

export function readWriteModeFromJSON(object: any): ReadWriteMode {
  switch (object) {
    case 0:
    case "READ_WRITE_MODE_UNSPECIFIED":
      return ReadWriteMode.READ_WRITE_MODE_UNSPECIFIED;
    case 1:
    case "READ_WRITE_MODE_READ_ONLY":
      return ReadWriteMode.READ_WRITE_MODE_READ_ONLY;
    case 2:
    case "READ_WRITE_MODE_WRITE_ONLY":
      return ReadWriteMode.READ_WRITE_MODE_WRITE_ONLY;
    case 3:
    case "READ_WRITE_MODE_READ_WRITE_SAME":
      return ReadWriteMode.READ_WRITE_MODE_READ_WRITE_SAME;
    case 4:
    case "READ_WRITE_MODE_READ_WRITE_DIFFERENT":
      return ReadWriteMode.READ_WRITE_MODE_READ_WRITE_DIFFERENT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ReadWriteMode.UNRECOGNIZED;
  }
}

export function readWriteModeToJSON(object: ReadWriteMode): string {
  switch (object) {
    case ReadWriteMode.READ_WRITE_MODE_UNSPECIFIED:
      return "READ_WRITE_MODE_UNSPECIFIED";
    case ReadWriteMode.READ_WRITE_MODE_READ_ONLY:
      return "READ_WRITE_MODE_READ_ONLY";
    case ReadWriteMode.READ_WRITE_MODE_WRITE_ONLY:
      return "READ_WRITE_MODE_WRITE_ONLY";
    case ReadWriteMode.READ_WRITE_MODE_READ_WRITE_SAME:
      return "READ_WRITE_MODE_READ_WRITE_SAME";
    case ReadWriteMode.READ_WRITE_MODE_READ_WRITE_DIFFERENT:
      return "READ_WRITE_MODE_READ_WRITE_DIFFERENT";
    case ReadWriteMode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 按钮键盘码 */
export enum ButtonKeyCode {
  /** BUTTON_KEY_CODE_UNSPECIFIED - 未设置 */
  BUTTON_KEY_CODE_UNSPECIFIED = 0,
  /** BUTTON_KEY_CODE_END_LINE - 按钮行结束，换新行 */
  BUTTON_KEY_CODE_END_LINE = 1,
  /** BUTTON_KEY_CODE_CLEAR - 清空 */
  BUTTON_KEY_CODE_CLEAR = 2,
  /** BUTTON_KEY_CODE_OK - 确定 */
  BUTTON_KEY_CODE_OK = 3,
  /** BUTTON_KEY_CODE_CANCEL - 取消 */
  BUTTON_KEY_CODE_CANCEL = 4,
  /** BUTTON_KEY_CODE_ENTER - ENTER */
  BUTTON_KEY_CODE_ENTER = 5,
  /** BUTTON_KEY_CODE_BACKSPACE - BACKSPACE */
  BUTTON_KEY_CODE_BACKSPACE = 6,
  /** BUTTON_KEY_CODE_DELETE - DELETE */
  BUTTON_KEY_CODE_DELETE = 7,
  /** BUTTON_KEY_CODE_ESCAPE - ESC */
  BUTTON_KEY_CODE_ESCAPE = 8,
  /** BUTTON_KEY_CODE_NUMBER_ADD - 数字加 */
  BUTTON_KEY_CODE_NUMBER_ADD = 9,
  /** BUTTON_KEY_CODE_NUMBER_SUB - 数字减 */
  BUTTON_KEY_CODE_NUMBER_SUB = 10,
  /** BUTTON_KEY_CODE_UP - UP */
  BUTTON_KEY_CODE_UP = 11,
  /** BUTTON_KEY_CODE_DOWN - DOWN */
  BUTTON_KEY_CODE_DOWN = 12,
  /** BUTTON_KEY_CODE_LEFT - LEFT */
  BUTTON_KEY_CODE_LEFT = 13,
  /** BUTTON_KEY_CODE_RIGHT - RIGHT */
  BUTTON_KEY_CODE_RIGHT = 14,
  /** BUTTON_KEY_CODE_HOME - HOME */
  BUTTON_KEY_CODE_HOME = 15,
  /** BUTTON_KEY_CODE_END - END */
  BUTTON_KEY_CODE_END = 16,
  /** BUTTON_KEY_CODE_PAGE_UP - PAGE_UP */
  BUTTON_KEY_CODE_PAGE_UP = 17,
  /** BUTTON_KEY_CODE_PAGE_DOWN - PAGE_DOWN */
  BUTTON_KEY_CODE_PAGE_DOWN = 18,
  UNRECOGNIZED = -1,
}

export function buttonKeyCodeFromJSON(object: any): ButtonKeyCode {
  switch (object) {
    case 0:
    case "BUTTON_KEY_CODE_UNSPECIFIED":
      return ButtonKeyCode.BUTTON_KEY_CODE_UNSPECIFIED;
    case 1:
    case "BUTTON_KEY_CODE_END_LINE":
      return ButtonKeyCode.BUTTON_KEY_CODE_END_LINE;
    case 2:
    case "BUTTON_KEY_CODE_CLEAR":
      return ButtonKeyCode.BUTTON_KEY_CODE_CLEAR;
    case 3:
    case "BUTTON_KEY_CODE_OK":
      return ButtonKeyCode.BUTTON_KEY_CODE_OK;
    case 4:
    case "BUTTON_KEY_CODE_CANCEL":
      return ButtonKeyCode.BUTTON_KEY_CODE_CANCEL;
    case 5:
    case "BUTTON_KEY_CODE_ENTER":
      return ButtonKeyCode.BUTTON_KEY_CODE_ENTER;
    case 6:
    case "BUTTON_KEY_CODE_BACKSPACE":
      return ButtonKeyCode.BUTTON_KEY_CODE_BACKSPACE;
    case 7:
    case "BUTTON_KEY_CODE_DELETE":
      return ButtonKeyCode.BUTTON_KEY_CODE_DELETE;
    case 8:
    case "BUTTON_KEY_CODE_ESCAPE":
      return ButtonKeyCode.BUTTON_KEY_CODE_ESCAPE;
    case 9:
    case "BUTTON_KEY_CODE_NUMBER_ADD":
      return ButtonKeyCode.BUTTON_KEY_CODE_NUMBER_ADD;
    case 10:
    case "BUTTON_KEY_CODE_NUMBER_SUB":
      return ButtonKeyCode.BUTTON_KEY_CODE_NUMBER_SUB;
    case 11:
    case "BUTTON_KEY_CODE_UP":
      return ButtonKeyCode.BUTTON_KEY_CODE_UP;
    case 12:
    case "BUTTON_KEY_CODE_DOWN":
      return ButtonKeyCode.BUTTON_KEY_CODE_DOWN;
    case 13:
    case "BUTTON_KEY_CODE_LEFT":
      return ButtonKeyCode.BUTTON_KEY_CODE_LEFT;
    case 14:
    case "BUTTON_KEY_CODE_RIGHT":
      return ButtonKeyCode.BUTTON_KEY_CODE_RIGHT;
    case 15:
    case "BUTTON_KEY_CODE_HOME":
      return ButtonKeyCode.BUTTON_KEY_CODE_HOME;
    case 16:
    case "BUTTON_KEY_CODE_END":
      return ButtonKeyCode.BUTTON_KEY_CODE_END;
    case 17:
    case "BUTTON_KEY_CODE_PAGE_UP":
      return ButtonKeyCode.BUTTON_KEY_CODE_PAGE_UP;
    case 18:
    case "BUTTON_KEY_CODE_PAGE_DOWN":
      return ButtonKeyCode.BUTTON_KEY_CODE_PAGE_DOWN;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ButtonKeyCode.UNRECOGNIZED;
  }
}

export function buttonKeyCodeToJSON(object: ButtonKeyCode): string {
  switch (object) {
    case ButtonKeyCode.BUTTON_KEY_CODE_UNSPECIFIED:
      return "BUTTON_KEY_CODE_UNSPECIFIED";
    case ButtonKeyCode.BUTTON_KEY_CODE_END_LINE:
      return "BUTTON_KEY_CODE_END_LINE";
    case ButtonKeyCode.BUTTON_KEY_CODE_CLEAR:
      return "BUTTON_KEY_CODE_CLEAR";
    case ButtonKeyCode.BUTTON_KEY_CODE_OK:
      return "BUTTON_KEY_CODE_OK";
    case ButtonKeyCode.BUTTON_KEY_CODE_CANCEL:
      return "BUTTON_KEY_CODE_CANCEL";
    case ButtonKeyCode.BUTTON_KEY_CODE_ENTER:
      return "BUTTON_KEY_CODE_ENTER";
    case ButtonKeyCode.BUTTON_KEY_CODE_BACKSPACE:
      return "BUTTON_KEY_CODE_BACKSPACE";
    case ButtonKeyCode.BUTTON_KEY_CODE_DELETE:
      return "BUTTON_KEY_CODE_DELETE";
    case ButtonKeyCode.BUTTON_KEY_CODE_ESCAPE:
      return "BUTTON_KEY_CODE_ESCAPE";
    case ButtonKeyCode.BUTTON_KEY_CODE_NUMBER_ADD:
      return "BUTTON_KEY_CODE_NUMBER_ADD";
    case ButtonKeyCode.BUTTON_KEY_CODE_NUMBER_SUB:
      return "BUTTON_KEY_CODE_NUMBER_SUB";
    case ButtonKeyCode.BUTTON_KEY_CODE_UP:
      return "BUTTON_KEY_CODE_UP";
    case ButtonKeyCode.BUTTON_KEY_CODE_DOWN:
      return "BUTTON_KEY_CODE_DOWN";
    case ButtonKeyCode.BUTTON_KEY_CODE_LEFT:
      return "BUTTON_KEY_CODE_LEFT";
    case ButtonKeyCode.BUTTON_KEY_CODE_RIGHT:
      return "BUTTON_KEY_CODE_RIGHT";
    case ButtonKeyCode.BUTTON_KEY_CODE_HOME:
      return "BUTTON_KEY_CODE_HOME";
    case ButtonKeyCode.BUTTON_KEY_CODE_END:
      return "BUTTON_KEY_CODE_END";
    case ButtonKeyCode.BUTTON_KEY_CODE_PAGE_UP:
      return "BUTTON_KEY_CODE_PAGE_UP";
    case ButtonKeyCode.BUTTON_KEY_CODE_PAGE_DOWN:
      return "BUTTON_KEY_CODE_PAGE_DOWN";
    case ButtonKeyCode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum MatrixButtonLayout {
  /** MATRIX_BUTTON_LAYOUT_UNSPECIFIED - 未设置、自定义 */
  MATRIX_BUTTON_LAYOUT_UNSPECIFIED = 0,
  /** MATRIX_BUTTON_LAYOUT_LOWER_LETTER - 小写字母 */
  MATRIX_BUTTON_LAYOUT_LOWER_LETTER = 1,
  /** MATRIX_BUTTON_LAYOUT_UPPER_LETTER - 大写字母 */
  MATRIX_BUTTON_LAYOUT_UPPER_LETTER = 2,
  /** MATRIX_BUTTON_LAYOUT_NUMBER - 数字 */
  MATRIX_BUTTON_LAYOUT_NUMBER = 3,
  /** MATRIX_BUTTON_LAYOUT_SPECIAL_CHARACTER - 特殊字符 */
  MATRIX_BUTTON_LAYOUT_SPECIAL_CHARACTER = 4,
  UNRECOGNIZED = -1,
}

export function matrixButtonLayoutFromJSON(object: any): MatrixButtonLayout {
  switch (object) {
    case 0:
    case "MATRIX_BUTTON_LAYOUT_UNSPECIFIED":
      return MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_UNSPECIFIED;
    case 1:
    case "MATRIX_BUTTON_LAYOUT_LOWER_LETTER":
      return MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_LOWER_LETTER;
    case 2:
    case "MATRIX_BUTTON_LAYOUT_UPPER_LETTER":
      return MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_UPPER_LETTER;
    case 3:
    case "MATRIX_BUTTON_LAYOUT_NUMBER":
      return MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_NUMBER;
    case 4:
    case "MATRIX_BUTTON_LAYOUT_SPECIAL_CHARACTER":
      return MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_SPECIAL_CHARACTER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MatrixButtonLayout.UNRECOGNIZED;
  }
}

export function matrixButtonLayoutToJSON(object: MatrixButtonLayout): string {
  switch (object) {
    case MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_UNSPECIFIED:
      return "MATRIX_BUTTON_LAYOUT_UNSPECIFIED";
    case MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_LOWER_LETTER:
      return "MATRIX_BUTTON_LAYOUT_LOWER_LETTER";
    case MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_UPPER_LETTER:
      return "MATRIX_BUTTON_LAYOUT_UPPER_LETTER";
    case MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_NUMBER:
      return "MATRIX_BUTTON_LAYOUT_NUMBER";
    case MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_SPECIAL_CHARACTER:
      return "MATRIX_BUTTON_LAYOUT_SPECIAL_CHARACTER";
    case MatrixButtonLayout.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 未找到状态时的处理方式 */
export enum NoStateWay {
  /** NO_STATE_WAY_UNSPECIFIED - 未设置，则默认使用最后一个状态 */
  NO_STATE_WAY_UNSPECIFIED = 0,
  /** NO_STATE_WAY_USE_LAST - 用最后一个状态 */
  NO_STATE_WAY_USE_LAST = 1,
  /** NO_STATE_WAY_USE_BLANK - 用空白设置或默认设置 */
  NO_STATE_WAY_USE_BLANK = 2,
  UNRECOGNIZED = -1,
}

export function noStateWayFromJSON(object: any): NoStateWay {
  switch (object) {
    case 0:
    case "NO_STATE_WAY_UNSPECIFIED":
      return NoStateWay.NO_STATE_WAY_UNSPECIFIED;
    case 1:
    case "NO_STATE_WAY_USE_LAST":
      return NoStateWay.NO_STATE_WAY_USE_LAST;
    case 2:
    case "NO_STATE_WAY_USE_BLANK":
      return NoStateWay.NO_STATE_WAY_USE_BLANK;
    case -1:
    case "UNRECOGNIZED":
    default:
      return NoStateWay.UNRECOGNIZED;
  }
}

export function noStateWayToJSON(object: NoStateWay): string {
  switch (object) {
    case NoStateWay.NO_STATE_WAY_UNSPECIFIED:
      return "NO_STATE_WAY_UNSPECIFIED";
    case NoStateWay.NO_STATE_WAY_USE_LAST:
      return "NO_STATE_WAY_USE_LAST";
    case NoStateWay.NO_STATE_WAY_USE_BLANK:
      return "NO_STATE_WAY_USE_BLANK";
    case NoStateWay.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 曲线类型 */
export enum CurveType {
  /** CURVE_TYPE_UNSPECIFIED - 未设置(默认为折线图) */
  CURVE_TYPE_UNSPECIFIED = 0,
  /** CURVE_TYPE_LINE - 折线图 */
  CURVE_TYPE_LINE = 1,
  /** CURVE_TYPE_BAR - 柱状图 */
  CURVE_TYPE_BAR = 2,
  /** CURVE_TYPE_SCATTER - 散点图 */
  CURVE_TYPE_SCATTER = 3,
  UNRECOGNIZED = -1,
}

export function curveTypeFromJSON(object: any): CurveType {
  switch (object) {
    case 0:
    case "CURVE_TYPE_UNSPECIFIED":
      return CurveType.CURVE_TYPE_UNSPECIFIED;
    case 1:
    case "CURVE_TYPE_LINE":
      return CurveType.CURVE_TYPE_LINE;
    case 2:
    case "CURVE_TYPE_BAR":
      return CurveType.CURVE_TYPE_BAR;
    case 3:
    case "CURVE_TYPE_SCATTER":
      return CurveType.CURVE_TYPE_SCATTER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CurveType.UNRECOGNIZED;
  }
}

export function curveTypeToJSON(object: CurveType): string {
  switch (object) {
    case CurveType.CURVE_TYPE_UNSPECIFIED:
      return "CURVE_TYPE_UNSPECIFIED";
    case CurveType.CURVE_TYPE_LINE:
      return "CURVE_TYPE_LINE";
    case CurveType.CURVE_TYPE_BAR:
      return "CURVE_TYPE_BAR";
    case CurveType.CURVE_TYPE_SCATTER:
      return "CURVE_TYPE_SCATTER";
    case CurveType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 线类型 */
export enum LineType {
  /** LINE_TYPE_UNSPECIFIED - 未设置 */
  LINE_TYPE_UNSPECIFIED = 0,
  /** LINE_TYPE_LINE - 实线 */
  LINE_TYPE_LINE = 1,
  /** LINE_TYPE_DASH - 虚线 */
  LINE_TYPE_DASH = 2,
  /** LINE_TYPE_DOT - 点线 */
  LINE_TYPE_DOT = 3,
  UNRECOGNIZED = -1,
}

export function lineTypeFromJSON(object: any): LineType {
  switch (object) {
    case 0:
    case "LINE_TYPE_UNSPECIFIED":
      return LineType.LINE_TYPE_UNSPECIFIED;
    case 1:
    case "LINE_TYPE_LINE":
      return LineType.LINE_TYPE_LINE;
    case 2:
    case "LINE_TYPE_DASH":
      return LineType.LINE_TYPE_DASH;
    case 3:
    case "LINE_TYPE_DOT":
      return LineType.LINE_TYPE_DOT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return LineType.UNRECOGNIZED;
  }
}

export function lineTypeToJSON(object: LineType): string {
  switch (object) {
    case LineType.LINE_TYPE_UNSPECIFIED:
      return "LINE_TYPE_UNSPECIFIED";
    case LineType.LINE_TYPE_LINE:
      return "LINE_TYPE_LINE";
    case LineType.LINE_TYPE_DASH:
      return "LINE_TYPE_DASH";
    case LineType.LINE_TYPE_DOT:
      return "LINE_TYPE_DOT";
    case LineType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 数据导出类型 */
export enum DataFileType {
  /** DATA_FILE_TYPE_UNSPECIFIED - 未设置 */
  DATA_FILE_TYPE_UNSPECIFIED = 0,
  /** DATA_FILE_TYPE_CSV - 表格 */
  DATA_FILE_TYPE_CSV = 1,
  /** DATA_FILE_TYPE_PDF - PDF */
  DATA_FILE_TYPE_PDF = 2,
  UNRECOGNIZED = -1,
}

export function dataFileTypeFromJSON(object: any): DataFileType {
  switch (object) {
    case 0:
    case "DATA_FILE_TYPE_UNSPECIFIED":
      return DataFileType.DATA_FILE_TYPE_UNSPECIFIED;
    case 1:
    case "DATA_FILE_TYPE_CSV":
      return DataFileType.DATA_FILE_TYPE_CSV;
    case 2:
    case "DATA_FILE_TYPE_PDF":
      return DataFileType.DATA_FILE_TYPE_PDF;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DataFileType.UNRECOGNIZED;
  }
}

export function dataFileTypeToJSON(object: DataFileType): string {
  switch (object) {
    case DataFileType.DATA_FILE_TYPE_UNSPECIFIED:
      return "DATA_FILE_TYPE_UNSPECIFIED";
    case DataFileType.DATA_FILE_TYPE_CSV:
      return "DATA_FILE_TYPE_CSV";
    case DataFileType.DATA_FILE_TYPE_PDF:
      return "DATA_FILE_TYPE_PDF";
    case DataFileType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 数据导出类型 */
export enum DataExportType {
  /** DATA_EXPORT_TYPE_UNSPECIFIED - 未设置 */
  DATA_EXPORT_TYPE_UNSPECIFIED = 0,
  /** DATA_EXPORT_TYPE_PER_DAY - 数据导出 */
  DATA_EXPORT_TYPE_PER_DAY = 1,
  /** DATA_EXPORT_TYPE_ALL - 数据导出所有 */
  DATA_EXPORT_TYPE_ALL = 2,
  UNRECOGNIZED = -1,
}

export function dataExportTypeFromJSON(object: any): DataExportType {
  switch (object) {
    case 0:
    case "DATA_EXPORT_TYPE_UNSPECIFIED":
      return DataExportType.DATA_EXPORT_TYPE_UNSPECIFIED;
    case 1:
    case "DATA_EXPORT_TYPE_PER_DAY":
      return DataExportType.DATA_EXPORT_TYPE_PER_DAY;
    case 2:
    case "DATA_EXPORT_TYPE_ALL":
      return DataExportType.DATA_EXPORT_TYPE_ALL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DataExportType.UNRECOGNIZED;
  }
}

export function dataExportTypeToJSON(object: DataExportType): string {
  switch (object) {
    case DataExportType.DATA_EXPORT_TYPE_UNSPECIFIED:
      return "DATA_EXPORT_TYPE_UNSPECIFIED";
    case DataExportType.DATA_EXPORT_TYPE_PER_DAY:
      return "DATA_EXPORT_TYPE_PER_DAY";
    case DataExportType.DATA_EXPORT_TYPE_ALL:
      return "DATA_EXPORT_TYPE_ALL";
    case DataExportType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 点配置 */
export enum PointType {
  /** POINT_TYPE_UNSPECIFIED - 未设置 */
  POINT_TYPE_UNSPECIFIED = 0,
  /** POINT_TYPE_CIRCLE - 圆点 */
  POINT_TYPE_CIRCLE = 1,
  /** POINT_TYPE_SQUARE - 方点 */
  POINT_TYPE_SQUARE = 2,
  /** POINT_TYPE_TRIANGLE - 三角 */
  POINT_TYPE_TRIANGLE = 3,
  /** POINT_TYPE_DIAMOND - 菱形 */
  POINT_TYPE_DIAMOND = 4,
  UNRECOGNIZED = -1,
}

export function pointTypeFromJSON(object: any): PointType {
  switch (object) {
    case 0:
    case "POINT_TYPE_UNSPECIFIED":
      return PointType.POINT_TYPE_UNSPECIFIED;
    case 1:
    case "POINT_TYPE_CIRCLE":
      return PointType.POINT_TYPE_CIRCLE;
    case 2:
    case "POINT_TYPE_SQUARE":
      return PointType.POINT_TYPE_SQUARE;
    case 3:
    case "POINT_TYPE_TRIANGLE":
      return PointType.POINT_TYPE_TRIANGLE;
    case 4:
    case "POINT_TYPE_DIAMOND":
      return PointType.POINT_TYPE_DIAMOND;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PointType.UNRECOGNIZED;
  }
}

export function pointTypeToJSON(object: PointType): string {
  switch (object) {
    case PointType.POINT_TYPE_UNSPECIFIED:
      return "POINT_TYPE_UNSPECIFIED";
    case PointType.POINT_TYPE_CIRCLE:
      return "POINT_TYPE_CIRCLE";
    case PointType.POINT_TYPE_SQUARE:
      return "POINT_TYPE_SQUARE";
    case PointType.POINT_TYPE_TRIANGLE:
      return "POINT_TYPE_TRIANGLE";
    case PointType.POINT_TYPE_DIAMOND:
      return "POINT_TYPE_DIAMOND";
    case PointType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 页面元件列表(按画面ID存的,画面ID从1开始) */
export interface PageWidgets {
  /** 元件列表 */
  widgets: Widget[];
  /** 属性绑定地址(key是元件ID，uint16，0表示页面本身) */
  widgetVariableReference: { [key: number]: WidgetVariableReference };
  /** 动作列表，key是元件ID，uint16，0表示页面本身事件 */
  widgetActionList: { [key: number]: ActionList };
}

export interface PageWidgets_WidgetVariableReferenceEntry {
  key: number;
  value: WidgetVariableReference | undefined;
}

export interface PageWidgets_WidgetActionListEntry {
  key: number;
  value: ActionList | undefined;
}

/** 元件变量引用 */
export interface WidgetVariableReference {
  /** KEY是u16，前8位表示元件属性ID，后8位表示数组属性的下标 */
  variable: { [key: number]: VariableReference };
}

export interface WidgetVariableReference_VariableEntry {
  key: number;
  value: VariableReference | undefined;
}

/** 状态属性 */
export interface StateProperty {
  /** 状态数量,为0表示没有状态之分 */
  stateCountU8: number;
  /** 状态值偏移(读取的地址值-偏移值=状态值),默认为0 */
  stateOffsetValueI16: number;
  /** 状态值等值匹配(数组下标表示状态) */
  stateMatchValueI16: number[];
  /** 状态值范围匹配，大于等于当前值，小于等于下个状态(数组下标表示状态) */
  stateRangeValueI16: number[];
  /** 状态值组合（用多个位组成一个状态） */
  stateCombineBit: boolean[];
  /** 自动切换状态 */
  autoSwitchState?:
    | AutoSwitchState
    | undefined;
  /** 如果配置了错误状态，则状态不存在时，跳转到错误状态，否则保留在当前状态，所以需要optional */
  errorStateU8?:
    | number
    | undefined;
  /** 找不到状态时的处理方式 */
  noStateWay: NoStateWay;
}

/** 自动切换状态参数 */
export interface AutoSwitchState {
  /** 是否自动切换状态(地址可绑定，由变量通知) */
  autoSwitch: boolean;
  /** 自动切换时间(单位100毫秒，地址可绑定，由变量通知) */
  autoSwitchTimeU16: number;
}

/** 确认参数 */
export interface ConfirmParam {
  /** 确认弹窗 */
  confirmPageIdU16: number;
  /** 确认弹窗等待时间(100毫秒的个数，为0则一直等待) */
  confirmWaitTimeU16: number;
  /** 确认超时是否执行，默念为否 */
  confirmTimeoutRun: boolean;
}

/** 元件的通用属性 */
export interface Widget {
  /** ID，同一页面内必须唯一 */
  idU16: number;
  /** 名称（设计器使用） */
  name: string;
  /** 备注（设计器使用） */
  memo: string;
  /** 是否可见（设计器使用） */
  visible: boolean;
  /** 是否锁定(包括了锁定位置和大小)（设计器使用） */
  pin: boolean;
  /** 是否只锁定比例（设计器使用） */
  lockScale: boolean;
  /** 读写类型（设计器使用） */
  rwMode: ReadWriteMode;
  /** 设计位置以及运行时初始位置(外观类不继承) */
  location:
    | Location
    | undefined;
  /** 大小(外观类不继承) */
  size:
    | Size
    | undefined;
  /** 控制属性 */
  enableControl?:
    | EnableControl
    | undefined;
  /** 状态相关属性 */
  stateProperty?:
    | StateProperty
    | undefined;
  /** 动作列表 */
  actionsU16: number[];
  /** 最小按压时间(100毫秒的个数，0表示不限制) */
  minPressTimeU16: number;
  /** 最小按压间隔(100毫秒的个数，0表示不限制) */
  minPressIntervalU16: number;
  /** 执行前确认弹窗 */
  confirmParam?:
    | ConfirmParam
    | undefined;
  /** 声音反馈(蜂鸣器) */
  soundFeedback: boolean;
  /** 图片（数组下标表示状态）(外观类有值使用，无值继承) */
  graphic: GraphicReference[];
  /** 样式（数组下标表示状态）(外观类有值使用，无值继承) */
  style: StyleProperties[];
  /** 样式同步标记(用位从高到低依次表示字体、文本颜色、背景颜色、位置、边框、阴影是否同步) */
  styleSyncU8: number;
  /** 文本（数组下标表示状态）(外观类有值使用，无值继承) */
  text: TextReference[];
  /** 其他属性以可选方式直接定义（只能继承，不会有值） */
  widget?:
    | { $case: "widgetClone"; value: WidgetClone }
    | { $case: "widgetGroup"; value: WidgetGroup }
    | { $case: "widgetText"; value: WidgetText }
    | { $case: "widgetGraphic"; value: WidgetGraphic }
    | { $case: "widgetBit"; value: WidgetBit }
    | { $case: "widgetWord"; value: WidgetWord }
    | { $case: "widgetNumber"; value: WidgetNumber }
    | { $case: "widgetString"; value: WidgetString }
    | { $case: "widgetButton"; value: WidgetButton }
    | { $case: "widgetMatrixButton"; value: WidgetMatrixButton }
    | { $case: "widgetTrendCurve"; value: WidgetTrendCurve }
    | { $case: "widgetScatterCurve"; value: WidgetScatterCurve }
    | { $case: "widgetBarCurve"; value: WidgetBarCurve }
    | { $case: "widgetXyCurve"; value: WidgetXYCurve }
    | { $case: "widgetCalendar"; value: WidgetCalendar }
    | { $case: "widgetRollerList"; value: WidgetRollerList }
    | { $case: "widgetDropList"; value: WidgetDropList }
    | { $case: "widgetMeter"; value: WidgetMeter }
    | { $case: "widgetRuler"; value: WidgetRuler }
    | { $case: "widgetRectAngle"; value: WidgetRectAngle }
    | { $case: "widgetLinear"; value: WidgetLinear }
    | { $case: "widgetArc"; value: WidgetArc }
    | undefined;
}

/** 克隆元件(除了标注为外观类的属性，其他都必须直接从克隆元件取值，不支持嵌套克隆) */
export interface WidgetClone {
  /** 克隆元件所在的页面ID，无值表示当前页面 */
  pageIdU16?:
    | number
    | undefined;
  /** 克隆元件的ID(要去识别页面id) */
  widgetIdU16: number;
}

/** 组合元件 */
export interface WidgetGroup {
  /** 下级元件 */
  subWidgets: Widget[];
  /** 是否禁止解散(用于用户自定义组合元件) */
  disableUngroup: boolean;
}

/** 静态文本 */
export interface WidgetText {
}

/** 静态图片 */
export interface WidgetGraphic {
}

/** 位元件(固定两个状态) */
export interface WidgetBit {
}

/** 字元件(状态数可设置) */
export interface WidgetWord {
}

/** 按钮（固定两个状态，分别是正常和按下,0-正常，1-按下） */
export interface WidgetButton {
  /** 按钮配置(如果配置了，表示有键盘功能) */
  buttonKeyCode?: ButtonKeyCode | undefined;
}

/** 矩阵按钮/键盘 */
export interface WidgetMatrixButton {
  /** 按钮 */
  buttons: MatrixButtonDefine[];
  /** 样式 */
  style?:
    | StyleProperties
    | undefined;
  /** 布局 */
  layout: MatrixButtonLayout;
}

/** 矩阵按钮配置 */
export interface MatrixButtonDefine {
  /** 按钮功能，下标是矩阵按钮的状态 */
  buttonFunctions: MatrixButtonFunction[];
  /** 执行动作 */
  actionsU16: number[];
  /** 样式(数组下标表示1按下和0抬起) */
  style: StyleProperties[];
  /** 图片(数组下标表示1按下和0抬起) */
  graphic: GraphicReference[];
  /** 矩阵按钮中的按钮宽度 */
  buttonWidthU16: number;
}

/** 矩阵按钮功能 */
export interface MatrixButtonFunction {
  func?:
    | { $case: "buttonKeyCode"; value: ButtonKeyCode }
    | //
    /** 输入按钮，Unicode码(UTF8) */
    { $case: "unicode"; value: string }
    | undefined;
  /** 文本 */
  text: TextReference | undefined;
}

/** 数字显示和输入 */
export interface WidgetNumber {
  /** 当前值 */
  currentValue:
    | NumberValue
    | undefined;
  /** 最大值 */
  maxValue:
    | NumberValue
    | undefined;
  /** 最小值 */
  minValue:
    | NumberValue
    | undefined;
  /** 是否支持输入 */
  supportInput: boolean;
  /** 键盘页面id */
  keyboardPageIdU16: number;
  /** 数据格式 */
  dataFormat: DataFormatType;
  /** 整数位数 */
  integerDigitsU8: number;
  /** 小数位数 */
  decimalDigitsU8: number;
  /** 是否隐藏前导零 */
  hideLeadingZero: boolean;
  /** 是否显示千位分隔符 */
  showThousandsSeparator: boolean;
  /** 是否隐藏小数未尾零 */
  hideTrailingZero: boolean;
  /** 是否显示正号 */
  showPlusSign: boolean;
  /** 小于最小值的颜色 */
  lessThanMinColor?:
    | ColorReference
    | undefined;
  /** 小于最小值的闪烁频率（几个100ms，最大255，用u8） */
  lessThanMinFlashTimeU8: number;
  /** 大于最大值的颜色 */
  greaterThanMaxColor?:
    | ColorReference
    | undefined;
  /** 大于最大值的闪烁频率（几个100ms，最大255，用u8） */
  greaterThanMaxFlashTimeU8: number;
  /** 密码模式 */
  passwordMode: boolean;
}

/** 字符串显示和输入 */
export interface WidgetString {
  /** 当前值 */
  currentValue: string;
  /** 是否支持输入 */
  supportInput: boolean;
  /** 键盘页面id */
  keyboardPageIdU16: number;
  /** 编码类型(服务端推送的数据如果带类型，以服务端为准，这里定义的是输入时的类型) */
  dataFormat: DataFormatType;
  /** 最大字符个数 */
  maxLengthU16: number;
  /** 最小字符个数 */
  minLengthU16: number;
  /** 是否显示滚动条 */
  showScrollbar: boolean;
  /** 密码模式 */
  passwordMode: boolean;
}

/** 下拉框 */
export interface ComboBox {
}

/** 曲线（游标数据区用Input事件） */
export interface WidgetCurve {
  /** 曲线类型 */
  curveType: CurveType;
  /** 数据序列数量(几条曲线的意思，从0开始的ID) */
  seriesCountU8: number;
  /** X轴显示方式配置 */
  xMode?:
    | //
    /** 采样点像素距离 */
    { $case: "pointDistanceU16"; value: number }
    | //
    /** 曲线上的一屏显示的点数 */
    { $case: "pointCountU16"; value: number }
    | //
    /** 一屏的时间范围（秒数） */
    { $case: "rangeSecondU16"; value: number }
    | //
    /** 一屏的时间范围（分钟） */
    { $case: "rangeMinuteU16"; value: number }
    | undefined;
  /** 绘制方向 */
  drawDirection: ScrollDirection;
  /** Y轴范围跟随哪个数据序列(设计端使用) */
  yRangeUseSeriesU8?: number | undefined;
  seriesConfig: WidgetCurve_SeriesConfig[];
  /** 是否启用游标 */
  enableCursor: boolean;
  /** 游标颜色 */
  cursorColor?:
    | ColorReference
    | undefined;
  /** 游标宽度 */
  cursorWidthU8: number;
  /** 是否显示实时时间 */
  useCurrentTime: boolean;
  /** 显示实时时间格式 */
  timeFormat: TimeFormatType;
  /** 显示实时日期格式 */
  dateFormat: DateFormatType;
  /** x 刻度值配置 */
  scaleValueConfigX?:
    | ScaleValueConfig
    | undefined;
  /** y 刻度值配置 */
  scaleValueConfigY?:
    | ScaleValueConfig
    | undefined;
  /** y2 刻度值配置 */
  scaleValueConfigY2?: ScaleValueConfig | undefined;
}

/** 数据序列配置 */
export interface WidgetCurve_SeriesConfig {
  /** 是否支持比例缩放 */
  supportScale: boolean;
  /** 是否显示渐变遮罩 */
  showFadeMask: boolean;
  /** 数据序列颜色 */
  color:
    | ColorReference
    | undefined;
  /** 点配置 */
  pointConfig?:
    | PointConfig
    | undefined;
  /** 线配置 */
  lineConfig?:
    | LineConfig
    | undefined;
  /** 柱状图配置 */
  barConfig?:
    | BarConfig
    | undefined;
  /** Y轴 */
  yMaxValue?: NumberValue | undefined;
  yMinValue?:
    | NumberValue
    | undefined;
  /** X轴 */
  xMinValue?: NumberValue | undefined;
  xMaxValue?: NumberValue | undefined;
}

/** 趋势图 */
export interface WidgetTrendCurve {
  /** 曲线配置 */
  curve:
    | WidgetCurve
    | undefined;
  /** 暂停恢复时间 max=60s */
  pauseResumeTimeU8: number;
  /** 曲线显示类型 （实时/历史） */
  isHistory: boolean;
  /** 是否使用相对时间 */
  useRelativeTime: boolean;
  /** 是否使用标签点 */
  useTimeLabel: boolean;
  /** 使用时间标签才可用 */
  timeFormat: TimeFormatType;
  /** 使用时间标签才可用 日期格式 */
  dateFormat: DateFormatType;
}

export interface WidgetXYCurve {
  /** 曲线配置 */
  curve: WidgetCurve | undefined;
}

export interface WidgetBarCurve {
  /** 曲线配置 */
  curve: WidgetCurve | undefined;
}

export interface WidgetScatterCurve {
  /** 曲线配置 */
  curve: WidgetCurve | undefined;
}

export interface WidgetCalendar {
  calendarType: WidgetCalendar_CalendarType;
  /** 星期名称背景颜色 */
  weekBgColor?:
    | ColorReference
    | undefined;
  /** 星期名称字体颜色 */
  weekFontColor?:
    | ColorReference
    | undefined;
  /** 今天背景颜色 */
  todayBgColor?:
    | ColorReference
    | undefined;
  /** 今天字体颜色 */
  todayFontColor?:
    | ColorReference
    | undefined;
  /**
   * 可无
   * 特殊日期高亮颜色
   */
  highlightColor?:
    | ColorReference
    | undefined;
  /** 特殊日期高亮字体颜色 */
  highlightFontColor?: ColorReference | undefined;
  highlightDateConfig: WidgetCalendar_DateConfig[];
}

export enum WidgetCalendar_CalendarType {
  /** CALENDAR_TYPE_UNSPECIFIED - 未设置 */
  CALENDAR_TYPE_UNSPECIFIED = 0,
  /** CALENDAR_TYPE_DROPDOWN - 下拉 */
  CALENDAR_TYPE_DROPDOWN = 1,
  /** CALENDAR_TYPE_ARROW - 箭头 */
  CALENDAR_TYPE_ARROW = 2,
  UNRECOGNIZED = -1,
}

export function widgetCalendar_CalendarTypeFromJSON(object: any): WidgetCalendar_CalendarType {
  switch (object) {
    case 0:
    case "CALENDAR_TYPE_UNSPECIFIED":
      return WidgetCalendar_CalendarType.CALENDAR_TYPE_UNSPECIFIED;
    case 1:
    case "CALENDAR_TYPE_DROPDOWN":
      return WidgetCalendar_CalendarType.CALENDAR_TYPE_DROPDOWN;
    case 2:
    case "CALENDAR_TYPE_ARROW":
      return WidgetCalendar_CalendarType.CALENDAR_TYPE_ARROW;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetCalendar_CalendarType.UNRECOGNIZED;
  }
}

export function widgetCalendar_CalendarTypeToJSON(object: WidgetCalendar_CalendarType): string {
  switch (object) {
    case WidgetCalendar_CalendarType.CALENDAR_TYPE_UNSPECIFIED:
      return "CALENDAR_TYPE_UNSPECIFIED";
    case WidgetCalendar_CalendarType.CALENDAR_TYPE_DROPDOWN:
      return "CALENDAR_TYPE_DROPDOWN";
    case WidgetCalendar_CalendarType.CALENDAR_TYPE_ARROW:
      return "CALENDAR_TYPE_ARROW";
    case WidgetCalendar_CalendarType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface WidgetCalendar_DateConfig {
  /** 年 */
  yearU16: number;
  /** 月 */
  monthU8: number;
  /** 日 */
  dayU8: number;
}

export interface WidgetOptionList {
  /** 数据来源 */
  dataResourceType: ListDataResourceType;
  /** 选项 [预留最后一个位错误值] */
  options: string[];
  /** 选项值 */
  optionsValue: string[];
  /** 选中颜色 */
  selectedColor?:
    | ColorReference
    | undefined;
  /** 行间距 */
  rowSpacingU8: number;
}

export interface WidgetRollerList {
  /** 滚动 */
  rollerListMode: WidgetRollerList_RollerListMode;
  /** 列表 */
  list:
    | WidgetOptionList
    | undefined;
  /** 显示数量 */
  viewCountU8: number;
  /** 默认索引 */
  defIndexU8: number;
}

export enum WidgetRollerList_RollerListMode {
  ROLLER_LIST_MODE_UNSPECIFIED = 0,
  ROLLER_LIST_MODE_INFINITE = 1,
  ROLLER_LIST_MODE_LIMITED = 2,
  UNRECOGNIZED = -1,
}

export function widgetRollerList_RollerListModeFromJSON(object: any): WidgetRollerList_RollerListMode {
  switch (object) {
    case 0:
    case "ROLLER_LIST_MODE_UNSPECIFIED":
      return WidgetRollerList_RollerListMode.ROLLER_LIST_MODE_UNSPECIFIED;
    case 1:
    case "ROLLER_LIST_MODE_INFINITE":
      return WidgetRollerList_RollerListMode.ROLLER_LIST_MODE_INFINITE;
    case 2:
    case "ROLLER_LIST_MODE_LIMITED":
      return WidgetRollerList_RollerListMode.ROLLER_LIST_MODE_LIMITED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetRollerList_RollerListMode.UNRECOGNIZED;
  }
}

export function widgetRollerList_RollerListModeToJSON(object: WidgetRollerList_RollerListMode): string {
  switch (object) {
    case WidgetRollerList_RollerListMode.ROLLER_LIST_MODE_UNSPECIFIED:
      return "ROLLER_LIST_MODE_UNSPECIFIED";
    case WidgetRollerList_RollerListMode.ROLLER_LIST_MODE_INFINITE:
      return "ROLLER_LIST_MODE_INFINITE";
    case WidgetRollerList_RollerListMode.ROLLER_LIST_MODE_LIMITED:
      return "ROLLER_LIST_MODE_LIMITED";
    case WidgetRollerList_RollerListMode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface WidgetDropList {
  /** 下拉 */
  direction: Direction;
  /** 列表 */
  list:
    | WidgetOptionList
    | undefined;
  /** 下拉内容框颜色 */
  listBgColor?:
    | ColorReference
    | undefined;
  /** 下拉图标 */
  dropListSymbol: SymbolType;
  /** 是否常开 */
  alwaysOpen: boolean;
}

export interface WidgetMeter {
  meterDirection: WidgetMeter_MeterDirection;
  /** 起始 */
  startU16: number;
  /** 结尾 */
  endU16: number;
  /**
   * 范围
   * 下限值
   */
  lowerLimitU16: number;
  /** 上限值 */
  upperLimitU16: number;
  /** 宽度 */
  limitWidthU8: number;
  /** 半径 */
  limitRadiusU8: number;
  /** 下限颜色 */
  lowerLimitColor:
    | ColorReference
    | undefined;
  /** 上限颜色 */
  upperLimitColor:
    | ColorReference
    | undefined;
  /** 中间颜色 */
  middleColor:
    | ColorReference
    | undefined;
  /** 刻度配置 (注意主刻度数量 为从左边开始的第多少个为一个主刻度) */
  scaleValueConfig:
    | ScaleValueConfig
    | undefined;
  /** 标签颜色 */
  scaleLabelColor:
    | ColorReference
    | undefined;
  /** 标签小数位数 */
  decimalPlacesU8: number;
  /** 标签半径 */
  labelRadiusU8: number;
  /** 指针配置 */
  pointerConfig:
    | LineConfig
    | undefined;
  /** 指针圆心配置 */
  pointerPointConfig?: PointConfig | undefined;
}

/** 方向 设计端使用 */
export enum WidgetMeter_MeterDirection {
  /** METER_DIRECTION_UNSPECIFIED - 未设置 */
  METER_DIRECTION_UNSPECIFIED = 0,
  /** METER_DIRECTION_CLOCKWISE - 顺时针 */
  METER_DIRECTION_CLOCKWISE = 1,
  /** METER_DIRECTION_COUNTERCLOCKWISE - 逆时针 */
  METER_DIRECTION_COUNTERCLOCKWISE = 2,
  UNRECOGNIZED = -1,
}

export function widgetMeter_MeterDirectionFromJSON(object: any): WidgetMeter_MeterDirection {
  switch (object) {
    case 0:
    case "METER_DIRECTION_UNSPECIFIED":
      return WidgetMeter_MeterDirection.METER_DIRECTION_UNSPECIFIED;
    case 1:
    case "METER_DIRECTION_CLOCKWISE":
      return WidgetMeter_MeterDirection.METER_DIRECTION_CLOCKWISE;
    case 2:
    case "METER_DIRECTION_COUNTERCLOCKWISE":
      return WidgetMeter_MeterDirection.METER_DIRECTION_COUNTERCLOCKWISE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetMeter_MeterDirection.UNRECOGNIZED;
  }
}

export function widgetMeter_MeterDirectionToJSON(object: WidgetMeter_MeterDirection): string {
  switch (object) {
    case WidgetMeter_MeterDirection.METER_DIRECTION_UNSPECIFIED:
      return "METER_DIRECTION_UNSPECIFIED";
    case WidgetMeter_MeterDirection.METER_DIRECTION_CLOCKWISE:
      return "METER_DIRECTION_CLOCKWISE";
    case WidgetMeter_MeterDirection.METER_DIRECTION_COUNTERCLOCKWISE:
      return "METER_DIRECTION_COUNTERCLOCKWISE";
    case WidgetMeter_MeterDirection.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 数据导出配置 */
export interface DataExportConfig {
  /** 数据文件类型 */
  fileType: DataFileType;
  /** 数据存储类型 */
  storageType: SaveLocation;
  /** 数据导出类型 */
  exportType: DataExportType;
}

/** 刻度值配置 */
export interface ScaleValueConfig {
  /** 刻度值是否显示 */
  show: boolean;
  scaleShowType: ScaleValueConfig_ScaleShowType;
  /** 刻度显示类型 */
  gridShowType: ScaleValueConfig_GridShowType;
  /** 主刻度配置 */
  scaleMain:
    | ScaleValueConfig_Scale
    | undefined;
  /** 次刻度配置 */
  scaleSec:
    | ScaleValueConfig_Scale
    | undefined;
  /** 刻度半径 */
  scaleRadiusU8: number;
  /** 最小值 */
  minValue?:
    | NumberValue
    | undefined;
  /** 最大值 */
  maxValue?:
    | NumberValue
    | undefined;
  /** 网格线配置 */
  gridLineConfig?: LineConfig | undefined;
}

export enum ScaleValueConfig_ScaleShowType {
  /** SCALE_SHOW_TYPE_UNSPECIFIED - 未设置 */
  SCALE_SHOW_TYPE_UNSPECIFIED = 0,
  /** SCALE_SHOW_TYPE_POINT - 显示点 */
  SCALE_SHOW_TYPE_POINT = 1,
  /** SCALE_SHOW_TYPE_TIME - 显示时间 */
  SCALE_SHOW_TYPE_TIME = 2,
  UNRECOGNIZED = -1,
}

export function scaleValueConfig_ScaleShowTypeFromJSON(object: any): ScaleValueConfig_ScaleShowType {
  switch (object) {
    case 0:
    case "SCALE_SHOW_TYPE_UNSPECIFIED":
      return ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_UNSPECIFIED;
    case 1:
    case "SCALE_SHOW_TYPE_POINT":
      return ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_POINT;
    case 2:
    case "SCALE_SHOW_TYPE_TIME":
      return ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_TIME;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ScaleValueConfig_ScaleShowType.UNRECOGNIZED;
  }
}

export function scaleValueConfig_ScaleShowTypeToJSON(object: ScaleValueConfig_ScaleShowType): string {
  switch (object) {
    case ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_UNSPECIFIED:
      return "SCALE_SHOW_TYPE_UNSPECIFIED";
    case ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_POINT:
      return "SCALE_SHOW_TYPE_POINT";
    case ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_TIME:
      return "SCALE_SHOW_TYPE_TIME";
    case ScaleValueConfig_ScaleShowType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 刻度值是否显示文字 */
export enum ScaleValueConfig_GridShowType {
  /** GRID_SHOW_TYPE_UNSPECIFIED - 未设置 */
  GRID_SHOW_TYPE_UNSPECIFIED = 0,
  /** GRID_SHOW_TYPE_MAIN - 主刻度 */
  GRID_SHOW_TYPE_MAIN = 1,
  /** GRID_SHOW_TYPE_SECOND - 次刻度 */
  GRID_SHOW_TYPE_SECOND = 2,
  UNRECOGNIZED = -1,
}

export function scaleValueConfig_GridShowTypeFromJSON(object: any): ScaleValueConfig_GridShowType {
  switch (object) {
    case 0:
    case "GRID_SHOW_TYPE_UNSPECIFIED":
      return ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_UNSPECIFIED;
    case 1:
    case "GRID_SHOW_TYPE_MAIN":
      return ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_MAIN;
    case 2:
    case "GRID_SHOW_TYPE_SECOND":
      return ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_SECOND;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ScaleValueConfig_GridShowType.UNRECOGNIZED;
  }
}

export function scaleValueConfig_GridShowTypeToJSON(object: ScaleValueConfig_GridShowType): string {
  switch (object) {
    case ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_UNSPECIFIED:
      return "GRID_SHOW_TYPE_UNSPECIFIED";
    case ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_MAIN:
      return "GRID_SHOW_TYPE_MAIN";
    case ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_SECOND:
      return "GRID_SHOW_TYPE_SECOND";
    case ScaleValueConfig_GridShowType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface ScaleValueConfig_Scale {
  /** 刻度数量 */
  scaleCountU8: number;
  /** 刻度值宽度 */
  scaleWidthU8: number;
  /** 刻度绘制长度 */
  scaleDrawLen: number;
  /** 刻度颜色 */
  color?: ColorReference | undefined;
}

export interface PointConfig {
  /** 点类型 */
  pointType: PointType;
  /** 点半径 */
  radiusU8: number;
  /** 点宽度 */
  widthU8: number;
  /** 点高度 */
  heightU8: number;
  /** 样式 */
  style?: StyleProperties | undefined;
}

export interface LineConfig {
  /** 线类型 */
  lineType: LineType;
  /** 线开头是否圆角 */
  roundStart: boolean;
  /** 线结尾是否圆角 */
  roundEnd: boolean;
  /** 虚线间隔 */
  dashGapU8: number;
  /** 虚线宽度 */
  dashWidthU8: number;
  /** 线宽度 */
  widthU8: number;
  /** 长度 */
  lengthU8: number;
  /** 线颜色 */
  color?: ColorReference | undefined;
}

export interface BarConfig {
  /** 开头是否圆角 */
  roundU8: number;
  /** 直径宽度 */
  widthU8: number;
  /** 样式 */
  style?: StyleProperties | undefined;
}

/** 刻度尺组件 */
export interface WidgetRuler {
  /** 刻度尺类型 */
  rulerType: WidgetRuler_RulerType;
  /** 刻度方向 */
  direction: WidgetRuler_RulerDirection;
  /** 基础刻度配置 */
  scaleConfig:
    | ScaleValueConfig
    | undefined;
  /** 刻度尺主线配置 */
  mainLine?:
    | LineConfig
    | undefined;
  /** 弧形刻度尺专用：半径 */
  radiusU16: number;
  /** 当前值（用于高亮显示） */
  currentValue?:
    | NumberValue
    | undefined;
  /** 自定义点列表 */
  points: Point[];
}

/** 刻度尺类型 */
export enum WidgetRuler_RulerType {
  /** RULER_TYPE_UNSPECIFIED - 未设置 */
  RULER_TYPE_UNSPECIFIED = 0,
  /** RULER_TYPE_HORIZONTAL - 水平直线刻度尺 */
  RULER_TYPE_HORIZONTAL = 1,
  /** RULER_TYPE_VERTICAL - 垂直直线刻度尺 */
  RULER_TYPE_VERTICAL = 2,
  /** RULER_TYPE_ARC - 弧形刻度尺 */
  RULER_TYPE_ARC = 3,
  /** RULER_TYPE_CIRCLE - 圆形刻度尺 */
  RULER_TYPE_CIRCLE = 4,
  UNRECOGNIZED = -1,
}

export function widgetRuler_RulerTypeFromJSON(object: any): WidgetRuler_RulerType {
  switch (object) {
    case 0:
    case "RULER_TYPE_UNSPECIFIED":
      return WidgetRuler_RulerType.RULER_TYPE_UNSPECIFIED;
    case 1:
    case "RULER_TYPE_HORIZONTAL":
      return WidgetRuler_RulerType.RULER_TYPE_HORIZONTAL;
    case 2:
    case "RULER_TYPE_VERTICAL":
      return WidgetRuler_RulerType.RULER_TYPE_VERTICAL;
    case 3:
    case "RULER_TYPE_ARC":
      return WidgetRuler_RulerType.RULER_TYPE_ARC;
    case 4:
    case "RULER_TYPE_CIRCLE":
      return WidgetRuler_RulerType.RULER_TYPE_CIRCLE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetRuler_RulerType.UNRECOGNIZED;
  }
}

export function widgetRuler_RulerTypeToJSON(object: WidgetRuler_RulerType): string {
  switch (object) {
    case WidgetRuler_RulerType.RULER_TYPE_UNSPECIFIED:
      return "RULER_TYPE_UNSPECIFIED";
    case WidgetRuler_RulerType.RULER_TYPE_HORIZONTAL:
      return "RULER_TYPE_HORIZONTAL";
    case WidgetRuler_RulerType.RULER_TYPE_VERTICAL:
      return "RULER_TYPE_VERTICAL";
    case WidgetRuler_RulerType.RULER_TYPE_ARC:
      return "RULER_TYPE_ARC";
    case WidgetRuler_RulerType.RULER_TYPE_CIRCLE:
      return "RULER_TYPE_CIRCLE";
    case WidgetRuler_RulerType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 刻度尺方向 */
export enum WidgetRuler_RulerDirection {
  /** RULER_DIRECTION_UNSPECIFIED - 未设置 */
  RULER_DIRECTION_UNSPECIFIED = 0,
  /** RULER_DIRECTION_TOP_LEFT - 刻度在上方/左侧 */
  RULER_DIRECTION_TOP_LEFT = 1,
  /** RULER_DIRECTION_BOTTOM_RIGHT - 刻度在下方/右侧 */
  RULER_DIRECTION_BOTTOM_RIGHT = 2,
  /** RULER_DIRECTION_BOTH - 刻度在两侧 */
  RULER_DIRECTION_BOTH = 3,
  UNRECOGNIZED = -1,
}

export function widgetRuler_RulerDirectionFromJSON(object: any): WidgetRuler_RulerDirection {
  switch (object) {
    case 0:
    case "RULER_DIRECTION_UNSPECIFIED":
      return WidgetRuler_RulerDirection.RULER_DIRECTION_UNSPECIFIED;
    case 1:
    case "RULER_DIRECTION_TOP_LEFT":
      return WidgetRuler_RulerDirection.RULER_DIRECTION_TOP_LEFT;
    case 2:
    case "RULER_DIRECTION_BOTTOM_RIGHT":
      return WidgetRuler_RulerDirection.RULER_DIRECTION_BOTTOM_RIGHT;
    case 3:
    case "RULER_DIRECTION_BOTH":
      return WidgetRuler_RulerDirection.RULER_DIRECTION_BOTH;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetRuler_RulerDirection.UNRECOGNIZED;
  }
}

export function widgetRuler_RulerDirectionToJSON(object: WidgetRuler_RulerDirection): string {
  switch (object) {
    case WidgetRuler_RulerDirection.RULER_DIRECTION_UNSPECIFIED:
      return "RULER_DIRECTION_UNSPECIFIED";
    case WidgetRuler_RulerDirection.RULER_DIRECTION_TOP_LEFT:
      return "RULER_DIRECTION_TOP_LEFT";
    case WidgetRuler_RulerDirection.RULER_DIRECTION_BOTTOM_RIGHT:
      return "RULER_DIRECTION_BOTTOM_RIGHT";
    case WidgetRuler_RulerDirection.RULER_DIRECTION_BOTH:
      return "RULER_DIRECTION_BOTH";
    case WidgetRuler_RulerDirection.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface WidgetRectAngle {
  chamferType: WidgetRectAngle_ChamferType;
  /** 倒角半径 */
  chamferRadiusU8: number;
  /** 配置 */
  line?: LineConfig | undefined;
}

/** 倒角 */
export enum WidgetRectAngle_ChamferType {
  /** CHAMFER_TYPE_UNSPECIFIED - 未设置 */
  CHAMFER_TYPE_UNSPECIFIED = 0,
  /** CHAMFER_TYPE_ROUND - 圆角 */
  CHAMFER_TYPE_ROUND = 1,
  /** CHAMFER_TYPE_SQUARE - 直角 */
  CHAMFER_TYPE_SQUARE = 2,
  UNRECOGNIZED = -1,
}

export function widgetRectAngle_ChamferTypeFromJSON(object: any): WidgetRectAngle_ChamferType {
  switch (object) {
    case 0:
    case "CHAMFER_TYPE_UNSPECIFIED":
      return WidgetRectAngle_ChamferType.CHAMFER_TYPE_UNSPECIFIED;
    case 1:
    case "CHAMFER_TYPE_ROUND":
      return WidgetRectAngle_ChamferType.CHAMFER_TYPE_ROUND;
    case 2:
    case "CHAMFER_TYPE_SQUARE":
      return WidgetRectAngle_ChamferType.CHAMFER_TYPE_SQUARE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetRectAngle_ChamferType.UNRECOGNIZED;
  }
}

export function widgetRectAngle_ChamferTypeToJSON(object: WidgetRectAngle_ChamferType): string {
  switch (object) {
    case WidgetRectAngle_ChamferType.CHAMFER_TYPE_UNSPECIFIED:
      return "CHAMFER_TYPE_UNSPECIFIED";
    case WidgetRectAngle_ChamferType.CHAMFER_TYPE_ROUND:
      return "CHAMFER_TYPE_ROUND";
    case WidgetRectAngle_ChamferType.CHAMFER_TYPE_SQUARE:
      return "CHAMFER_TYPE_SQUARE";
    case WidgetRectAngle_ChamferType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 直线构成形状 */
export interface WidgetLinear {
  linearType: WidgetLinear_LinearType;
  /** 点列表 */
  points: Point[];
  /** 线配置 */
  mainLine?: LineConfig | undefined;
}

export enum WidgetLinear_LinearType {
  /** LINEAR_TYPE_UNSPECIFIED - 未设置 */
  LINEAR_TYPE_UNSPECIFIED = 0,
  /** LINEAR_TYPE_LINE - 直线 */
  LINEAR_TYPE_LINE = 1,
  /** LINEAR_TYPE_POLYLINE - 折线 */
  LINEAR_TYPE_POLYLINE = 2,
  /** LINEAR_TYPE_POLYGON - 多边形 */
  LINEAR_TYPE_POLYGON = 4,
  UNRECOGNIZED = -1,
}

export function widgetLinear_LinearTypeFromJSON(object: any): WidgetLinear_LinearType {
  switch (object) {
    case 0:
    case "LINEAR_TYPE_UNSPECIFIED":
      return WidgetLinear_LinearType.LINEAR_TYPE_UNSPECIFIED;
    case 1:
    case "LINEAR_TYPE_LINE":
      return WidgetLinear_LinearType.LINEAR_TYPE_LINE;
    case 2:
    case "LINEAR_TYPE_POLYLINE":
      return WidgetLinear_LinearType.LINEAR_TYPE_POLYLINE;
    case 4:
    case "LINEAR_TYPE_POLYGON":
      return WidgetLinear_LinearType.LINEAR_TYPE_POLYGON;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetLinear_LinearType.UNRECOGNIZED;
  }
}

export function widgetLinear_LinearTypeToJSON(object: WidgetLinear_LinearType): string {
  switch (object) {
    case WidgetLinear_LinearType.LINEAR_TYPE_UNSPECIFIED:
      return "LINEAR_TYPE_UNSPECIFIED";
    case WidgetLinear_LinearType.LINEAR_TYPE_LINE:
      return "LINEAR_TYPE_LINE";
    case WidgetLinear_LinearType.LINEAR_TYPE_POLYLINE:
      return "LINEAR_TYPE_POLYLINE";
    case WidgetLinear_LinearType.LINEAR_TYPE_POLYGON:
      return "LINEAR_TYPE_POLYGON";
    case WidgetLinear_LinearType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 弧线构成形状 */
export interface WidgetArc {
  arcType: WidgetArc_ArcType;
  /** 中心点 */
  center?:
    | Point
    | undefined;
  /** 半径 */
  radiusU16: number;
  /** 起始角度 (0-360度) */
  startAngleU16: number;
  /** 结束角度 (0-360度) */
  endAngleU16: number;
}

export enum WidgetArc_ArcType {
  /** ARC_TYPE_UNSPECIFIED - 未设置 */
  ARC_TYPE_UNSPECIFIED = 0,
  /** ARC_TYPE_ARC - 弧形 */
  ARC_TYPE_ARC = 1,
  /** ARC_TYPE_SECTOR - 扇形 */
  ARC_TYPE_SECTOR = 2,
  /** ARC_TYPE_RING - 扇环 */
  ARC_TYPE_RING = 3,
  UNRECOGNIZED = -1,
}

export function widgetArc_ArcTypeFromJSON(object: any): WidgetArc_ArcType {
  switch (object) {
    case 0:
    case "ARC_TYPE_UNSPECIFIED":
      return WidgetArc_ArcType.ARC_TYPE_UNSPECIFIED;
    case 1:
    case "ARC_TYPE_ARC":
      return WidgetArc_ArcType.ARC_TYPE_ARC;
    case 2:
    case "ARC_TYPE_SECTOR":
      return WidgetArc_ArcType.ARC_TYPE_SECTOR;
    case 3:
    case "ARC_TYPE_RING":
      return WidgetArc_ArcType.ARC_TYPE_RING;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetArc_ArcType.UNRECOGNIZED;
  }
}

export function widgetArc_ArcTypeToJSON(object: WidgetArc_ArcType): string {
  switch (object) {
    case WidgetArc_ArcType.ARC_TYPE_UNSPECIFIED:
      return "ARC_TYPE_UNSPECIFIED";
    case WidgetArc_ArcType.ARC_TYPE_ARC:
      return "ARC_TYPE_ARC";
    case WidgetArc_ArcType.ARC_TYPE_SECTOR:
      return "ARC_TYPE_SECTOR";
    case WidgetArc_ArcType.ARC_TYPE_RING:
      return "ARC_TYPE_RING";
    case WidgetArc_ArcType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

function createBasePageWidgets(): PageWidgets {
  return { widgets: [], widgetVariableReference: {}, widgetActionList: {} };
}

export const PageWidgets: MessageFns<PageWidgets> = {
  encode(message: PageWidgets, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.widgets) {
      Widget.encode(v!, writer.uint32(10).fork()).join();
    }
    Object.entries(message.widgetVariableReference).forEach(([key, value]) => {
      PageWidgets_WidgetVariableReferenceEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.widgetActionList).forEach(([key, value]) => {
      PageWidgets_WidgetActionListEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PageWidgets {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePageWidgets();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.widgets.push(Widget.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = PageWidgets_WidgetVariableReferenceEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.widgetVariableReference[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = PageWidgets_WidgetActionListEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.widgetActionList[entry3.key] = entry3.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PageWidgets {
    return {
      widgets: globalThis.Array.isArray(object?.widgets) ? object.widgets.map((e: any) => Widget.fromJSON(e)) : [],
      widgetVariableReference: isObject(object.widgetVariableReference)
        ? Object.entries(object.widgetVariableReference).reduce<{ [key: number]: WidgetVariableReference }>(
          (acc, [key, value]) => {
            acc[globalThis.Number(key)] = WidgetVariableReference.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
      widgetActionList: isObject(object.widgetActionList)
        ? Object.entries(object.widgetActionList).reduce<{ [key: number]: ActionList }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = ActionList.fromJSON(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: PageWidgets): unknown {
    const obj: any = {};
    if (message.widgets?.length) {
      obj.widgets = message.widgets.map((e) => Widget.toJSON(e));
    }
    if (message.widgetVariableReference) {
      const entries = Object.entries(message.widgetVariableReference);
      if (entries.length > 0) {
        obj.widgetVariableReference = {};
        entries.forEach(([k, v]) => {
          obj.widgetVariableReference[k] = WidgetVariableReference.toJSON(v);
        });
      }
    }
    if (message.widgetActionList) {
      const entries = Object.entries(message.widgetActionList);
      if (entries.length > 0) {
        obj.widgetActionList = {};
        entries.forEach(([k, v]) => {
          obj.widgetActionList[k] = ActionList.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PageWidgets>, I>>(base?: I): PageWidgets {
    return PageWidgets.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageWidgets>, I>>(object: I): PageWidgets {
    const message = createBasePageWidgets();
    message.widgets = object.widgets?.map((e) => Widget.fromPartial(e)) || [];
    message.widgetVariableReference = Object.entries(object.widgetVariableReference ?? {}).reduce<
      { [key: number]: WidgetVariableReference }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = WidgetVariableReference.fromPartial(value);
      }
      return acc;
    }, {});
    message.widgetActionList = Object.entries(object.widgetActionList ?? {}).reduce<{ [key: number]: ActionList }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = ActionList.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBasePageWidgets_WidgetVariableReferenceEntry(): PageWidgets_WidgetVariableReferenceEntry {
  return { key: 0, value: undefined };
}

export const PageWidgets_WidgetVariableReferenceEntry: MessageFns<PageWidgets_WidgetVariableReferenceEntry> = {
  encode(message: PageWidgets_WidgetVariableReferenceEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      WidgetVariableReference.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PageWidgets_WidgetVariableReferenceEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePageWidgets_WidgetVariableReferenceEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = WidgetVariableReference.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PageWidgets_WidgetVariableReferenceEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? WidgetVariableReference.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: PageWidgets_WidgetVariableReferenceEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = WidgetVariableReference.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PageWidgets_WidgetVariableReferenceEntry>, I>>(
    base?: I,
  ): PageWidgets_WidgetVariableReferenceEntry {
    return PageWidgets_WidgetVariableReferenceEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageWidgets_WidgetVariableReferenceEntry>, I>>(
    object: I,
  ): PageWidgets_WidgetVariableReferenceEntry {
    const message = createBasePageWidgets_WidgetVariableReferenceEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? WidgetVariableReference.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBasePageWidgets_WidgetActionListEntry(): PageWidgets_WidgetActionListEntry {
  return { key: 0, value: undefined };
}

export const PageWidgets_WidgetActionListEntry: MessageFns<PageWidgets_WidgetActionListEntry> = {
  encode(message: PageWidgets_WidgetActionListEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      ActionList.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PageWidgets_WidgetActionListEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePageWidgets_WidgetActionListEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = ActionList.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PageWidgets_WidgetActionListEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? ActionList.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: PageWidgets_WidgetActionListEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = ActionList.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PageWidgets_WidgetActionListEntry>, I>>(
    base?: I,
  ): PageWidgets_WidgetActionListEntry {
    return PageWidgets_WidgetActionListEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageWidgets_WidgetActionListEntry>, I>>(
    object: I,
  ): PageWidgets_WidgetActionListEntry {
    const message = createBasePageWidgets_WidgetActionListEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? ActionList.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseWidgetVariableReference(): WidgetVariableReference {
  return { variable: {} };
}

export const WidgetVariableReference: MessageFns<WidgetVariableReference> = {
  encode(message: WidgetVariableReference, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.variable).forEach(([key, value]) => {
      WidgetVariableReference_VariableEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetVariableReference {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetVariableReference();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = WidgetVariableReference_VariableEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.variable[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetVariableReference {
    return {
      variable: isObject(object.variable)
        ? Object.entries(object.variable).reduce<{ [key: number]: VariableReference }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = VariableReference.fromJSON(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: WidgetVariableReference): unknown {
    const obj: any = {};
    if (message.variable) {
      const entries = Object.entries(message.variable);
      if (entries.length > 0) {
        obj.variable = {};
        entries.forEach(([k, v]) => {
          obj.variable[k] = VariableReference.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetVariableReference>, I>>(base?: I): WidgetVariableReference {
    return WidgetVariableReference.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetVariableReference>, I>>(object: I): WidgetVariableReference {
    const message = createBaseWidgetVariableReference();
    message.variable = Object.entries(object.variable ?? {}).reduce<{ [key: number]: VariableReference }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = VariableReference.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseWidgetVariableReference_VariableEntry(): WidgetVariableReference_VariableEntry {
  return { key: 0, value: undefined };
}

export const WidgetVariableReference_VariableEntry: MessageFns<WidgetVariableReference_VariableEntry> = {
  encode(message: WidgetVariableReference_VariableEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== undefined) {
      VariableReference.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetVariableReference_VariableEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetVariableReference_VariableEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = VariableReference.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetVariableReference_VariableEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? VariableReference.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: WidgetVariableReference_VariableEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = VariableReference.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetVariableReference_VariableEntry>, I>>(
    base?: I,
  ): WidgetVariableReference_VariableEntry {
    return WidgetVariableReference_VariableEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetVariableReference_VariableEntry>, I>>(
    object: I,
  ): WidgetVariableReference_VariableEntry {
    const message = createBaseWidgetVariableReference_VariableEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? VariableReference.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseStateProperty(): StateProperty {
  return {
    stateCountU8: 0,
    stateOffsetValueI16: 0,
    stateMatchValueI16: [],
    stateRangeValueI16: [],
    stateCombineBit: [],
    autoSwitchState: undefined,
    errorStateU8: undefined,
    noStateWay: 0,
  };
}

export const StateProperty: MessageFns<StateProperty> = {
  encode(message: StateProperty, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.stateCountU8 !== 0) {
      writer.uint32(8).uint32(message.stateCountU8);
    }
    if (message.stateOffsetValueI16 !== 0) {
      writer.uint32(16).int32(message.stateOffsetValueI16);
    }
    writer.uint32(26).fork();
    for (const v of message.stateMatchValueI16) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.stateRangeValueI16) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(42).fork();
    for (const v of message.stateCombineBit) {
      writer.bool(v);
    }
    writer.join();
    if (message.autoSwitchState !== undefined) {
      AutoSwitchState.encode(message.autoSwitchState, writer.uint32(50).fork()).join();
    }
    if (message.errorStateU8 !== undefined) {
      writer.uint32(56).uint32(message.errorStateU8);
    }
    if (message.noStateWay !== 0) {
      writer.uint32(64).int32(message.noStateWay);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StateProperty {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStateProperty();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.stateCountU8 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.stateOffsetValueI16 = reader.int32();
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.stateMatchValueI16.push(reader.int32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.stateMatchValueI16.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 32) {
            message.stateRangeValueI16.push(reader.int32());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.stateRangeValueI16.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag === 40) {
            message.stateCombineBit.push(reader.bool());

            continue;
          }

          if (tag === 42) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.stateCombineBit.push(reader.bool());
            }

            continue;
          }

          break;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.autoSwitchState = AutoSwitchState.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.errorStateU8 = reader.uint32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.noStateWay = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StateProperty {
    return {
      stateCountU8: isSet(object.stateCountU8) ? globalThis.Number(object.stateCountU8) : 0,
      stateOffsetValueI16: isSet(object.stateOffsetValueI16) ? globalThis.Number(object.stateOffsetValueI16) : 0,
      stateMatchValueI16: globalThis.Array.isArray(object?.stateMatchValueI16)
        ? object.stateMatchValueI16.map((e: any) => globalThis.Number(e))
        : [],
      stateRangeValueI16: globalThis.Array.isArray(object?.stateRangeValueI16)
        ? object.stateRangeValueI16.map((e: any) => globalThis.Number(e))
        : [],
      stateCombineBit: globalThis.Array.isArray(object?.stateCombineBit)
        ? object.stateCombineBit.map((e: any) => globalThis.Boolean(e))
        : [],
      autoSwitchState: isSet(object.autoSwitchState) ? AutoSwitchState.fromJSON(object.autoSwitchState) : undefined,
      errorStateU8: isSet(object.errorStateU8) ? globalThis.Number(object.errorStateU8) : undefined,
      noStateWay: isSet(object.noStateWay) ? noStateWayFromJSON(object.noStateWay) : 0,
    };
  },

  toJSON(message: StateProperty): unknown {
    const obj: any = {};
    if (message.stateCountU8 !== 0) {
      obj.stateCountU8 = Math.round(message.stateCountU8);
    }
    if (message.stateOffsetValueI16 !== 0) {
      obj.stateOffsetValueI16 = Math.round(message.stateOffsetValueI16);
    }
    if (message.stateMatchValueI16?.length) {
      obj.stateMatchValueI16 = message.stateMatchValueI16.map((e) => Math.round(e));
    }
    if (message.stateRangeValueI16?.length) {
      obj.stateRangeValueI16 = message.stateRangeValueI16.map((e) => Math.round(e));
    }
    if (message.stateCombineBit?.length) {
      obj.stateCombineBit = message.stateCombineBit;
    }
    if (message.autoSwitchState !== undefined) {
      obj.autoSwitchState = AutoSwitchState.toJSON(message.autoSwitchState);
    }
    if (message.errorStateU8 !== undefined) {
      obj.errorStateU8 = Math.round(message.errorStateU8);
    }
    if (message.noStateWay !== 0) {
      obj.noStateWay = noStateWayToJSON(message.noStateWay);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StateProperty>, I>>(base?: I): StateProperty {
    return StateProperty.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StateProperty>, I>>(object: I): StateProperty {
    const message = createBaseStateProperty();
    message.stateCountU8 = object.stateCountU8 ?? 0;
    message.stateOffsetValueI16 = object.stateOffsetValueI16 ?? 0;
    message.stateMatchValueI16 = object.stateMatchValueI16?.map((e) => e) || [];
    message.stateRangeValueI16 = object.stateRangeValueI16?.map((e) => e) || [];
    message.stateCombineBit = object.stateCombineBit?.map((e) => e) || [];
    message.autoSwitchState = (object.autoSwitchState !== undefined && object.autoSwitchState !== null)
      ? AutoSwitchState.fromPartial(object.autoSwitchState)
      : undefined;
    message.errorStateU8 = object.errorStateU8 ?? undefined;
    message.noStateWay = object.noStateWay ?? 0;
    return message;
  },
};

function createBaseAutoSwitchState(): AutoSwitchState {
  return { autoSwitch: false, autoSwitchTimeU16: 0 };
}

export const AutoSwitchState: MessageFns<AutoSwitchState> = {
  encode(message: AutoSwitchState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.autoSwitch !== false) {
      writer.uint32(8).bool(message.autoSwitch);
    }
    if (message.autoSwitchTimeU16 !== 0) {
      writer.uint32(16).uint32(message.autoSwitchTimeU16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AutoSwitchState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAutoSwitchState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.autoSwitch = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.autoSwitchTimeU16 = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AutoSwitchState {
    return {
      autoSwitch: isSet(object.autoSwitch) ? globalThis.Boolean(object.autoSwitch) : false,
      autoSwitchTimeU16: isSet(object.autoSwitchTimeU16) ? globalThis.Number(object.autoSwitchTimeU16) : 0,
    };
  },

  toJSON(message: AutoSwitchState): unknown {
    const obj: any = {};
    if (message.autoSwitch !== false) {
      obj.autoSwitch = message.autoSwitch;
    }
    if (message.autoSwitchTimeU16 !== 0) {
      obj.autoSwitchTimeU16 = Math.round(message.autoSwitchTimeU16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AutoSwitchState>, I>>(base?: I): AutoSwitchState {
    return AutoSwitchState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AutoSwitchState>, I>>(object: I): AutoSwitchState {
    const message = createBaseAutoSwitchState();
    message.autoSwitch = object.autoSwitch ?? false;
    message.autoSwitchTimeU16 = object.autoSwitchTimeU16 ?? 0;
    return message;
  },
};

function createBaseConfirmParam(): ConfirmParam {
  return { confirmPageIdU16: 0, confirmWaitTimeU16: 0, confirmTimeoutRun: false };
}

export const ConfirmParam: MessageFns<ConfirmParam> = {
  encode(message: ConfirmParam, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.confirmPageIdU16 !== 0) {
      writer.uint32(8).uint32(message.confirmPageIdU16);
    }
    if (message.confirmWaitTimeU16 !== 0) {
      writer.uint32(16).uint32(message.confirmWaitTimeU16);
    }
    if (message.confirmTimeoutRun !== false) {
      writer.uint32(24).bool(message.confirmTimeoutRun);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConfirmParam {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfirmParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.confirmPageIdU16 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.confirmWaitTimeU16 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.confirmTimeoutRun = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ConfirmParam {
    return {
      confirmPageIdU16: isSet(object.confirmPageIdU16) ? globalThis.Number(object.confirmPageIdU16) : 0,
      confirmWaitTimeU16: isSet(object.confirmWaitTimeU16) ? globalThis.Number(object.confirmWaitTimeU16) : 0,
      confirmTimeoutRun: isSet(object.confirmTimeoutRun) ? globalThis.Boolean(object.confirmTimeoutRun) : false,
    };
  },

  toJSON(message: ConfirmParam): unknown {
    const obj: any = {};
    if (message.confirmPageIdU16 !== 0) {
      obj.confirmPageIdU16 = Math.round(message.confirmPageIdU16);
    }
    if (message.confirmWaitTimeU16 !== 0) {
      obj.confirmWaitTimeU16 = Math.round(message.confirmWaitTimeU16);
    }
    if (message.confirmTimeoutRun !== false) {
      obj.confirmTimeoutRun = message.confirmTimeoutRun;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConfirmParam>, I>>(base?: I): ConfirmParam {
    return ConfirmParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfirmParam>, I>>(object: I): ConfirmParam {
    const message = createBaseConfirmParam();
    message.confirmPageIdU16 = object.confirmPageIdU16 ?? 0;
    message.confirmWaitTimeU16 = object.confirmWaitTimeU16 ?? 0;
    message.confirmTimeoutRun = object.confirmTimeoutRun ?? false;
    return message;
  },
};

function createBaseWidget(): Widget {
  return {
    idU16: 0,
    name: "",
    memo: "",
    visible: false,
    pin: false,
    lockScale: false,
    rwMode: 0,
    location: undefined,
    size: undefined,
    enableControl: undefined,
    stateProperty: undefined,
    actionsU16: [],
    minPressTimeU16: 0,
    minPressIntervalU16: 0,
    confirmParam: undefined,
    soundFeedback: false,
    graphic: [],
    style: [],
    styleSyncU8: 0,
    text: [],
    widget: undefined,
  };
}

export const Widget: MessageFns<Widget> = {
  encode(message: Widget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.idU16 !== 0) {
      writer.uint32(8).uint32(message.idU16);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.memo !== "") {
      writer.uint32(26).string(message.memo);
    }
    if (message.visible !== false) {
      writer.uint32(32).bool(message.visible);
    }
    if (message.pin !== false) {
      writer.uint32(40).bool(message.pin);
    }
    if (message.lockScale !== false) {
      writer.uint32(48).bool(message.lockScale);
    }
    if (message.rwMode !== 0) {
      writer.uint32(56).int32(message.rwMode);
    }
    if (message.location !== undefined) {
      Location.encode(message.location, writer.uint32(66).fork()).join();
    }
    if (message.size !== undefined) {
      Size.encode(message.size, writer.uint32(74).fork()).join();
    }
    if (message.enableControl !== undefined) {
      EnableControl.encode(message.enableControl, writer.uint32(82).fork()).join();
    }
    if (message.stateProperty !== undefined) {
      StateProperty.encode(message.stateProperty, writer.uint32(90).fork()).join();
    }
    writer.uint32(98).fork();
    for (const v of message.actionsU16) {
      writer.uint32(v);
    }
    writer.join();
    if (message.minPressTimeU16 !== 0) {
      writer.uint32(104).uint32(message.minPressTimeU16);
    }
    if (message.minPressIntervalU16 !== 0) {
      writer.uint32(112).uint32(message.minPressIntervalU16);
    }
    if (message.confirmParam !== undefined) {
      ConfirmParam.encode(message.confirmParam, writer.uint32(122).fork()).join();
    }
    if (message.soundFeedback !== false) {
      writer.uint32(128).bool(message.soundFeedback);
    }
    for (const v of message.graphic) {
      GraphicReference.encode(v!, writer.uint32(138).fork()).join();
    }
    for (const v of message.style) {
      StyleProperties.encode(v!, writer.uint32(146).fork()).join();
    }
    if (message.styleSyncU8 !== 0) {
      writer.uint32(152).uint32(message.styleSyncU8);
    }
    for (const v of message.text) {
      TextReference.encode(v!, writer.uint32(162).fork()).join();
    }
    switch (message.widget?.$case) {
      case "widgetClone":
        WidgetClone.encode(message.widget.value, writer.uint32(170).fork()).join();
        break;
      case "widgetGroup":
        WidgetGroup.encode(message.widget.value, writer.uint32(178).fork()).join();
        break;
      case "widgetText":
        WidgetText.encode(message.widget.value, writer.uint32(186).fork()).join();
        break;
      case "widgetGraphic":
        WidgetGraphic.encode(message.widget.value, writer.uint32(194).fork()).join();
        break;
      case "widgetBit":
        WidgetBit.encode(message.widget.value, writer.uint32(202).fork()).join();
        break;
      case "widgetWord":
        WidgetWord.encode(message.widget.value, writer.uint32(210).fork()).join();
        break;
      case "widgetNumber":
        WidgetNumber.encode(message.widget.value, writer.uint32(218).fork()).join();
        break;
      case "widgetString":
        WidgetString.encode(message.widget.value, writer.uint32(226).fork()).join();
        break;
      case "widgetButton":
        WidgetButton.encode(message.widget.value, writer.uint32(234).fork()).join();
        break;
      case "widgetMatrixButton":
        WidgetMatrixButton.encode(message.widget.value, writer.uint32(242).fork()).join();
        break;
      case "widgetTrendCurve":
        WidgetTrendCurve.encode(message.widget.value, writer.uint32(250).fork()).join();
        break;
      case "widgetScatterCurve":
        WidgetScatterCurve.encode(message.widget.value, writer.uint32(258).fork()).join();
        break;
      case "widgetBarCurve":
        WidgetBarCurve.encode(message.widget.value, writer.uint32(266).fork()).join();
        break;
      case "widgetXyCurve":
        WidgetXYCurve.encode(message.widget.value, writer.uint32(274).fork()).join();
        break;
      case "widgetCalendar":
        WidgetCalendar.encode(message.widget.value, writer.uint32(282).fork()).join();
        break;
      case "widgetRollerList":
        WidgetRollerList.encode(message.widget.value, writer.uint32(290).fork()).join();
        break;
      case "widgetDropList":
        WidgetDropList.encode(message.widget.value, writer.uint32(298).fork()).join();
        break;
      case "widgetMeter":
        WidgetMeter.encode(message.widget.value, writer.uint32(306).fork()).join();
        break;
      case "widgetRuler":
        WidgetRuler.encode(message.widget.value, writer.uint32(314).fork()).join();
        break;
      case "widgetRectAngle":
        WidgetRectAngle.encode(message.widget.value, writer.uint32(322).fork()).join();
        break;
      case "widgetLinear":
        WidgetLinear.encode(message.widget.value, writer.uint32(330).fork()).join();
        break;
      case "widgetArc":
        WidgetArc.encode(message.widget.value, writer.uint32(338).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Widget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.idU16 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.visible = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.pin = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.lockScale = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.rwMode = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.location = Location.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.size = Size.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.enableControl = EnableControl.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.stateProperty = StateProperty.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag === 96) {
            message.actionsU16.push(reader.uint32());

            continue;
          }

          if (tag === 98) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.actionsU16.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.minPressTimeU16 = reader.uint32();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.minPressIntervalU16 = reader.uint32();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.confirmParam = ConfirmParam.decode(reader, reader.uint32());
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.soundFeedback = reader.bool();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.graphic.push(GraphicReference.decode(reader, reader.uint32()));
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.style.push(StyleProperties.decode(reader, reader.uint32()));
          continue;
        }
        case 19: {
          if (tag !== 152) {
            break;
          }

          message.styleSyncU8 = reader.uint32();
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.text.push(TextReference.decode(reader, reader.uint32()));
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.widget = { $case: "widgetClone", value: WidgetClone.decode(reader, reader.uint32()) };
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.widget = { $case: "widgetGroup", value: WidgetGroup.decode(reader, reader.uint32()) };
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.widget = { $case: "widgetText", value: WidgetText.decode(reader, reader.uint32()) };
          continue;
        }
        case 24: {
          if (tag !== 194) {
            break;
          }

          message.widget = { $case: "widgetGraphic", value: WidgetGraphic.decode(reader, reader.uint32()) };
          continue;
        }
        case 25: {
          if (tag !== 202) {
            break;
          }

          message.widget = { $case: "widgetBit", value: WidgetBit.decode(reader, reader.uint32()) };
          continue;
        }
        case 26: {
          if (tag !== 210) {
            break;
          }

          message.widget = { $case: "widgetWord", value: WidgetWord.decode(reader, reader.uint32()) };
          continue;
        }
        case 27: {
          if (tag !== 218) {
            break;
          }

          message.widget = { $case: "widgetNumber", value: WidgetNumber.decode(reader, reader.uint32()) };
          continue;
        }
        case 28: {
          if (tag !== 226) {
            break;
          }

          message.widget = { $case: "widgetString", value: WidgetString.decode(reader, reader.uint32()) };
          continue;
        }
        case 29: {
          if (tag !== 234) {
            break;
          }

          message.widget = { $case: "widgetButton", value: WidgetButton.decode(reader, reader.uint32()) };
          continue;
        }
        case 30: {
          if (tag !== 242) {
            break;
          }

          message.widget = { $case: "widgetMatrixButton", value: WidgetMatrixButton.decode(reader, reader.uint32()) };
          continue;
        }
        case 31: {
          if (tag !== 250) {
            break;
          }

          message.widget = { $case: "widgetTrendCurve", value: WidgetTrendCurve.decode(reader, reader.uint32()) };
          continue;
        }
        case 32: {
          if (tag !== 258) {
            break;
          }

          message.widget = { $case: "widgetScatterCurve", value: WidgetScatterCurve.decode(reader, reader.uint32()) };
          continue;
        }
        case 33: {
          if (tag !== 266) {
            break;
          }

          message.widget = { $case: "widgetBarCurve", value: WidgetBarCurve.decode(reader, reader.uint32()) };
          continue;
        }
        case 34: {
          if (tag !== 274) {
            break;
          }

          message.widget = { $case: "widgetXyCurve", value: WidgetXYCurve.decode(reader, reader.uint32()) };
          continue;
        }
        case 35: {
          if (tag !== 282) {
            break;
          }

          message.widget = { $case: "widgetCalendar", value: WidgetCalendar.decode(reader, reader.uint32()) };
          continue;
        }
        case 36: {
          if (tag !== 290) {
            break;
          }

          message.widget = { $case: "widgetRollerList", value: WidgetRollerList.decode(reader, reader.uint32()) };
          continue;
        }
        case 37: {
          if (tag !== 298) {
            break;
          }

          message.widget = { $case: "widgetDropList", value: WidgetDropList.decode(reader, reader.uint32()) };
          continue;
        }
        case 38: {
          if (tag !== 306) {
            break;
          }

          message.widget = { $case: "widgetMeter", value: WidgetMeter.decode(reader, reader.uint32()) };
          continue;
        }
        case 39: {
          if (tag !== 314) {
            break;
          }

          message.widget = { $case: "widgetRuler", value: WidgetRuler.decode(reader, reader.uint32()) };
          continue;
        }
        case 40: {
          if (tag !== 322) {
            break;
          }

          message.widget = { $case: "widgetRectAngle", value: WidgetRectAngle.decode(reader, reader.uint32()) };
          continue;
        }
        case 41: {
          if (tag !== 330) {
            break;
          }

          message.widget = { $case: "widgetLinear", value: WidgetLinear.decode(reader, reader.uint32()) };
          continue;
        }
        case 42: {
          if (tag !== 338) {
            break;
          }

          message.widget = { $case: "widgetArc", value: WidgetArc.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Widget {
    return {
      idU16: isSet(object.idU16) ? globalThis.Number(object.idU16) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      memo: isSet(object.memo) ? globalThis.String(object.memo) : "",
      visible: isSet(object.visible) ? globalThis.Boolean(object.visible) : false,
      pin: isSet(object.pin) ? globalThis.Boolean(object.pin) : false,
      lockScale: isSet(object.lockScale) ? globalThis.Boolean(object.lockScale) : false,
      rwMode: isSet(object.rwMode) ? readWriteModeFromJSON(object.rwMode) : 0,
      location: isSet(object.location) ? Location.fromJSON(object.location) : undefined,
      size: isSet(object.size) ? Size.fromJSON(object.size) : undefined,
      enableControl: isSet(object.enableControl) ? EnableControl.fromJSON(object.enableControl) : undefined,
      stateProperty: isSet(object.stateProperty) ? StateProperty.fromJSON(object.stateProperty) : undefined,
      actionsU16: globalThis.Array.isArray(object?.actionsU16)
        ? object.actionsU16.map((e: any) => globalThis.Number(e))
        : [],
      minPressTimeU16: isSet(object.minPressTimeU16) ? globalThis.Number(object.minPressTimeU16) : 0,
      minPressIntervalU16: isSet(object.minPressIntervalU16) ? globalThis.Number(object.minPressIntervalU16) : 0,
      confirmParam: isSet(object.confirmParam) ? ConfirmParam.fromJSON(object.confirmParam) : undefined,
      soundFeedback: isSet(object.soundFeedback) ? globalThis.Boolean(object.soundFeedback) : false,
      graphic: globalThis.Array.isArray(object?.graphic)
        ? object.graphic.map((e: any) => GraphicReference.fromJSON(e))
        : [],
      style: globalThis.Array.isArray(object?.style) ? object.style.map((e: any) => StyleProperties.fromJSON(e)) : [],
      styleSyncU8: isSet(object.styleSyncU8) ? globalThis.Number(object.styleSyncU8) : 0,
      text: globalThis.Array.isArray(object?.text) ? object.text.map((e: any) => TextReference.fromJSON(e)) : [],
      widget: isSet(object.widgetClone)
        ? { $case: "widgetClone", value: WidgetClone.fromJSON(object.widgetClone) }
        : isSet(object.widgetGroup)
        ? { $case: "widgetGroup", value: WidgetGroup.fromJSON(object.widgetGroup) }
        : isSet(object.widgetText)
        ? { $case: "widgetText", value: WidgetText.fromJSON(object.widgetText) }
        : isSet(object.widgetGraphic)
        ? { $case: "widgetGraphic", value: WidgetGraphic.fromJSON(object.widgetGraphic) }
        : isSet(object.widgetBit)
        ? { $case: "widgetBit", value: WidgetBit.fromJSON(object.widgetBit) }
        : isSet(object.widgetWord)
        ? { $case: "widgetWord", value: WidgetWord.fromJSON(object.widgetWord) }
        : isSet(object.widgetNumber)
        ? { $case: "widgetNumber", value: WidgetNumber.fromJSON(object.widgetNumber) }
        : isSet(object.widgetString)
        ? { $case: "widgetString", value: WidgetString.fromJSON(object.widgetString) }
        : isSet(object.widgetButton)
        ? { $case: "widgetButton", value: WidgetButton.fromJSON(object.widgetButton) }
        : isSet(object.widgetMatrixButton)
        ? { $case: "widgetMatrixButton", value: WidgetMatrixButton.fromJSON(object.widgetMatrixButton) }
        : isSet(object.widgetTrendCurve)
        ? { $case: "widgetTrendCurve", value: WidgetTrendCurve.fromJSON(object.widgetTrendCurve) }
        : isSet(object.widgetScatterCurve)
        ? { $case: "widgetScatterCurve", value: WidgetScatterCurve.fromJSON(object.widgetScatterCurve) }
        : isSet(object.widgetBarCurve)
        ? { $case: "widgetBarCurve", value: WidgetBarCurve.fromJSON(object.widgetBarCurve) }
        : isSet(object.widgetXyCurve)
        ? { $case: "widgetXyCurve", value: WidgetXYCurve.fromJSON(object.widgetXyCurve) }
        : isSet(object.widgetCalendar)
        ? { $case: "widgetCalendar", value: WidgetCalendar.fromJSON(object.widgetCalendar) }
        : isSet(object.widgetRollerList)
        ? { $case: "widgetRollerList", value: WidgetRollerList.fromJSON(object.widgetRollerList) }
        : isSet(object.widgetDropList)
        ? { $case: "widgetDropList", value: WidgetDropList.fromJSON(object.widgetDropList) }
        : isSet(object.widgetMeter)
        ? { $case: "widgetMeter", value: WidgetMeter.fromJSON(object.widgetMeter) }
        : isSet(object.widgetRuler)
        ? { $case: "widgetRuler", value: WidgetRuler.fromJSON(object.widgetRuler) }
        : isSet(object.widgetRectAngle)
        ? { $case: "widgetRectAngle", value: WidgetRectAngle.fromJSON(object.widgetRectAngle) }
        : isSet(object.widgetLinear)
        ? { $case: "widgetLinear", value: WidgetLinear.fromJSON(object.widgetLinear) }
        : isSet(object.widgetArc)
        ? { $case: "widgetArc", value: WidgetArc.fromJSON(object.widgetArc) }
        : undefined,
    };
  },

  toJSON(message: Widget): unknown {
    const obj: any = {};
    if (message.idU16 !== 0) {
      obj.idU16 = Math.round(message.idU16);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.memo !== "") {
      obj.memo = message.memo;
    }
    if (message.visible !== false) {
      obj.visible = message.visible;
    }
    if (message.pin !== false) {
      obj.pin = message.pin;
    }
    if (message.lockScale !== false) {
      obj.lockScale = message.lockScale;
    }
    if (message.rwMode !== 0) {
      obj.rwMode = readWriteModeToJSON(message.rwMode);
    }
    if (message.location !== undefined) {
      obj.location = Location.toJSON(message.location);
    }
    if (message.size !== undefined) {
      obj.size = Size.toJSON(message.size);
    }
    if (message.enableControl !== undefined) {
      obj.enableControl = EnableControl.toJSON(message.enableControl);
    }
    if (message.stateProperty !== undefined) {
      obj.stateProperty = StateProperty.toJSON(message.stateProperty);
    }
    if (message.actionsU16?.length) {
      obj.actionsU16 = message.actionsU16.map((e) => Math.round(e));
    }
    if (message.minPressTimeU16 !== 0) {
      obj.minPressTimeU16 = Math.round(message.minPressTimeU16);
    }
    if (message.minPressIntervalU16 !== 0) {
      obj.minPressIntervalU16 = Math.round(message.minPressIntervalU16);
    }
    if (message.confirmParam !== undefined) {
      obj.confirmParam = ConfirmParam.toJSON(message.confirmParam);
    }
    if (message.soundFeedback !== false) {
      obj.soundFeedback = message.soundFeedback;
    }
    if (message.graphic?.length) {
      obj.graphic = message.graphic.map((e) => GraphicReference.toJSON(e));
    }
    if (message.style?.length) {
      obj.style = message.style.map((e) => StyleProperties.toJSON(e));
    }
    if (message.styleSyncU8 !== 0) {
      obj.styleSyncU8 = Math.round(message.styleSyncU8);
    }
    if (message.text?.length) {
      obj.text = message.text.map((e) => TextReference.toJSON(e));
    }
    if (message.widget?.$case === "widgetClone") {
      obj.widgetClone = WidgetClone.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetGroup") {
      obj.widgetGroup = WidgetGroup.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetText") {
      obj.widgetText = WidgetText.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetGraphic") {
      obj.widgetGraphic = WidgetGraphic.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetBit") {
      obj.widgetBit = WidgetBit.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetWord") {
      obj.widgetWord = WidgetWord.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetNumber") {
      obj.widgetNumber = WidgetNumber.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetString") {
      obj.widgetString = WidgetString.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetButton") {
      obj.widgetButton = WidgetButton.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetMatrixButton") {
      obj.widgetMatrixButton = WidgetMatrixButton.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetTrendCurve") {
      obj.widgetTrendCurve = WidgetTrendCurve.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetScatterCurve") {
      obj.widgetScatterCurve = WidgetScatterCurve.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetBarCurve") {
      obj.widgetBarCurve = WidgetBarCurve.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetXyCurve") {
      obj.widgetXyCurve = WidgetXYCurve.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetCalendar") {
      obj.widgetCalendar = WidgetCalendar.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetRollerList") {
      obj.widgetRollerList = WidgetRollerList.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetDropList") {
      obj.widgetDropList = WidgetDropList.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetMeter") {
      obj.widgetMeter = WidgetMeter.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetRuler") {
      obj.widgetRuler = WidgetRuler.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetRectAngle") {
      obj.widgetRectAngle = WidgetRectAngle.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetLinear") {
      obj.widgetLinear = WidgetLinear.toJSON(message.widget.value);
    } else if (message.widget?.$case === "widgetArc") {
      obj.widgetArc = WidgetArc.toJSON(message.widget.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Widget>, I>>(base?: I): Widget {
    return Widget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Widget>, I>>(object: I): Widget {
    const message = createBaseWidget();
    message.idU16 = object.idU16 ?? 0;
    message.name = object.name ?? "";
    message.memo = object.memo ?? "";
    message.visible = object.visible ?? false;
    message.pin = object.pin ?? false;
    message.lockScale = object.lockScale ?? false;
    message.rwMode = object.rwMode ?? 0;
    message.location = (object.location !== undefined && object.location !== null)
      ? Location.fromPartial(object.location)
      : undefined;
    message.size = (object.size !== undefined && object.size !== null) ? Size.fromPartial(object.size) : undefined;
    message.enableControl = (object.enableControl !== undefined && object.enableControl !== null)
      ? EnableControl.fromPartial(object.enableControl)
      : undefined;
    message.stateProperty = (object.stateProperty !== undefined && object.stateProperty !== null)
      ? StateProperty.fromPartial(object.stateProperty)
      : undefined;
    message.actionsU16 = object.actionsU16?.map((e) => e) || [];
    message.minPressTimeU16 = object.minPressTimeU16 ?? 0;
    message.minPressIntervalU16 = object.minPressIntervalU16 ?? 0;
    message.confirmParam = (object.confirmParam !== undefined && object.confirmParam !== null)
      ? ConfirmParam.fromPartial(object.confirmParam)
      : undefined;
    message.soundFeedback = object.soundFeedback ?? false;
    message.graphic = object.graphic?.map((e) => GraphicReference.fromPartial(e)) || [];
    message.style = object.style?.map((e) => StyleProperties.fromPartial(e)) || [];
    message.styleSyncU8 = object.styleSyncU8 ?? 0;
    message.text = object.text?.map((e) => TextReference.fromPartial(e)) || [];
    switch (object.widget?.$case) {
      case "widgetClone": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetClone", value: WidgetClone.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetGroup": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetGroup", value: WidgetGroup.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetText": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetText", value: WidgetText.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetGraphic": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetGraphic", value: WidgetGraphic.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetBit": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetBit", value: WidgetBit.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetWord": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetWord", value: WidgetWord.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetNumber": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetNumber", value: WidgetNumber.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetString": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetString", value: WidgetString.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetButton": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetButton", value: WidgetButton.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetMatrixButton": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetMatrixButton", value: WidgetMatrixButton.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetTrendCurve": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetTrendCurve", value: WidgetTrendCurve.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetScatterCurve": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetScatterCurve", value: WidgetScatterCurve.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetBarCurve": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetBarCurve", value: WidgetBarCurve.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetXyCurve": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetXyCurve", value: WidgetXYCurve.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetCalendar": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetCalendar", value: WidgetCalendar.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetRollerList": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetRollerList", value: WidgetRollerList.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetDropList": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetDropList", value: WidgetDropList.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetMeter": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetMeter", value: WidgetMeter.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetRuler": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetRuler", value: WidgetRuler.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetRectAngle": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetRectAngle", value: WidgetRectAngle.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetLinear": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetLinear", value: WidgetLinear.fromPartial(object.widget.value) };
        }
        break;
      }
      case "widgetArc": {
        if (object.widget?.value !== undefined && object.widget?.value !== null) {
          message.widget = { $case: "widgetArc", value: WidgetArc.fromPartial(object.widget.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseWidgetClone(): WidgetClone {
  return { pageIdU16: undefined, widgetIdU16: 0 };
}

export const WidgetClone: MessageFns<WidgetClone> = {
  encode(message: WidgetClone, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageIdU16 !== undefined) {
      writer.uint32(8).uint32(message.pageIdU16);
    }
    if (message.widgetIdU16 !== 0) {
      writer.uint32(16).uint32(message.widgetIdU16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetClone {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetClone();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pageIdU16 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.widgetIdU16 = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetClone {
    return {
      pageIdU16: isSet(object.pageIdU16) ? globalThis.Number(object.pageIdU16) : undefined,
      widgetIdU16: isSet(object.widgetIdU16) ? globalThis.Number(object.widgetIdU16) : 0,
    };
  },

  toJSON(message: WidgetClone): unknown {
    const obj: any = {};
    if (message.pageIdU16 !== undefined) {
      obj.pageIdU16 = Math.round(message.pageIdU16);
    }
    if (message.widgetIdU16 !== 0) {
      obj.widgetIdU16 = Math.round(message.widgetIdU16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetClone>, I>>(base?: I): WidgetClone {
    return WidgetClone.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetClone>, I>>(object: I): WidgetClone {
    const message = createBaseWidgetClone();
    message.pageIdU16 = object.pageIdU16 ?? undefined;
    message.widgetIdU16 = object.widgetIdU16 ?? 0;
    return message;
  },
};

function createBaseWidgetGroup(): WidgetGroup {
  return { subWidgets: [], disableUngroup: false };
}

export const WidgetGroup: MessageFns<WidgetGroup> = {
  encode(message: WidgetGroup, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.subWidgets) {
      Widget.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.disableUngroup !== false) {
      writer.uint32(16).bool(message.disableUngroup);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetGroup {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetGroup();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.subWidgets.push(Widget.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.disableUngroup = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetGroup {
    return {
      subWidgets: globalThis.Array.isArray(object?.subWidgets)
        ? object.subWidgets.map((e: any) => Widget.fromJSON(e))
        : [],
      disableUngroup: isSet(object.disableUngroup) ? globalThis.Boolean(object.disableUngroup) : false,
    };
  },

  toJSON(message: WidgetGroup): unknown {
    const obj: any = {};
    if (message.subWidgets?.length) {
      obj.subWidgets = message.subWidgets.map((e) => Widget.toJSON(e));
    }
    if (message.disableUngroup !== false) {
      obj.disableUngroup = message.disableUngroup;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetGroup>, I>>(base?: I): WidgetGroup {
    return WidgetGroup.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetGroup>, I>>(object: I): WidgetGroup {
    const message = createBaseWidgetGroup();
    message.subWidgets = object.subWidgets?.map((e) => Widget.fromPartial(e)) || [];
    message.disableUngroup = object.disableUngroup ?? false;
    return message;
  },
};

function createBaseWidgetText(): WidgetText {
  return {};
}

export const WidgetText: MessageFns<WidgetText> = {
  encode(_: WidgetText, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetText {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetText();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): WidgetText {
    return {};
  },

  toJSON(_: WidgetText): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetText>, I>>(base?: I): WidgetText {
    return WidgetText.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetText>, I>>(_: I): WidgetText {
    const message = createBaseWidgetText();
    return message;
  },
};

function createBaseWidgetGraphic(): WidgetGraphic {
  return {};
}

export const WidgetGraphic: MessageFns<WidgetGraphic> = {
  encode(_: WidgetGraphic, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetGraphic {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetGraphic();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): WidgetGraphic {
    return {};
  },

  toJSON(_: WidgetGraphic): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetGraphic>, I>>(base?: I): WidgetGraphic {
    return WidgetGraphic.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetGraphic>, I>>(_: I): WidgetGraphic {
    const message = createBaseWidgetGraphic();
    return message;
  },
};

function createBaseWidgetBit(): WidgetBit {
  return {};
}

export const WidgetBit: MessageFns<WidgetBit> = {
  encode(_: WidgetBit, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetBit {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetBit();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): WidgetBit {
    return {};
  },

  toJSON(_: WidgetBit): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetBit>, I>>(base?: I): WidgetBit {
    return WidgetBit.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetBit>, I>>(_: I): WidgetBit {
    const message = createBaseWidgetBit();
    return message;
  },
};

function createBaseWidgetWord(): WidgetWord {
  return {};
}

export const WidgetWord: MessageFns<WidgetWord> = {
  encode(_: WidgetWord, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetWord {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetWord();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): WidgetWord {
    return {};
  },

  toJSON(_: WidgetWord): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetWord>, I>>(base?: I): WidgetWord {
    return WidgetWord.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetWord>, I>>(_: I): WidgetWord {
    const message = createBaseWidgetWord();
    return message;
  },
};

function createBaseWidgetButton(): WidgetButton {
  return { buttonKeyCode: undefined };
}

export const WidgetButton: MessageFns<WidgetButton> = {
  encode(message: WidgetButton, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.buttonKeyCode !== undefined) {
      writer.uint32(8).int32(message.buttonKeyCode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetButton {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetButton();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.buttonKeyCode = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetButton {
    return { buttonKeyCode: isSet(object.buttonKeyCode) ? buttonKeyCodeFromJSON(object.buttonKeyCode) : undefined };
  },

  toJSON(message: WidgetButton): unknown {
    const obj: any = {};
    if (message.buttonKeyCode !== undefined) {
      obj.buttonKeyCode = buttonKeyCodeToJSON(message.buttonKeyCode);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetButton>, I>>(base?: I): WidgetButton {
    return WidgetButton.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetButton>, I>>(object: I): WidgetButton {
    const message = createBaseWidgetButton();
    message.buttonKeyCode = object.buttonKeyCode ?? undefined;
    return message;
  },
};

function createBaseWidgetMatrixButton(): WidgetMatrixButton {
  return { buttons: [], style: undefined, layout: 0 };
}

export const WidgetMatrixButton: MessageFns<WidgetMatrixButton> = {
  encode(message: WidgetMatrixButton, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.buttons) {
      MatrixButtonDefine.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.style !== undefined) {
      StyleProperties.encode(message.style, writer.uint32(18).fork()).join();
    }
    if (message.layout !== 0) {
      writer.uint32(24).int32(message.layout);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetMatrixButton {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetMatrixButton();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.buttons.push(MatrixButtonDefine.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.style = StyleProperties.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.layout = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetMatrixButton {
    return {
      buttons: globalThis.Array.isArray(object?.buttons)
        ? object.buttons.map((e: any) => MatrixButtonDefine.fromJSON(e))
        : [],
      style: isSet(object.style) ? StyleProperties.fromJSON(object.style) : undefined,
      layout: isSet(object.layout) ? matrixButtonLayoutFromJSON(object.layout) : 0,
    };
  },

  toJSON(message: WidgetMatrixButton): unknown {
    const obj: any = {};
    if (message.buttons?.length) {
      obj.buttons = message.buttons.map((e) => MatrixButtonDefine.toJSON(e));
    }
    if (message.style !== undefined) {
      obj.style = StyleProperties.toJSON(message.style);
    }
    if (message.layout !== 0) {
      obj.layout = matrixButtonLayoutToJSON(message.layout);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetMatrixButton>, I>>(base?: I): WidgetMatrixButton {
    return WidgetMatrixButton.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetMatrixButton>, I>>(object: I): WidgetMatrixButton {
    const message = createBaseWidgetMatrixButton();
    message.buttons = object.buttons?.map((e) => MatrixButtonDefine.fromPartial(e)) || [];
    message.style = (object.style !== undefined && object.style !== null)
      ? StyleProperties.fromPartial(object.style)
      : undefined;
    message.layout = object.layout ?? 0;
    return message;
  },
};

function createBaseMatrixButtonDefine(): MatrixButtonDefine {
  return { buttonFunctions: [], actionsU16: [], style: [], graphic: [], buttonWidthU16: 0 };
}

export const MatrixButtonDefine: MessageFns<MatrixButtonDefine> = {
  encode(message: MatrixButtonDefine, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.buttonFunctions) {
      MatrixButtonFunction.encode(v!, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.actionsU16) {
      writer.uint32(v);
    }
    writer.join();
    for (const v of message.style) {
      StyleProperties.encode(v!, writer.uint32(26).fork()).join();
    }
    for (const v of message.graphic) {
      GraphicReference.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.buttonWidthU16 !== 0) {
      writer.uint32(40).uint32(message.buttonWidthU16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MatrixButtonDefine {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMatrixButtonDefine();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.buttonFunctions.push(MatrixButtonFunction.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.actionsU16.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.actionsU16.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.style.push(StyleProperties.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.graphic.push(GraphicReference.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.buttonWidthU16 = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MatrixButtonDefine {
    return {
      buttonFunctions: globalThis.Array.isArray(object?.buttonFunctions)
        ? object.buttonFunctions.map((e: any) => MatrixButtonFunction.fromJSON(e))
        : [],
      actionsU16: globalThis.Array.isArray(object?.actionsU16)
        ? object.actionsU16.map((e: any) => globalThis.Number(e))
        : [],
      style: globalThis.Array.isArray(object?.style) ? object.style.map((e: any) => StyleProperties.fromJSON(e)) : [],
      graphic: globalThis.Array.isArray(object?.graphic)
        ? object.graphic.map((e: any) => GraphicReference.fromJSON(e))
        : [],
      buttonWidthU16: isSet(object.buttonWidthU16) ? globalThis.Number(object.buttonWidthU16) : 0,
    };
  },

  toJSON(message: MatrixButtonDefine): unknown {
    const obj: any = {};
    if (message.buttonFunctions?.length) {
      obj.buttonFunctions = message.buttonFunctions.map((e) => MatrixButtonFunction.toJSON(e));
    }
    if (message.actionsU16?.length) {
      obj.actionsU16 = message.actionsU16.map((e) => Math.round(e));
    }
    if (message.style?.length) {
      obj.style = message.style.map((e) => StyleProperties.toJSON(e));
    }
    if (message.graphic?.length) {
      obj.graphic = message.graphic.map((e) => GraphicReference.toJSON(e));
    }
    if (message.buttonWidthU16 !== 0) {
      obj.buttonWidthU16 = Math.round(message.buttonWidthU16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MatrixButtonDefine>, I>>(base?: I): MatrixButtonDefine {
    return MatrixButtonDefine.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MatrixButtonDefine>, I>>(object: I): MatrixButtonDefine {
    const message = createBaseMatrixButtonDefine();
    message.buttonFunctions = object.buttonFunctions?.map((e) => MatrixButtonFunction.fromPartial(e)) || [];
    message.actionsU16 = object.actionsU16?.map((e) => e) || [];
    message.style = object.style?.map((e) => StyleProperties.fromPartial(e)) || [];
    message.graphic = object.graphic?.map((e) => GraphicReference.fromPartial(e)) || [];
    message.buttonWidthU16 = object.buttonWidthU16 ?? 0;
    return message;
  },
};

function createBaseMatrixButtonFunction(): MatrixButtonFunction {
  return { func: undefined, text: undefined };
}

export const MatrixButtonFunction: MessageFns<MatrixButtonFunction> = {
  encode(message: MatrixButtonFunction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.func?.$case) {
      case "buttonKeyCode":
        writer.uint32(8).int32(message.func.value);
        break;
      case "unicode":
        writer.uint32(18).string(message.func.value);
        break;
    }
    if (message.text !== undefined) {
      TextReference.encode(message.text, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MatrixButtonFunction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMatrixButtonFunction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.func = { $case: "buttonKeyCode", value: reader.int32() as any };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.func = { $case: "unicode", value: reader.string() };
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.text = TextReference.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MatrixButtonFunction {
    return {
      func: isSet(object.buttonKeyCode)
        ? { $case: "buttonKeyCode", value: buttonKeyCodeFromJSON(object.buttonKeyCode) }
        : isSet(object.unicode)
        ? { $case: "unicode", value: globalThis.String(object.unicode) }
        : undefined,
      text: isSet(object.text) ? TextReference.fromJSON(object.text) : undefined,
    };
  },

  toJSON(message: MatrixButtonFunction): unknown {
    const obj: any = {};
    if (message.func?.$case === "buttonKeyCode") {
      obj.buttonKeyCode = buttonKeyCodeToJSON(message.func.value);
    } else if (message.func?.$case === "unicode") {
      obj.unicode = message.func.value;
    }
    if (message.text !== undefined) {
      obj.text = TextReference.toJSON(message.text);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MatrixButtonFunction>, I>>(base?: I): MatrixButtonFunction {
    return MatrixButtonFunction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MatrixButtonFunction>, I>>(object: I): MatrixButtonFunction {
    const message = createBaseMatrixButtonFunction();
    switch (object.func?.$case) {
      case "buttonKeyCode": {
        if (object.func?.value !== undefined && object.func?.value !== null) {
          message.func = { $case: "buttonKeyCode", value: object.func.value };
        }
        break;
      }
      case "unicode": {
        if (object.func?.value !== undefined && object.func?.value !== null) {
          message.func = { $case: "unicode", value: object.func.value };
        }
        break;
      }
    }
    message.text = (object.text !== undefined && object.text !== null)
      ? TextReference.fromPartial(object.text)
      : undefined;
    return message;
  },
};

function createBaseWidgetNumber(): WidgetNumber {
  return {
    currentValue: undefined,
    maxValue: undefined,
    minValue: undefined,
    supportInput: false,
    keyboardPageIdU16: 0,
    dataFormat: 0,
    integerDigitsU8: 0,
    decimalDigitsU8: 0,
    hideLeadingZero: false,
    showThousandsSeparator: false,
    hideTrailingZero: false,
    showPlusSign: false,
    lessThanMinColor: undefined,
    lessThanMinFlashTimeU8: 0,
    greaterThanMaxColor: undefined,
    greaterThanMaxFlashTimeU8: 0,
    passwordMode: false,
  };
}

export const WidgetNumber: MessageFns<WidgetNumber> = {
  encode(message: WidgetNumber, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.currentValue !== undefined) {
      NumberValue.encode(message.currentValue, writer.uint32(10).fork()).join();
    }
    if (message.maxValue !== undefined) {
      NumberValue.encode(message.maxValue, writer.uint32(18).fork()).join();
    }
    if (message.minValue !== undefined) {
      NumberValue.encode(message.minValue, writer.uint32(26).fork()).join();
    }
    if (message.supportInput !== false) {
      writer.uint32(32).bool(message.supportInput);
    }
    if (message.keyboardPageIdU16 !== 0) {
      writer.uint32(40).uint32(message.keyboardPageIdU16);
    }
    if (message.dataFormat !== 0) {
      writer.uint32(48).int32(message.dataFormat);
    }
    if (message.integerDigitsU8 !== 0) {
      writer.uint32(56).uint32(message.integerDigitsU8);
    }
    if (message.decimalDigitsU8 !== 0) {
      writer.uint32(64).uint32(message.decimalDigitsU8);
    }
    if (message.hideLeadingZero !== false) {
      writer.uint32(72).bool(message.hideLeadingZero);
    }
    if (message.showThousandsSeparator !== false) {
      writer.uint32(80).bool(message.showThousandsSeparator);
    }
    if (message.hideTrailingZero !== false) {
      writer.uint32(88).bool(message.hideTrailingZero);
    }
    if (message.showPlusSign !== false) {
      writer.uint32(96).bool(message.showPlusSign);
    }
    if (message.lessThanMinColor !== undefined) {
      ColorReference.encode(message.lessThanMinColor, writer.uint32(106).fork()).join();
    }
    if (message.lessThanMinFlashTimeU8 !== 0) {
      writer.uint32(112).uint32(message.lessThanMinFlashTimeU8);
    }
    if (message.greaterThanMaxColor !== undefined) {
      ColorReference.encode(message.greaterThanMaxColor, writer.uint32(122).fork()).join();
    }
    if (message.greaterThanMaxFlashTimeU8 !== 0) {
      writer.uint32(128).uint32(message.greaterThanMaxFlashTimeU8);
    }
    if (message.passwordMode !== false) {
      writer.uint32(136).bool(message.passwordMode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetNumber {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetNumber();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.currentValue = NumberValue.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.maxValue = NumberValue.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.minValue = NumberValue.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.supportInput = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.keyboardPageIdU16 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.dataFormat = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.integerDigitsU8 = reader.uint32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.decimalDigitsU8 = reader.uint32();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.hideLeadingZero = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.showThousandsSeparator = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.hideTrailingZero = reader.bool();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.showPlusSign = reader.bool();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.lessThanMinColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.lessThanMinFlashTimeU8 = reader.uint32();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.greaterThanMaxColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.greaterThanMaxFlashTimeU8 = reader.uint32();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.passwordMode = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetNumber {
    return {
      currentValue: isSet(object.currentValue) ? NumberValue.fromJSON(object.currentValue) : undefined,
      maxValue: isSet(object.maxValue) ? NumberValue.fromJSON(object.maxValue) : undefined,
      minValue: isSet(object.minValue) ? NumberValue.fromJSON(object.minValue) : undefined,
      supportInput: isSet(object.supportInput) ? globalThis.Boolean(object.supportInput) : false,
      keyboardPageIdU16: isSet(object.keyboardPageIdU16) ? globalThis.Number(object.keyboardPageIdU16) : 0,
      dataFormat: isSet(object.dataFormat) ? dataFormatTypeFromJSON(object.dataFormat) : 0,
      integerDigitsU8: isSet(object.integerDigitsU8) ? globalThis.Number(object.integerDigitsU8) : 0,
      decimalDigitsU8: isSet(object.decimalDigitsU8) ? globalThis.Number(object.decimalDigitsU8) : 0,
      hideLeadingZero: isSet(object.hideLeadingZero) ? globalThis.Boolean(object.hideLeadingZero) : false,
      showThousandsSeparator: isSet(object.showThousandsSeparator)
        ? globalThis.Boolean(object.showThousandsSeparator)
        : false,
      hideTrailingZero: isSet(object.hideTrailingZero) ? globalThis.Boolean(object.hideTrailingZero) : false,
      showPlusSign: isSet(object.showPlusSign) ? globalThis.Boolean(object.showPlusSign) : false,
      lessThanMinColor: isSet(object.lessThanMinColor) ? ColorReference.fromJSON(object.lessThanMinColor) : undefined,
      lessThanMinFlashTimeU8: isSet(object.lessThanMinFlashTimeU8)
        ? globalThis.Number(object.lessThanMinFlashTimeU8)
        : 0,
      greaterThanMaxColor: isSet(object.greaterThanMaxColor)
        ? ColorReference.fromJSON(object.greaterThanMaxColor)
        : undefined,
      greaterThanMaxFlashTimeU8: isSet(object.greaterThanMaxFlashTimeU8)
        ? globalThis.Number(object.greaterThanMaxFlashTimeU8)
        : 0,
      passwordMode: isSet(object.passwordMode) ? globalThis.Boolean(object.passwordMode) : false,
    };
  },

  toJSON(message: WidgetNumber): unknown {
    const obj: any = {};
    if (message.currentValue !== undefined) {
      obj.currentValue = NumberValue.toJSON(message.currentValue);
    }
    if (message.maxValue !== undefined) {
      obj.maxValue = NumberValue.toJSON(message.maxValue);
    }
    if (message.minValue !== undefined) {
      obj.minValue = NumberValue.toJSON(message.minValue);
    }
    if (message.supportInput !== false) {
      obj.supportInput = message.supportInput;
    }
    if (message.keyboardPageIdU16 !== 0) {
      obj.keyboardPageIdU16 = Math.round(message.keyboardPageIdU16);
    }
    if (message.dataFormat !== 0) {
      obj.dataFormat = dataFormatTypeToJSON(message.dataFormat);
    }
    if (message.integerDigitsU8 !== 0) {
      obj.integerDigitsU8 = Math.round(message.integerDigitsU8);
    }
    if (message.decimalDigitsU8 !== 0) {
      obj.decimalDigitsU8 = Math.round(message.decimalDigitsU8);
    }
    if (message.hideLeadingZero !== false) {
      obj.hideLeadingZero = message.hideLeadingZero;
    }
    if (message.showThousandsSeparator !== false) {
      obj.showThousandsSeparator = message.showThousandsSeparator;
    }
    if (message.hideTrailingZero !== false) {
      obj.hideTrailingZero = message.hideTrailingZero;
    }
    if (message.showPlusSign !== false) {
      obj.showPlusSign = message.showPlusSign;
    }
    if (message.lessThanMinColor !== undefined) {
      obj.lessThanMinColor = ColorReference.toJSON(message.lessThanMinColor);
    }
    if (message.lessThanMinFlashTimeU8 !== 0) {
      obj.lessThanMinFlashTimeU8 = Math.round(message.lessThanMinFlashTimeU8);
    }
    if (message.greaterThanMaxColor !== undefined) {
      obj.greaterThanMaxColor = ColorReference.toJSON(message.greaterThanMaxColor);
    }
    if (message.greaterThanMaxFlashTimeU8 !== 0) {
      obj.greaterThanMaxFlashTimeU8 = Math.round(message.greaterThanMaxFlashTimeU8);
    }
    if (message.passwordMode !== false) {
      obj.passwordMode = message.passwordMode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetNumber>, I>>(base?: I): WidgetNumber {
    return WidgetNumber.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetNumber>, I>>(object: I): WidgetNumber {
    const message = createBaseWidgetNumber();
    message.currentValue = (object.currentValue !== undefined && object.currentValue !== null)
      ? NumberValue.fromPartial(object.currentValue)
      : undefined;
    message.maxValue = (object.maxValue !== undefined && object.maxValue !== null)
      ? NumberValue.fromPartial(object.maxValue)
      : undefined;
    message.minValue = (object.minValue !== undefined && object.minValue !== null)
      ? NumberValue.fromPartial(object.minValue)
      : undefined;
    message.supportInput = object.supportInput ?? false;
    message.keyboardPageIdU16 = object.keyboardPageIdU16 ?? 0;
    message.dataFormat = object.dataFormat ?? 0;
    message.integerDigitsU8 = object.integerDigitsU8 ?? 0;
    message.decimalDigitsU8 = object.decimalDigitsU8 ?? 0;
    message.hideLeadingZero = object.hideLeadingZero ?? false;
    message.showThousandsSeparator = object.showThousandsSeparator ?? false;
    message.hideTrailingZero = object.hideTrailingZero ?? false;
    message.showPlusSign = object.showPlusSign ?? false;
    message.lessThanMinColor = (object.lessThanMinColor !== undefined && object.lessThanMinColor !== null)
      ? ColorReference.fromPartial(object.lessThanMinColor)
      : undefined;
    message.lessThanMinFlashTimeU8 = object.lessThanMinFlashTimeU8 ?? 0;
    message.greaterThanMaxColor = (object.greaterThanMaxColor !== undefined && object.greaterThanMaxColor !== null)
      ? ColorReference.fromPartial(object.greaterThanMaxColor)
      : undefined;
    message.greaterThanMaxFlashTimeU8 = object.greaterThanMaxFlashTimeU8 ?? 0;
    message.passwordMode = object.passwordMode ?? false;
    return message;
  },
};

function createBaseWidgetString(): WidgetString {
  return {
    currentValue: "",
    supportInput: false,
    keyboardPageIdU16: 0,
    dataFormat: 0,
    maxLengthU16: 0,
    minLengthU16: 0,
    showScrollbar: false,
    passwordMode: false,
  };
}

export const WidgetString: MessageFns<WidgetString> = {
  encode(message: WidgetString, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.currentValue !== "") {
      writer.uint32(10).string(message.currentValue);
    }
    if (message.supportInput !== false) {
      writer.uint32(16).bool(message.supportInput);
    }
    if (message.keyboardPageIdU16 !== 0) {
      writer.uint32(24).uint32(message.keyboardPageIdU16);
    }
    if (message.dataFormat !== 0) {
      writer.uint32(32).int32(message.dataFormat);
    }
    if (message.maxLengthU16 !== 0) {
      writer.uint32(40).uint32(message.maxLengthU16);
    }
    if (message.minLengthU16 !== 0) {
      writer.uint32(48).uint32(message.minLengthU16);
    }
    if (message.showScrollbar !== false) {
      writer.uint32(56).bool(message.showScrollbar);
    }
    if (message.passwordMode !== false) {
      writer.uint32(64).bool(message.passwordMode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetString {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetString();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.currentValue = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.supportInput = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.keyboardPageIdU16 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.dataFormat = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.maxLengthU16 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.minLengthU16 = reader.uint32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.showScrollbar = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.passwordMode = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetString {
    return {
      currentValue: isSet(object.currentValue) ? globalThis.String(object.currentValue) : "",
      supportInput: isSet(object.supportInput) ? globalThis.Boolean(object.supportInput) : false,
      keyboardPageIdU16: isSet(object.keyboardPageIdU16) ? globalThis.Number(object.keyboardPageIdU16) : 0,
      dataFormat: isSet(object.dataFormat) ? dataFormatTypeFromJSON(object.dataFormat) : 0,
      maxLengthU16: isSet(object.maxLengthU16) ? globalThis.Number(object.maxLengthU16) : 0,
      minLengthU16: isSet(object.minLengthU16) ? globalThis.Number(object.minLengthU16) : 0,
      showScrollbar: isSet(object.showScrollbar) ? globalThis.Boolean(object.showScrollbar) : false,
      passwordMode: isSet(object.passwordMode) ? globalThis.Boolean(object.passwordMode) : false,
    };
  },

  toJSON(message: WidgetString): unknown {
    const obj: any = {};
    if (message.currentValue !== "") {
      obj.currentValue = message.currentValue;
    }
    if (message.supportInput !== false) {
      obj.supportInput = message.supportInput;
    }
    if (message.keyboardPageIdU16 !== 0) {
      obj.keyboardPageIdU16 = Math.round(message.keyboardPageIdU16);
    }
    if (message.dataFormat !== 0) {
      obj.dataFormat = dataFormatTypeToJSON(message.dataFormat);
    }
    if (message.maxLengthU16 !== 0) {
      obj.maxLengthU16 = Math.round(message.maxLengthU16);
    }
    if (message.minLengthU16 !== 0) {
      obj.minLengthU16 = Math.round(message.minLengthU16);
    }
    if (message.showScrollbar !== false) {
      obj.showScrollbar = message.showScrollbar;
    }
    if (message.passwordMode !== false) {
      obj.passwordMode = message.passwordMode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetString>, I>>(base?: I): WidgetString {
    return WidgetString.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetString>, I>>(object: I): WidgetString {
    const message = createBaseWidgetString();
    message.currentValue = object.currentValue ?? "";
    message.supportInput = object.supportInput ?? false;
    message.keyboardPageIdU16 = object.keyboardPageIdU16 ?? 0;
    message.dataFormat = object.dataFormat ?? 0;
    message.maxLengthU16 = object.maxLengthU16 ?? 0;
    message.minLengthU16 = object.minLengthU16 ?? 0;
    message.showScrollbar = object.showScrollbar ?? false;
    message.passwordMode = object.passwordMode ?? false;
    return message;
  },
};

function createBaseComboBox(): ComboBox {
  return {};
}

export const ComboBox: MessageFns<ComboBox> = {
  encode(_: ComboBox, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ComboBox {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseComboBox();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): ComboBox {
    return {};
  },

  toJSON(_: ComboBox): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<ComboBox>, I>>(base?: I): ComboBox {
    return ComboBox.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ComboBox>, I>>(_: I): ComboBox {
    const message = createBaseComboBox();
    return message;
  },
};

function createBaseWidgetCurve(): WidgetCurve {
  return {
    curveType: 0,
    seriesCountU8: 0,
    xMode: undefined,
    drawDirection: 0,
    yRangeUseSeriesU8: undefined,
    seriesConfig: [],
    enableCursor: false,
    cursorColor: undefined,
    cursorWidthU8: 0,
    useCurrentTime: false,
    timeFormat: 0,
    dateFormat: 0,
    scaleValueConfigX: undefined,
    scaleValueConfigY: undefined,
    scaleValueConfigY2: undefined,
  };
}

export const WidgetCurve: MessageFns<WidgetCurve> = {
  encode(message: WidgetCurve, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.curveType !== 0) {
      writer.uint32(8).int32(message.curveType);
    }
    if (message.seriesCountU8 !== 0) {
      writer.uint32(16).uint32(message.seriesCountU8);
    }
    switch (message.xMode?.$case) {
      case "pointDistanceU16":
        writer.uint32(24).uint32(message.xMode.value);
        break;
      case "pointCountU16":
        writer.uint32(32).uint32(message.xMode.value);
        break;
      case "rangeSecondU16":
        writer.uint32(40).uint32(message.xMode.value);
        break;
      case "rangeMinuteU16":
        writer.uint32(48).uint32(message.xMode.value);
        break;
    }
    if (message.drawDirection !== 0) {
      writer.uint32(56).int32(message.drawDirection);
    }
    if (message.yRangeUseSeriesU8 !== undefined) {
      writer.uint32(64).uint32(message.yRangeUseSeriesU8);
    }
    for (const v of message.seriesConfig) {
      WidgetCurve_SeriesConfig.encode(v!, writer.uint32(74).fork()).join();
    }
    if (message.enableCursor !== false) {
      writer.uint32(80).bool(message.enableCursor);
    }
    if (message.cursorColor !== undefined) {
      ColorReference.encode(message.cursorColor, writer.uint32(90).fork()).join();
    }
    if (message.cursorWidthU8 !== 0) {
      writer.uint32(96).uint32(message.cursorWidthU8);
    }
    if (message.useCurrentTime !== false) {
      writer.uint32(104).bool(message.useCurrentTime);
    }
    if (message.timeFormat !== 0) {
      writer.uint32(112).int32(message.timeFormat);
    }
    if (message.dateFormat !== 0) {
      writer.uint32(120).int32(message.dateFormat);
    }
    if (message.scaleValueConfigX !== undefined) {
      ScaleValueConfig.encode(message.scaleValueConfigX, writer.uint32(130).fork()).join();
    }
    if (message.scaleValueConfigY !== undefined) {
      ScaleValueConfig.encode(message.scaleValueConfigY, writer.uint32(138).fork()).join();
    }
    if (message.scaleValueConfigY2 !== undefined) {
      ScaleValueConfig.encode(message.scaleValueConfigY2, writer.uint32(146).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetCurve {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetCurve();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.curveType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.seriesCountU8 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.xMode = { $case: "pointDistanceU16", value: reader.uint32() };
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.xMode = { $case: "pointCountU16", value: reader.uint32() };
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.xMode = { $case: "rangeSecondU16", value: reader.uint32() };
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.xMode = { $case: "rangeMinuteU16", value: reader.uint32() };
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.drawDirection = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.yRangeUseSeriesU8 = reader.uint32();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.seriesConfig.push(WidgetCurve_SeriesConfig.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.enableCursor = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.cursorColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.cursorWidthU8 = reader.uint32();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.useCurrentTime = reader.bool();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.timeFormat = reader.int32() as any;
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.dateFormat = reader.int32() as any;
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.scaleValueConfigX = ScaleValueConfig.decode(reader, reader.uint32());
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.scaleValueConfigY = ScaleValueConfig.decode(reader, reader.uint32());
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.scaleValueConfigY2 = ScaleValueConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetCurve {
    return {
      curveType: isSet(object.curveType) ? curveTypeFromJSON(object.curveType) : 0,
      seriesCountU8: isSet(object.seriesCountU8) ? globalThis.Number(object.seriesCountU8) : 0,
      xMode: isSet(object.pointDistanceU16)
        ? { $case: "pointDistanceU16", value: globalThis.Number(object.pointDistanceU16) }
        : isSet(object.pointCountU16)
        ? { $case: "pointCountU16", value: globalThis.Number(object.pointCountU16) }
        : isSet(object.rangeSecondU16)
        ? { $case: "rangeSecondU16", value: globalThis.Number(object.rangeSecondU16) }
        : isSet(object.rangeMinuteU16)
        ? { $case: "rangeMinuteU16", value: globalThis.Number(object.rangeMinuteU16) }
        : undefined,
      drawDirection: isSet(object.drawDirection) ? scrollDirectionFromJSON(object.drawDirection) : 0,
      yRangeUseSeriesU8: isSet(object.yRangeUseSeriesU8) ? globalThis.Number(object.yRangeUseSeriesU8) : undefined,
      seriesConfig: globalThis.Array.isArray(object?.seriesConfig)
        ? object.seriesConfig.map((e: any) => WidgetCurve_SeriesConfig.fromJSON(e))
        : [],
      enableCursor: isSet(object.enableCursor) ? globalThis.Boolean(object.enableCursor) : false,
      cursorColor: isSet(object.cursorColor) ? ColorReference.fromJSON(object.cursorColor) : undefined,
      cursorWidthU8: isSet(object.cursorWidthU8) ? globalThis.Number(object.cursorWidthU8) : 0,
      useCurrentTime: isSet(object.useCurrentTime) ? globalThis.Boolean(object.useCurrentTime) : false,
      timeFormat: isSet(object.timeFormat) ? timeFormatTypeFromJSON(object.timeFormat) : 0,
      dateFormat: isSet(object.dateFormat) ? dateFormatTypeFromJSON(object.dateFormat) : 0,
      scaleValueConfigX: isSet(object.scaleValueConfigX)
        ? ScaleValueConfig.fromJSON(object.scaleValueConfigX)
        : undefined,
      scaleValueConfigY: isSet(object.scaleValueConfigY)
        ? ScaleValueConfig.fromJSON(object.scaleValueConfigY)
        : undefined,
      scaleValueConfigY2: isSet(object.scaleValueConfigY2)
        ? ScaleValueConfig.fromJSON(object.scaleValueConfigY2)
        : undefined,
    };
  },

  toJSON(message: WidgetCurve): unknown {
    const obj: any = {};
    if (message.curveType !== 0) {
      obj.curveType = curveTypeToJSON(message.curveType);
    }
    if (message.seriesCountU8 !== 0) {
      obj.seriesCountU8 = Math.round(message.seriesCountU8);
    }
    if (message.xMode?.$case === "pointDistanceU16") {
      obj.pointDistanceU16 = Math.round(message.xMode.value);
    } else if (message.xMode?.$case === "pointCountU16") {
      obj.pointCountU16 = Math.round(message.xMode.value);
    } else if (message.xMode?.$case === "rangeSecondU16") {
      obj.rangeSecondU16 = Math.round(message.xMode.value);
    } else if (message.xMode?.$case === "rangeMinuteU16") {
      obj.rangeMinuteU16 = Math.round(message.xMode.value);
    }
    if (message.drawDirection !== 0) {
      obj.drawDirection = scrollDirectionToJSON(message.drawDirection);
    }
    if (message.yRangeUseSeriesU8 !== undefined) {
      obj.yRangeUseSeriesU8 = Math.round(message.yRangeUseSeriesU8);
    }
    if (message.seriesConfig?.length) {
      obj.seriesConfig = message.seriesConfig.map((e) => WidgetCurve_SeriesConfig.toJSON(e));
    }
    if (message.enableCursor !== false) {
      obj.enableCursor = message.enableCursor;
    }
    if (message.cursorColor !== undefined) {
      obj.cursorColor = ColorReference.toJSON(message.cursorColor);
    }
    if (message.cursorWidthU8 !== 0) {
      obj.cursorWidthU8 = Math.round(message.cursorWidthU8);
    }
    if (message.useCurrentTime !== false) {
      obj.useCurrentTime = message.useCurrentTime;
    }
    if (message.timeFormat !== 0) {
      obj.timeFormat = timeFormatTypeToJSON(message.timeFormat);
    }
    if (message.dateFormat !== 0) {
      obj.dateFormat = dateFormatTypeToJSON(message.dateFormat);
    }
    if (message.scaleValueConfigX !== undefined) {
      obj.scaleValueConfigX = ScaleValueConfig.toJSON(message.scaleValueConfigX);
    }
    if (message.scaleValueConfigY !== undefined) {
      obj.scaleValueConfigY = ScaleValueConfig.toJSON(message.scaleValueConfigY);
    }
    if (message.scaleValueConfigY2 !== undefined) {
      obj.scaleValueConfigY2 = ScaleValueConfig.toJSON(message.scaleValueConfigY2);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetCurve>, I>>(base?: I): WidgetCurve {
    return WidgetCurve.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetCurve>, I>>(object: I): WidgetCurve {
    const message = createBaseWidgetCurve();
    message.curveType = object.curveType ?? 0;
    message.seriesCountU8 = object.seriesCountU8 ?? 0;
    switch (object.xMode?.$case) {
      case "pointDistanceU16": {
        if (object.xMode?.value !== undefined && object.xMode?.value !== null) {
          message.xMode = { $case: "pointDistanceU16", value: object.xMode.value };
        }
        break;
      }
      case "pointCountU16": {
        if (object.xMode?.value !== undefined && object.xMode?.value !== null) {
          message.xMode = { $case: "pointCountU16", value: object.xMode.value };
        }
        break;
      }
      case "rangeSecondU16": {
        if (object.xMode?.value !== undefined && object.xMode?.value !== null) {
          message.xMode = { $case: "rangeSecondU16", value: object.xMode.value };
        }
        break;
      }
      case "rangeMinuteU16": {
        if (object.xMode?.value !== undefined && object.xMode?.value !== null) {
          message.xMode = { $case: "rangeMinuteU16", value: object.xMode.value };
        }
        break;
      }
    }
    message.drawDirection = object.drawDirection ?? 0;
    message.yRangeUseSeriesU8 = object.yRangeUseSeriesU8 ?? undefined;
    message.seriesConfig = object.seriesConfig?.map((e) => WidgetCurve_SeriesConfig.fromPartial(e)) || [];
    message.enableCursor = object.enableCursor ?? false;
    message.cursorColor = (object.cursorColor !== undefined && object.cursorColor !== null)
      ? ColorReference.fromPartial(object.cursorColor)
      : undefined;
    message.cursorWidthU8 = object.cursorWidthU8 ?? 0;
    message.useCurrentTime = object.useCurrentTime ?? false;
    message.timeFormat = object.timeFormat ?? 0;
    message.dateFormat = object.dateFormat ?? 0;
    message.scaleValueConfigX = (object.scaleValueConfigX !== undefined && object.scaleValueConfigX !== null)
      ? ScaleValueConfig.fromPartial(object.scaleValueConfigX)
      : undefined;
    message.scaleValueConfigY = (object.scaleValueConfigY !== undefined && object.scaleValueConfigY !== null)
      ? ScaleValueConfig.fromPartial(object.scaleValueConfigY)
      : undefined;
    message.scaleValueConfigY2 = (object.scaleValueConfigY2 !== undefined && object.scaleValueConfigY2 !== null)
      ? ScaleValueConfig.fromPartial(object.scaleValueConfigY2)
      : undefined;
    return message;
  },
};

function createBaseWidgetCurve_SeriesConfig(): WidgetCurve_SeriesConfig {
  return {
    supportScale: false,
    showFadeMask: false,
    color: undefined,
    pointConfig: undefined,
    lineConfig: undefined,
    barConfig: undefined,
    yMaxValue: undefined,
    yMinValue: undefined,
    xMinValue: undefined,
    xMaxValue: undefined,
  };
}

export const WidgetCurve_SeriesConfig: MessageFns<WidgetCurve_SeriesConfig> = {
  encode(message: WidgetCurve_SeriesConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.supportScale !== false) {
      writer.uint32(8).bool(message.supportScale);
    }
    if (message.showFadeMask !== false) {
      writer.uint32(16).bool(message.showFadeMask);
    }
    if (message.color !== undefined) {
      ColorReference.encode(message.color, writer.uint32(26).fork()).join();
    }
    if (message.pointConfig !== undefined) {
      PointConfig.encode(message.pointConfig, writer.uint32(34).fork()).join();
    }
    if (message.lineConfig !== undefined) {
      LineConfig.encode(message.lineConfig, writer.uint32(42).fork()).join();
    }
    if (message.barConfig !== undefined) {
      BarConfig.encode(message.barConfig, writer.uint32(50).fork()).join();
    }
    if (message.yMaxValue !== undefined) {
      NumberValue.encode(message.yMaxValue, writer.uint32(58).fork()).join();
    }
    if (message.yMinValue !== undefined) {
      NumberValue.encode(message.yMinValue, writer.uint32(66).fork()).join();
    }
    if (message.xMinValue !== undefined) {
      NumberValue.encode(message.xMinValue, writer.uint32(74).fork()).join();
    }
    if (message.xMaxValue !== undefined) {
      NumberValue.encode(message.xMaxValue, writer.uint32(82).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetCurve_SeriesConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetCurve_SeriesConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.supportScale = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.showFadeMask = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.color = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.pointConfig = PointConfig.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.lineConfig = LineConfig.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.barConfig = BarConfig.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.yMaxValue = NumberValue.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.yMinValue = NumberValue.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.xMinValue = NumberValue.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.xMaxValue = NumberValue.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetCurve_SeriesConfig {
    return {
      supportScale: isSet(object.supportScale) ? globalThis.Boolean(object.supportScale) : false,
      showFadeMask: isSet(object.showFadeMask) ? globalThis.Boolean(object.showFadeMask) : false,
      color: isSet(object.color) ? ColorReference.fromJSON(object.color) : undefined,
      pointConfig: isSet(object.pointConfig) ? PointConfig.fromJSON(object.pointConfig) : undefined,
      lineConfig: isSet(object.lineConfig) ? LineConfig.fromJSON(object.lineConfig) : undefined,
      barConfig: isSet(object.barConfig) ? BarConfig.fromJSON(object.barConfig) : undefined,
      yMaxValue: isSet(object.yMaxValue) ? NumberValue.fromJSON(object.yMaxValue) : undefined,
      yMinValue: isSet(object.yMinValue) ? NumberValue.fromJSON(object.yMinValue) : undefined,
      xMinValue: isSet(object.xMinValue) ? NumberValue.fromJSON(object.xMinValue) : undefined,
      xMaxValue: isSet(object.xMaxValue) ? NumberValue.fromJSON(object.xMaxValue) : undefined,
    };
  },

  toJSON(message: WidgetCurve_SeriesConfig): unknown {
    const obj: any = {};
    if (message.supportScale !== false) {
      obj.supportScale = message.supportScale;
    }
    if (message.showFadeMask !== false) {
      obj.showFadeMask = message.showFadeMask;
    }
    if (message.color !== undefined) {
      obj.color = ColorReference.toJSON(message.color);
    }
    if (message.pointConfig !== undefined) {
      obj.pointConfig = PointConfig.toJSON(message.pointConfig);
    }
    if (message.lineConfig !== undefined) {
      obj.lineConfig = LineConfig.toJSON(message.lineConfig);
    }
    if (message.barConfig !== undefined) {
      obj.barConfig = BarConfig.toJSON(message.barConfig);
    }
    if (message.yMaxValue !== undefined) {
      obj.yMaxValue = NumberValue.toJSON(message.yMaxValue);
    }
    if (message.yMinValue !== undefined) {
      obj.yMinValue = NumberValue.toJSON(message.yMinValue);
    }
    if (message.xMinValue !== undefined) {
      obj.xMinValue = NumberValue.toJSON(message.xMinValue);
    }
    if (message.xMaxValue !== undefined) {
      obj.xMaxValue = NumberValue.toJSON(message.xMaxValue);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetCurve_SeriesConfig>, I>>(base?: I): WidgetCurve_SeriesConfig {
    return WidgetCurve_SeriesConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetCurve_SeriesConfig>, I>>(object: I): WidgetCurve_SeriesConfig {
    const message = createBaseWidgetCurve_SeriesConfig();
    message.supportScale = object.supportScale ?? false;
    message.showFadeMask = object.showFadeMask ?? false;
    message.color = (object.color !== undefined && object.color !== null)
      ? ColorReference.fromPartial(object.color)
      : undefined;
    message.pointConfig = (object.pointConfig !== undefined && object.pointConfig !== null)
      ? PointConfig.fromPartial(object.pointConfig)
      : undefined;
    message.lineConfig = (object.lineConfig !== undefined && object.lineConfig !== null)
      ? LineConfig.fromPartial(object.lineConfig)
      : undefined;
    message.barConfig = (object.barConfig !== undefined && object.barConfig !== null)
      ? BarConfig.fromPartial(object.barConfig)
      : undefined;
    message.yMaxValue = (object.yMaxValue !== undefined && object.yMaxValue !== null)
      ? NumberValue.fromPartial(object.yMaxValue)
      : undefined;
    message.yMinValue = (object.yMinValue !== undefined && object.yMinValue !== null)
      ? NumberValue.fromPartial(object.yMinValue)
      : undefined;
    message.xMinValue = (object.xMinValue !== undefined && object.xMinValue !== null)
      ? NumberValue.fromPartial(object.xMinValue)
      : undefined;
    message.xMaxValue = (object.xMaxValue !== undefined && object.xMaxValue !== null)
      ? NumberValue.fromPartial(object.xMaxValue)
      : undefined;
    return message;
  },
};

function createBaseWidgetTrendCurve(): WidgetTrendCurve {
  return {
    curve: undefined,
    pauseResumeTimeU8: 0,
    isHistory: false,
    useRelativeTime: false,
    useTimeLabel: false,
    timeFormat: 0,
    dateFormat: 0,
  };
}

export const WidgetTrendCurve: MessageFns<WidgetTrendCurve> = {
  encode(message: WidgetTrendCurve, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.curve !== undefined) {
      WidgetCurve.encode(message.curve, writer.uint32(10).fork()).join();
    }
    if (message.pauseResumeTimeU8 !== 0) {
      writer.uint32(16).uint32(message.pauseResumeTimeU8);
    }
    if (message.isHistory !== false) {
      writer.uint32(24).bool(message.isHistory);
    }
    if (message.useRelativeTime !== false) {
      writer.uint32(32).bool(message.useRelativeTime);
    }
    if (message.useTimeLabel !== false) {
      writer.uint32(40).bool(message.useTimeLabel);
    }
    if (message.timeFormat !== 0) {
      writer.uint32(48).int32(message.timeFormat);
    }
    if (message.dateFormat !== 0) {
      writer.uint32(56).int32(message.dateFormat);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetTrendCurve {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetTrendCurve();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.curve = WidgetCurve.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.pauseResumeTimeU8 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isHistory = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.useRelativeTime = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.useTimeLabel = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.timeFormat = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.dateFormat = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetTrendCurve {
    return {
      curve: isSet(object.curve) ? WidgetCurve.fromJSON(object.curve) : undefined,
      pauseResumeTimeU8: isSet(object.pauseResumeTimeU8) ? globalThis.Number(object.pauseResumeTimeU8) : 0,
      isHistory: isSet(object.isHistory) ? globalThis.Boolean(object.isHistory) : false,
      useRelativeTime: isSet(object.useRelativeTime) ? globalThis.Boolean(object.useRelativeTime) : false,
      useTimeLabel: isSet(object.useTimeLabel) ? globalThis.Boolean(object.useTimeLabel) : false,
      timeFormat: isSet(object.timeFormat) ? timeFormatTypeFromJSON(object.timeFormat) : 0,
      dateFormat: isSet(object.dateFormat) ? dateFormatTypeFromJSON(object.dateFormat) : 0,
    };
  },

  toJSON(message: WidgetTrendCurve): unknown {
    const obj: any = {};
    if (message.curve !== undefined) {
      obj.curve = WidgetCurve.toJSON(message.curve);
    }
    if (message.pauseResumeTimeU8 !== 0) {
      obj.pauseResumeTimeU8 = Math.round(message.pauseResumeTimeU8);
    }
    if (message.isHistory !== false) {
      obj.isHistory = message.isHistory;
    }
    if (message.useRelativeTime !== false) {
      obj.useRelativeTime = message.useRelativeTime;
    }
    if (message.useTimeLabel !== false) {
      obj.useTimeLabel = message.useTimeLabel;
    }
    if (message.timeFormat !== 0) {
      obj.timeFormat = timeFormatTypeToJSON(message.timeFormat);
    }
    if (message.dateFormat !== 0) {
      obj.dateFormat = dateFormatTypeToJSON(message.dateFormat);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetTrendCurve>, I>>(base?: I): WidgetTrendCurve {
    return WidgetTrendCurve.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetTrendCurve>, I>>(object: I): WidgetTrendCurve {
    const message = createBaseWidgetTrendCurve();
    message.curve = (object.curve !== undefined && object.curve !== null)
      ? WidgetCurve.fromPartial(object.curve)
      : undefined;
    message.pauseResumeTimeU8 = object.pauseResumeTimeU8 ?? 0;
    message.isHistory = object.isHistory ?? false;
    message.useRelativeTime = object.useRelativeTime ?? false;
    message.useTimeLabel = object.useTimeLabel ?? false;
    message.timeFormat = object.timeFormat ?? 0;
    message.dateFormat = object.dateFormat ?? 0;
    return message;
  },
};

function createBaseWidgetXYCurve(): WidgetXYCurve {
  return { curve: undefined };
}

export const WidgetXYCurve: MessageFns<WidgetXYCurve> = {
  encode(message: WidgetXYCurve, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.curve !== undefined) {
      WidgetCurve.encode(message.curve, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetXYCurve {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetXYCurve();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.curve = WidgetCurve.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetXYCurve {
    return { curve: isSet(object.curve) ? WidgetCurve.fromJSON(object.curve) : undefined };
  },

  toJSON(message: WidgetXYCurve): unknown {
    const obj: any = {};
    if (message.curve !== undefined) {
      obj.curve = WidgetCurve.toJSON(message.curve);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetXYCurve>, I>>(base?: I): WidgetXYCurve {
    return WidgetXYCurve.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetXYCurve>, I>>(object: I): WidgetXYCurve {
    const message = createBaseWidgetXYCurve();
    message.curve = (object.curve !== undefined && object.curve !== null)
      ? WidgetCurve.fromPartial(object.curve)
      : undefined;
    return message;
  },
};

function createBaseWidgetBarCurve(): WidgetBarCurve {
  return { curve: undefined };
}

export const WidgetBarCurve: MessageFns<WidgetBarCurve> = {
  encode(message: WidgetBarCurve, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.curve !== undefined) {
      WidgetCurve.encode(message.curve, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetBarCurve {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetBarCurve();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.curve = WidgetCurve.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetBarCurve {
    return { curve: isSet(object.curve) ? WidgetCurve.fromJSON(object.curve) : undefined };
  },

  toJSON(message: WidgetBarCurve): unknown {
    const obj: any = {};
    if (message.curve !== undefined) {
      obj.curve = WidgetCurve.toJSON(message.curve);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetBarCurve>, I>>(base?: I): WidgetBarCurve {
    return WidgetBarCurve.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetBarCurve>, I>>(object: I): WidgetBarCurve {
    const message = createBaseWidgetBarCurve();
    message.curve = (object.curve !== undefined && object.curve !== null)
      ? WidgetCurve.fromPartial(object.curve)
      : undefined;
    return message;
  },
};

function createBaseWidgetScatterCurve(): WidgetScatterCurve {
  return { curve: undefined };
}

export const WidgetScatterCurve: MessageFns<WidgetScatterCurve> = {
  encode(message: WidgetScatterCurve, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.curve !== undefined) {
      WidgetCurve.encode(message.curve, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetScatterCurve {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetScatterCurve();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.curve = WidgetCurve.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetScatterCurve {
    return { curve: isSet(object.curve) ? WidgetCurve.fromJSON(object.curve) : undefined };
  },

  toJSON(message: WidgetScatterCurve): unknown {
    const obj: any = {};
    if (message.curve !== undefined) {
      obj.curve = WidgetCurve.toJSON(message.curve);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetScatterCurve>, I>>(base?: I): WidgetScatterCurve {
    return WidgetScatterCurve.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetScatterCurve>, I>>(object: I): WidgetScatterCurve {
    const message = createBaseWidgetScatterCurve();
    message.curve = (object.curve !== undefined && object.curve !== null)
      ? WidgetCurve.fromPartial(object.curve)
      : undefined;
    return message;
  },
};

function createBaseWidgetCalendar(): WidgetCalendar {
  return {
    calendarType: 0,
    weekBgColor: undefined,
    weekFontColor: undefined,
    todayBgColor: undefined,
    todayFontColor: undefined,
    highlightColor: undefined,
    highlightFontColor: undefined,
    highlightDateConfig: [],
  };
}

export const WidgetCalendar: MessageFns<WidgetCalendar> = {
  encode(message: WidgetCalendar, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.calendarType !== 0) {
      writer.uint32(8).int32(message.calendarType);
    }
    if (message.weekBgColor !== undefined) {
      ColorReference.encode(message.weekBgColor, writer.uint32(26).fork()).join();
    }
    if (message.weekFontColor !== undefined) {
      ColorReference.encode(message.weekFontColor, writer.uint32(34).fork()).join();
    }
    if (message.todayBgColor !== undefined) {
      ColorReference.encode(message.todayBgColor, writer.uint32(42).fork()).join();
    }
    if (message.todayFontColor !== undefined) {
      ColorReference.encode(message.todayFontColor, writer.uint32(50).fork()).join();
    }
    if (message.highlightColor !== undefined) {
      ColorReference.encode(message.highlightColor, writer.uint32(58).fork()).join();
    }
    if (message.highlightFontColor !== undefined) {
      ColorReference.encode(message.highlightFontColor, writer.uint32(66).fork()).join();
    }
    for (const v of message.highlightDateConfig) {
      WidgetCalendar_DateConfig.encode(v!, writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetCalendar {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetCalendar();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.calendarType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.weekBgColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.weekFontColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.todayBgColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.todayFontColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.highlightColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.highlightFontColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.highlightDateConfig.push(WidgetCalendar_DateConfig.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetCalendar {
    return {
      calendarType: isSet(object.calendarType) ? widgetCalendar_CalendarTypeFromJSON(object.calendarType) : 0,
      weekBgColor: isSet(object.weekBgColor) ? ColorReference.fromJSON(object.weekBgColor) : undefined,
      weekFontColor: isSet(object.weekFontColor) ? ColorReference.fromJSON(object.weekFontColor) : undefined,
      todayBgColor: isSet(object.todayBgColor) ? ColorReference.fromJSON(object.todayBgColor) : undefined,
      todayFontColor: isSet(object.todayFontColor) ? ColorReference.fromJSON(object.todayFontColor) : undefined,
      highlightColor: isSet(object.highlightColor) ? ColorReference.fromJSON(object.highlightColor) : undefined,
      highlightFontColor: isSet(object.highlightFontColor)
        ? ColorReference.fromJSON(object.highlightFontColor)
        : undefined,
      highlightDateConfig: globalThis.Array.isArray(object?.highlightDateConfig)
        ? object.highlightDateConfig.map((e: any) => WidgetCalendar_DateConfig.fromJSON(e))
        : [],
    };
  },

  toJSON(message: WidgetCalendar): unknown {
    const obj: any = {};
    if (message.calendarType !== 0) {
      obj.calendarType = widgetCalendar_CalendarTypeToJSON(message.calendarType);
    }
    if (message.weekBgColor !== undefined) {
      obj.weekBgColor = ColorReference.toJSON(message.weekBgColor);
    }
    if (message.weekFontColor !== undefined) {
      obj.weekFontColor = ColorReference.toJSON(message.weekFontColor);
    }
    if (message.todayBgColor !== undefined) {
      obj.todayBgColor = ColorReference.toJSON(message.todayBgColor);
    }
    if (message.todayFontColor !== undefined) {
      obj.todayFontColor = ColorReference.toJSON(message.todayFontColor);
    }
    if (message.highlightColor !== undefined) {
      obj.highlightColor = ColorReference.toJSON(message.highlightColor);
    }
    if (message.highlightFontColor !== undefined) {
      obj.highlightFontColor = ColorReference.toJSON(message.highlightFontColor);
    }
    if (message.highlightDateConfig?.length) {
      obj.highlightDateConfig = message.highlightDateConfig.map((e) => WidgetCalendar_DateConfig.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetCalendar>, I>>(base?: I): WidgetCalendar {
    return WidgetCalendar.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetCalendar>, I>>(object: I): WidgetCalendar {
    const message = createBaseWidgetCalendar();
    message.calendarType = object.calendarType ?? 0;
    message.weekBgColor = (object.weekBgColor !== undefined && object.weekBgColor !== null)
      ? ColorReference.fromPartial(object.weekBgColor)
      : undefined;
    message.weekFontColor = (object.weekFontColor !== undefined && object.weekFontColor !== null)
      ? ColorReference.fromPartial(object.weekFontColor)
      : undefined;
    message.todayBgColor = (object.todayBgColor !== undefined && object.todayBgColor !== null)
      ? ColorReference.fromPartial(object.todayBgColor)
      : undefined;
    message.todayFontColor = (object.todayFontColor !== undefined && object.todayFontColor !== null)
      ? ColorReference.fromPartial(object.todayFontColor)
      : undefined;
    message.highlightColor = (object.highlightColor !== undefined && object.highlightColor !== null)
      ? ColorReference.fromPartial(object.highlightColor)
      : undefined;
    message.highlightFontColor = (object.highlightFontColor !== undefined && object.highlightFontColor !== null)
      ? ColorReference.fromPartial(object.highlightFontColor)
      : undefined;
    message.highlightDateConfig = object.highlightDateConfig?.map((e) => WidgetCalendar_DateConfig.fromPartial(e)) ||
      [];
    return message;
  },
};

function createBaseWidgetCalendar_DateConfig(): WidgetCalendar_DateConfig {
  return { yearU16: 0, monthU8: 0, dayU8: 0 };
}

export const WidgetCalendar_DateConfig: MessageFns<WidgetCalendar_DateConfig> = {
  encode(message: WidgetCalendar_DateConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.yearU16 !== 0) {
      writer.uint32(8).uint32(message.yearU16);
    }
    if (message.monthU8 !== 0) {
      writer.uint32(16).uint32(message.monthU8);
    }
    if (message.dayU8 !== 0) {
      writer.uint32(24).uint32(message.dayU8);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetCalendar_DateConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetCalendar_DateConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.yearU16 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.monthU8 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.dayU8 = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetCalendar_DateConfig {
    return {
      yearU16: isSet(object.yearU16) ? globalThis.Number(object.yearU16) : 0,
      monthU8: isSet(object.monthU8) ? globalThis.Number(object.monthU8) : 0,
      dayU8: isSet(object.dayU8) ? globalThis.Number(object.dayU8) : 0,
    };
  },

  toJSON(message: WidgetCalendar_DateConfig): unknown {
    const obj: any = {};
    if (message.yearU16 !== 0) {
      obj.yearU16 = Math.round(message.yearU16);
    }
    if (message.monthU8 !== 0) {
      obj.monthU8 = Math.round(message.monthU8);
    }
    if (message.dayU8 !== 0) {
      obj.dayU8 = Math.round(message.dayU8);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetCalendar_DateConfig>, I>>(base?: I): WidgetCalendar_DateConfig {
    return WidgetCalendar_DateConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetCalendar_DateConfig>, I>>(object: I): WidgetCalendar_DateConfig {
    const message = createBaseWidgetCalendar_DateConfig();
    message.yearU16 = object.yearU16 ?? 0;
    message.monthU8 = object.monthU8 ?? 0;
    message.dayU8 = object.dayU8 ?? 0;
    return message;
  },
};

function createBaseWidgetOptionList(): WidgetOptionList {
  return { dataResourceType: 0, options: [], optionsValue: [], selectedColor: undefined, rowSpacingU8: 0 };
}

export const WidgetOptionList: MessageFns<WidgetOptionList> = {
  encode(message: WidgetOptionList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.dataResourceType !== 0) {
      writer.uint32(8).int32(message.dataResourceType);
    }
    for (const v of message.options) {
      writer.uint32(18).string(v!);
    }
    for (const v of message.optionsValue) {
      writer.uint32(26).string(v!);
    }
    if (message.selectedColor !== undefined) {
      ColorReference.encode(message.selectedColor, writer.uint32(42).fork()).join();
    }
    if (message.rowSpacingU8 !== 0) {
      writer.uint32(48).uint32(message.rowSpacingU8);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetOptionList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetOptionList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.dataResourceType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.options.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.optionsValue.push(reader.string());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.selectedColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.rowSpacingU8 = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetOptionList {
    return {
      dataResourceType: isSet(object.dataResourceType) ? listDataResourceTypeFromJSON(object.dataResourceType) : 0,
      options: globalThis.Array.isArray(object?.options) ? object.options.map((e: any) => globalThis.String(e)) : [],
      optionsValue: globalThis.Array.isArray(object?.optionsValue)
        ? object.optionsValue.map((e: any) => globalThis.String(e))
        : [],
      selectedColor: isSet(object.selectedColor) ? ColorReference.fromJSON(object.selectedColor) : undefined,
      rowSpacingU8: isSet(object.rowSpacingU8) ? globalThis.Number(object.rowSpacingU8) : 0,
    };
  },

  toJSON(message: WidgetOptionList): unknown {
    const obj: any = {};
    if (message.dataResourceType !== 0) {
      obj.dataResourceType = listDataResourceTypeToJSON(message.dataResourceType);
    }
    if (message.options?.length) {
      obj.options = message.options;
    }
    if (message.optionsValue?.length) {
      obj.optionsValue = message.optionsValue;
    }
    if (message.selectedColor !== undefined) {
      obj.selectedColor = ColorReference.toJSON(message.selectedColor);
    }
    if (message.rowSpacingU8 !== 0) {
      obj.rowSpacingU8 = Math.round(message.rowSpacingU8);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetOptionList>, I>>(base?: I): WidgetOptionList {
    return WidgetOptionList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetOptionList>, I>>(object: I): WidgetOptionList {
    const message = createBaseWidgetOptionList();
    message.dataResourceType = object.dataResourceType ?? 0;
    message.options = object.options?.map((e) => e) || [];
    message.optionsValue = object.optionsValue?.map((e) => e) || [];
    message.selectedColor = (object.selectedColor !== undefined && object.selectedColor !== null)
      ? ColorReference.fromPartial(object.selectedColor)
      : undefined;
    message.rowSpacingU8 = object.rowSpacingU8 ?? 0;
    return message;
  },
};

function createBaseWidgetRollerList(): WidgetRollerList {
  return { rollerListMode: 0, list: undefined, viewCountU8: 0, defIndexU8: 0 };
}

export const WidgetRollerList: MessageFns<WidgetRollerList> = {
  encode(message: WidgetRollerList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rollerListMode !== 0) {
      writer.uint32(8).int32(message.rollerListMode);
    }
    if (message.list !== undefined) {
      WidgetOptionList.encode(message.list, writer.uint32(18).fork()).join();
    }
    if (message.viewCountU8 !== 0) {
      writer.uint32(24).uint32(message.viewCountU8);
    }
    if (message.defIndexU8 !== 0) {
      writer.uint32(32).uint32(message.defIndexU8);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetRollerList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetRollerList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.rollerListMode = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.list = WidgetOptionList.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.viewCountU8 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.defIndexU8 = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetRollerList {
    return {
      rollerListMode: isSet(object.rollerListMode) ? widgetRollerList_RollerListModeFromJSON(object.rollerListMode) : 0,
      list: isSet(object.list) ? WidgetOptionList.fromJSON(object.list) : undefined,
      viewCountU8: isSet(object.viewCountU8) ? globalThis.Number(object.viewCountU8) : 0,
      defIndexU8: isSet(object.defIndexU8) ? globalThis.Number(object.defIndexU8) : 0,
    };
  },

  toJSON(message: WidgetRollerList): unknown {
    const obj: any = {};
    if (message.rollerListMode !== 0) {
      obj.rollerListMode = widgetRollerList_RollerListModeToJSON(message.rollerListMode);
    }
    if (message.list !== undefined) {
      obj.list = WidgetOptionList.toJSON(message.list);
    }
    if (message.viewCountU8 !== 0) {
      obj.viewCountU8 = Math.round(message.viewCountU8);
    }
    if (message.defIndexU8 !== 0) {
      obj.defIndexU8 = Math.round(message.defIndexU8);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetRollerList>, I>>(base?: I): WidgetRollerList {
    return WidgetRollerList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetRollerList>, I>>(object: I): WidgetRollerList {
    const message = createBaseWidgetRollerList();
    message.rollerListMode = object.rollerListMode ?? 0;
    message.list = (object.list !== undefined && object.list !== null)
      ? WidgetOptionList.fromPartial(object.list)
      : undefined;
    message.viewCountU8 = object.viewCountU8 ?? 0;
    message.defIndexU8 = object.defIndexU8 ?? 0;
    return message;
  },
};

function createBaseWidgetDropList(): WidgetDropList {
  return { direction: 0, list: undefined, listBgColor: undefined, dropListSymbol: 0, alwaysOpen: false };
}

export const WidgetDropList: MessageFns<WidgetDropList> = {
  encode(message: WidgetDropList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.direction !== 0) {
      writer.uint32(8).int32(message.direction);
    }
    if (message.list !== undefined) {
      WidgetOptionList.encode(message.list, writer.uint32(18).fork()).join();
    }
    if (message.listBgColor !== undefined) {
      ColorReference.encode(message.listBgColor, writer.uint32(26).fork()).join();
    }
    if (message.dropListSymbol !== 0) {
      writer.uint32(32).int32(message.dropListSymbol);
    }
    if (message.alwaysOpen !== false) {
      writer.uint32(40).bool(message.alwaysOpen);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetDropList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetDropList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.direction = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.list = WidgetOptionList.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.listBgColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.dropListSymbol = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.alwaysOpen = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetDropList {
    return {
      direction: isSet(object.direction) ? directionFromJSON(object.direction) : 0,
      list: isSet(object.list) ? WidgetOptionList.fromJSON(object.list) : undefined,
      listBgColor: isSet(object.listBgColor) ? ColorReference.fromJSON(object.listBgColor) : undefined,
      dropListSymbol: isSet(object.dropListSymbol) ? symbolTypeFromJSON(object.dropListSymbol) : 0,
      alwaysOpen: isSet(object.alwaysOpen) ? globalThis.Boolean(object.alwaysOpen) : false,
    };
  },

  toJSON(message: WidgetDropList): unknown {
    const obj: any = {};
    if (message.direction !== 0) {
      obj.direction = directionToJSON(message.direction);
    }
    if (message.list !== undefined) {
      obj.list = WidgetOptionList.toJSON(message.list);
    }
    if (message.listBgColor !== undefined) {
      obj.listBgColor = ColorReference.toJSON(message.listBgColor);
    }
    if (message.dropListSymbol !== 0) {
      obj.dropListSymbol = symbolTypeToJSON(message.dropListSymbol);
    }
    if (message.alwaysOpen !== false) {
      obj.alwaysOpen = message.alwaysOpen;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetDropList>, I>>(base?: I): WidgetDropList {
    return WidgetDropList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetDropList>, I>>(object: I): WidgetDropList {
    const message = createBaseWidgetDropList();
    message.direction = object.direction ?? 0;
    message.list = (object.list !== undefined && object.list !== null)
      ? WidgetOptionList.fromPartial(object.list)
      : undefined;
    message.listBgColor = (object.listBgColor !== undefined && object.listBgColor !== null)
      ? ColorReference.fromPartial(object.listBgColor)
      : undefined;
    message.dropListSymbol = object.dropListSymbol ?? 0;
    message.alwaysOpen = object.alwaysOpen ?? false;
    return message;
  },
};

function createBaseWidgetMeter(): WidgetMeter {
  return {
    meterDirection: 0,
    startU16: 0,
    endU16: 0,
    lowerLimitU16: 0,
    upperLimitU16: 0,
    limitWidthU8: 0,
    limitRadiusU8: 0,
    lowerLimitColor: undefined,
    upperLimitColor: undefined,
    middleColor: undefined,
    scaleValueConfig: undefined,
    scaleLabelColor: undefined,
    decimalPlacesU8: 0,
    labelRadiusU8: 0,
    pointerConfig: undefined,
    pointerPointConfig: undefined,
  };
}

export const WidgetMeter: MessageFns<WidgetMeter> = {
  encode(message: WidgetMeter, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.meterDirection !== 0) {
      writer.uint32(8).int32(message.meterDirection);
    }
    if (message.startU16 !== 0) {
      writer.uint32(16).uint32(message.startU16);
    }
    if (message.endU16 !== 0) {
      writer.uint32(24).uint32(message.endU16);
    }
    if (message.lowerLimitU16 !== 0) {
      writer.uint32(32).uint32(message.lowerLimitU16);
    }
    if (message.upperLimitU16 !== 0) {
      writer.uint32(40).uint32(message.upperLimitU16);
    }
    if (message.limitWidthU8 !== 0) {
      writer.uint32(48).uint32(message.limitWidthU8);
    }
    if (message.limitRadiusU8 !== 0) {
      writer.uint32(56).uint32(message.limitRadiusU8);
    }
    if (message.lowerLimitColor !== undefined) {
      ColorReference.encode(message.lowerLimitColor, writer.uint32(66).fork()).join();
    }
    if (message.upperLimitColor !== undefined) {
      ColorReference.encode(message.upperLimitColor, writer.uint32(74).fork()).join();
    }
    if (message.middleColor !== undefined) {
      ColorReference.encode(message.middleColor, writer.uint32(82).fork()).join();
    }
    if (message.scaleValueConfig !== undefined) {
      ScaleValueConfig.encode(message.scaleValueConfig, writer.uint32(90).fork()).join();
    }
    if (message.scaleLabelColor !== undefined) {
      ColorReference.encode(message.scaleLabelColor, writer.uint32(98).fork()).join();
    }
    if (message.decimalPlacesU8 !== 0) {
      writer.uint32(104).uint32(message.decimalPlacesU8);
    }
    if (message.labelRadiusU8 !== 0) {
      writer.uint32(112).uint32(message.labelRadiusU8);
    }
    if (message.pointerConfig !== undefined) {
      LineConfig.encode(message.pointerConfig, writer.uint32(122).fork()).join();
    }
    if (message.pointerPointConfig !== undefined) {
      PointConfig.encode(message.pointerPointConfig, writer.uint32(130).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetMeter {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetMeter();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.meterDirection = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.startU16 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.endU16 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.lowerLimitU16 = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.upperLimitU16 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.limitWidthU8 = reader.uint32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.limitRadiusU8 = reader.uint32();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.lowerLimitColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.upperLimitColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.middleColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.scaleValueConfig = ScaleValueConfig.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.scaleLabelColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.decimalPlacesU8 = reader.uint32();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.labelRadiusU8 = reader.uint32();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.pointerConfig = LineConfig.decode(reader, reader.uint32());
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.pointerPointConfig = PointConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetMeter {
    return {
      meterDirection: isSet(object.meterDirection) ? widgetMeter_MeterDirectionFromJSON(object.meterDirection) : 0,
      startU16: isSet(object.startU16) ? globalThis.Number(object.startU16) : 0,
      endU16: isSet(object.endU16) ? globalThis.Number(object.endU16) : 0,
      lowerLimitU16: isSet(object.lowerLimitU16) ? globalThis.Number(object.lowerLimitU16) : 0,
      upperLimitU16: isSet(object.upperLimitU16) ? globalThis.Number(object.upperLimitU16) : 0,
      limitWidthU8: isSet(object.limitWidthU8) ? globalThis.Number(object.limitWidthU8) : 0,
      limitRadiusU8: isSet(object.limitRadiusU8) ? globalThis.Number(object.limitRadiusU8) : 0,
      lowerLimitColor: isSet(object.lowerLimitColor) ? ColorReference.fromJSON(object.lowerLimitColor) : undefined,
      upperLimitColor: isSet(object.upperLimitColor) ? ColorReference.fromJSON(object.upperLimitColor) : undefined,
      middleColor: isSet(object.middleColor) ? ColorReference.fromJSON(object.middleColor) : undefined,
      scaleValueConfig: isSet(object.scaleValueConfig) ? ScaleValueConfig.fromJSON(object.scaleValueConfig) : undefined,
      scaleLabelColor: isSet(object.scaleLabelColor) ? ColorReference.fromJSON(object.scaleLabelColor) : undefined,
      decimalPlacesU8: isSet(object.decimalPlacesU8) ? globalThis.Number(object.decimalPlacesU8) : 0,
      labelRadiusU8: isSet(object.labelRadiusU8) ? globalThis.Number(object.labelRadiusU8) : 0,
      pointerConfig: isSet(object.pointerConfig) ? LineConfig.fromJSON(object.pointerConfig) : undefined,
      pointerPointConfig: isSet(object.pointerPointConfig)
        ? PointConfig.fromJSON(object.pointerPointConfig)
        : undefined,
    };
  },

  toJSON(message: WidgetMeter): unknown {
    const obj: any = {};
    if (message.meterDirection !== 0) {
      obj.meterDirection = widgetMeter_MeterDirectionToJSON(message.meterDirection);
    }
    if (message.startU16 !== 0) {
      obj.startU16 = Math.round(message.startU16);
    }
    if (message.endU16 !== 0) {
      obj.endU16 = Math.round(message.endU16);
    }
    if (message.lowerLimitU16 !== 0) {
      obj.lowerLimitU16 = Math.round(message.lowerLimitU16);
    }
    if (message.upperLimitU16 !== 0) {
      obj.upperLimitU16 = Math.round(message.upperLimitU16);
    }
    if (message.limitWidthU8 !== 0) {
      obj.limitWidthU8 = Math.round(message.limitWidthU8);
    }
    if (message.limitRadiusU8 !== 0) {
      obj.limitRadiusU8 = Math.round(message.limitRadiusU8);
    }
    if (message.lowerLimitColor !== undefined) {
      obj.lowerLimitColor = ColorReference.toJSON(message.lowerLimitColor);
    }
    if (message.upperLimitColor !== undefined) {
      obj.upperLimitColor = ColorReference.toJSON(message.upperLimitColor);
    }
    if (message.middleColor !== undefined) {
      obj.middleColor = ColorReference.toJSON(message.middleColor);
    }
    if (message.scaleValueConfig !== undefined) {
      obj.scaleValueConfig = ScaleValueConfig.toJSON(message.scaleValueConfig);
    }
    if (message.scaleLabelColor !== undefined) {
      obj.scaleLabelColor = ColorReference.toJSON(message.scaleLabelColor);
    }
    if (message.decimalPlacesU8 !== 0) {
      obj.decimalPlacesU8 = Math.round(message.decimalPlacesU8);
    }
    if (message.labelRadiusU8 !== 0) {
      obj.labelRadiusU8 = Math.round(message.labelRadiusU8);
    }
    if (message.pointerConfig !== undefined) {
      obj.pointerConfig = LineConfig.toJSON(message.pointerConfig);
    }
    if (message.pointerPointConfig !== undefined) {
      obj.pointerPointConfig = PointConfig.toJSON(message.pointerPointConfig);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetMeter>, I>>(base?: I): WidgetMeter {
    return WidgetMeter.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetMeter>, I>>(object: I): WidgetMeter {
    const message = createBaseWidgetMeter();
    message.meterDirection = object.meterDirection ?? 0;
    message.startU16 = object.startU16 ?? 0;
    message.endU16 = object.endU16 ?? 0;
    message.lowerLimitU16 = object.lowerLimitU16 ?? 0;
    message.upperLimitU16 = object.upperLimitU16 ?? 0;
    message.limitWidthU8 = object.limitWidthU8 ?? 0;
    message.limitRadiusU8 = object.limitRadiusU8 ?? 0;
    message.lowerLimitColor = (object.lowerLimitColor !== undefined && object.lowerLimitColor !== null)
      ? ColorReference.fromPartial(object.lowerLimitColor)
      : undefined;
    message.upperLimitColor = (object.upperLimitColor !== undefined && object.upperLimitColor !== null)
      ? ColorReference.fromPartial(object.upperLimitColor)
      : undefined;
    message.middleColor = (object.middleColor !== undefined && object.middleColor !== null)
      ? ColorReference.fromPartial(object.middleColor)
      : undefined;
    message.scaleValueConfig = (object.scaleValueConfig !== undefined && object.scaleValueConfig !== null)
      ? ScaleValueConfig.fromPartial(object.scaleValueConfig)
      : undefined;
    message.scaleLabelColor = (object.scaleLabelColor !== undefined && object.scaleLabelColor !== null)
      ? ColorReference.fromPartial(object.scaleLabelColor)
      : undefined;
    message.decimalPlacesU8 = object.decimalPlacesU8 ?? 0;
    message.labelRadiusU8 = object.labelRadiusU8 ?? 0;
    message.pointerConfig = (object.pointerConfig !== undefined && object.pointerConfig !== null)
      ? LineConfig.fromPartial(object.pointerConfig)
      : undefined;
    message.pointerPointConfig = (object.pointerPointConfig !== undefined && object.pointerPointConfig !== null)
      ? PointConfig.fromPartial(object.pointerPointConfig)
      : undefined;
    return message;
  },
};

function createBaseDataExportConfig(): DataExportConfig {
  return { fileType: 0, storageType: 0, exportType: 0 };
}

export const DataExportConfig: MessageFns<DataExportConfig> = {
  encode(message: DataExportConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fileType !== 0) {
      writer.uint32(8).int32(message.fileType);
    }
    if (message.storageType !== 0) {
      writer.uint32(16).int32(message.storageType);
    }
    if (message.exportType !== 0) {
      writer.uint32(24).int32(message.exportType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataExportConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataExportConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.fileType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.storageType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.exportType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataExportConfig {
    return {
      fileType: isSet(object.fileType) ? dataFileTypeFromJSON(object.fileType) : 0,
      storageType: isSet(object.storageType) ? saveLocationFromJSON(object.storageType) : 0,
      exportType: isSet(object.exportType) ? dataExportTypeFromJSON(object.exportType) : 0,
    };
  },

  toJSON(message: DataExportConfig): unknown {
    const obj: any = {};
    if (message.fileType !== 0) {
      obj.fileType = dataFileTypeToJSON(message.fileType);
    }
    if (message.storageType !== 0) {
      obj.storageType = saveLocationToJSON(message.storageType);
    }
    if (message.exportType !== 0) {
      obj.exportType = dataExportTypeToJSON(message.exportType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataExportConfig>, I>>(base?: I): DataExportConfig {
    return DataExportConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataExportConfig>, I>>(object: I): DataExportConfig {
    const message = createBaseDataExportConfig();
    message.fileType = object.fileType ?? 0;
    message.storageType = object.storageType ?? 0;
    message.exportType = object.exportType ?? 0;
    return message;
  },
};

function createBaseScaleValueConfig(): ScaleValueConfig {
  return {
    show: false,
    scaleShowType: 0,
    gridShowType: 0,
    scaleMain: undefined,
    scaleSec: undefined,
    scaleRadiusU8: 0,
    minValue: undefined,
    maxValue: undefined,
    gridLineConfig: undefined,
  };
}

export const ScaleValueConfig: MessageFns<ScaleValueConfig> = {
  encode(message: ScaleValueConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.show !== false) {
      writer.uint32(8).bool(message.show);
    }
    if (message.scaleShowType !== 0) {
      writer.uint32(16).int32(message.scaleShowType);
    }
    if (message.gridShowType !== 0) {
      writer.uint32(24).int32(message.gridShowType);
    }
    if (message.scaleMain !== undefined) {
      ScaleValueConfig_Scale.encode(message.scaleMain, writer.uint32(34).fork()).join();
    }
    if (message.scaleSec !== undefined) {
      ScaleValueConfig_Scale.encode(message.scaleSec, writer.uint32(42).fork()).join();
    }
    if (message.scaleRadiusU8 !== 0) {
      writer.uint32(80).uint32(message.scaleRadiusU8);
    }
    if (message.minValue !== undefined) {
      NumberValue.encode(message.minValue, writer.uint32(90).fork()).join();
    }
    if (message.maxValue !== undefined) {
      NumberValue.encode(message.maxValue, writer.uint32(98).fork()).join();
    }
    if (message.gridLineConfig !== undefined) {
      LineConfig.encode(message.gridLineConfig, writer.uint32(122).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ScaleValueConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseScaleValueConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.show = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.scaleShowType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.gridShowType = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.scaleMain = ScaleValueConfig_Scale.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.scaleSec = ScaleValueConfig_Scale.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.scaleRadiusU8 = reader.uint32();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.minValue = NumberValue.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.maxValue = NumberValue.decode(reader, reader.uint32());
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.gridLineConfig = LineConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ScaleValueConfig {
    return {
      show: isSet(object.show) ? globalThis.Boolean(object.show) : false,
      scaleShowType: isSet(object.scaleShowType) ? scaleValueConfig_ScaleShowTypeFromJSON(object.scaleShowType) : 0,
      gridShowType: isSet(object.gridShowType) ? scaleValueConfig_GridShowTypeFromJSON(object.gridShowType) : 0,
      scaleMain: isSet(object.scaleMain) ? ScaleValueConfig_Scale.fromJSON(object.scaleMain) : undefined,
      scaleSec: isSet(object.scaleSec) ? ScaleValueConfig_Scale.fromJSON(object.scaleSec) : undefined,
      scaleRadiusU8: isSet(object.scaleRadiusU8) ? globalThis.Number(object.scaleRadiusU8) : 0,
      minValue: isSet(object.minValue) ? NumberValue.fromJSON(object.minValue) : undefined,
      maxValue: isSet(object.maxValue) ? NumberValue.fromJSON(object.maxValue) : undefined,
      gridLineConfig: isSet(object.gridLineConfig) ? LineConfig.fromJSON(object.gridLineConfig) : undefined,
    };
  },

  toJSON(message: ScaleValueConfig): unknown {
    const obj: any = {};
    if (message.show !== false) {
      obj.show = message.show;
    }
    if (message.scaleShowType !== 0) {
      obj.scaleShowType = scaleValueConfig_ScaleShowTypeToJSON(message.scaleShowType);
    }
    if (message.gridShowType !== 0) {
      obj.gridShowType = scaleValueConfig_GridShowTypeToJSON(message.gridShowType);
    }
    if (message.scaleMain !== undefined) {
      obj.scaleMain = ScaleValueConfig_Scale.toJSON(message.scaleMain);
    }
    if (message.scaleSec !== undefined) {
      obj.scaleSec = ScaleValueConfig_Scale.toJSON(message.scaleSec);
    }
    if (message.scaleRadiusU8 !== 0) {
      obj.scaleRadiusU8 = Math.round(message.scaleRadiusU8);
    }
    if (message.minValue !== undefined) {
      obj.minValue = NumberValue.toJSON(message.minValue);
    }
    if (message.maxValue !== undefined) {
      obj.maxValue = NumberValue.toJSON(message.maxValue);
    }
    if (message.gridLineConfig !== undefined) {
      obj.gridLineConfig = LineConfig.toJSON(message.gridLineConfig);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ScaleValueConfig>, I>>(base?: I): ScaleValueConfig {
    return ScaleValueConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ScaleValueConfig>, I>>(object: I): ScaleValueConfig {
    const message = createBaseScaleValueConfig();
    message.show = object.show ?? false;
    message.scaleShowType = object.scaleShowType ?? 0;
    message.gridShowType = object.gridShowType ?? 0;
    message.scaleMain = (object.scaleMain !== undefined && object.scaleMain !== null)
      ? ScaleValueConfig_Scale.fromPartial(object.scaleMain)
      : undefined;
    message.scaleSec = (object.scaleSec !== undefined && object.scaleSec !== null)
      ? ScaleValueConfig_Scale.fromPartial(object.scaleSec)
      : undefined;
    message.scaleRadiusU8 = object.scaleRadiusU8 ?? 0;
    message.minValue = (object.minValue !== undefined && object.minValue !== null)
      ? NumberValue.fromPartial(object.minValue)
      : undefined;
    message.maxValue = (object.maxValue !== undefined && object.maxValue !== null)
      ? NumberValue.fromPartial(object.maxValue)
      : undefined;
    message.gridLineConfig = (object.gridLineConfig !== undefined && object.gridLineConfig !== null)
      ? LineConfig.fromPartial(object.gridLineConfig)
      : undefined;
    return message;
  },
};

function createBaseScaleValueConfig_Scale(): ScaleValueConfig_Scale {
  return { scaleCountU8: 0, scaleWidthU8: 0, scaleDrawLen: 0, color: undefined };
}

export const ScaleValueConfig_Scale: MessageFns<ScaleValueConfig_Scale> = {
  encode(message: ScaleValueConfig_Scale, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.scaleCountU8 !== 0) {
      writer.uint32(32).uint32(message.scaleCountU8);
    }
    if (message.scaleWidthU8 !== 0) {
      writer.uint32(48).uint32(message.scaleWidthU8);
    }
    if (message.scaleDrawLen !== 0) {
      writer.uint32(64).uint32(message.scaleDrawLen);
    }
    if (message.color !== undefined) {
      ColorReference.encode(message.color, writer.uint32(106).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ScaleValueConfig_Scale {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseScaleValueConfig_Scale();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.scaleCountU8 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.scaleWidthU8 = reader.uint32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.scaleDrawLen = reader.uint32();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.color = ColorReference.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ScaleValueConfig_Scale {
    return {
      scaleCountU8: isSet(object.scaleCountU8) ? globalThis.Number(object.scaleCountU8) : 0,
      scaleWidthU8: isSet(object.scaleWidthU8) ? globalThis.Number(object.scaleWidthU8) : 0,
      scaleDrawLen: isSet(object.scaleDrawLen) ? globalThis.Number(object.scaleDrawLen) : 0,
      color: isSet(object.color) ? ColorReference.fromJSON(object.color) : undefined,
    };
  },

  toJSON(message: ScaleValueConfig_Scale): unknown {
    const obj: any = {};
    if (message.scaleCountU8 !== 0) {
      obj.scaleCountU8 = Math.round(message.scaleCountU8);
    }
    if (message.scaleWidthU8 !== 0) {
      obj.scaleWidthU8 = Math.round(message.scaleWidthU8);
    }
    if (message.scaleDrawLen !== 0) {
      obj.scaleDrawLen = Math.round(message.scaleDrawLen);
    }
    if (message.color !== undefined) {
      obj.color = ColorReference.toJSON(message.color);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ScaleValueConfig_Scale>, I>>(base?: I): ScaleValueConfig_Scale {
    return ScaleValueConfig_Scale.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ScaleValueConfig_Scale>, I>>(object: I): ScaleValueConfig_Scale {
    const message = createBaseScaleValueConfig_Scale();
    message.scaleCountU8 = object.scaleCountU8 ?? 0;
    message.scaleWidthU8 = object.scaleWidthU8 ?? 0;
    message.scaleDrawLen = object.scaleDrawLen ?? 0;
    message.color = (object.color !== undefined && object.color !== null)
      ? ColorReference.fromPartial(object.color)
      : undefined;
    return message;
  },
};

function createBasePointConfig(): PointConfig {
  return { pointType: 0, radiusU8: 0, widthU8: 0, heightU8: 0, style: undefined };
}

export const PointConfig: MessageFns<PointConfig> = {
  encode(message: PointConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pointType !== 0) {
      writer.uint32(8).int32(message.pointType);
    }
    if (message.radiusU8 !== 0) {
      writer.uint32(16).uint32(message.radiusU8);
    }
    if (message.widthU8 !== 0) {
      writer.uint32(24).uint32(message.widthU8);
    }
    if (message.heightU8 !== 0) {
      writer.uint32(32).uint32(message.heightU8);
    }
    if (message.style !== undefined) {
      StyleProperties.encode(message.style, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PointConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePointConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pointType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.radiusU8 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.widthU8 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.heightU8 = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.style = StyleProperties.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PointConfig {
    return {
      pointType: isSet(object.pointType) ? pointTypeFromJSON(object.pointType) : 0,
      radiusU8: isSet(object.radiusU8) ? globalThis.Number(object.radiusU8) : 0,
      widthU8: isSet(object.widthU8) ? globalThis.Number(object.widthU8) : 0,
      heightU8: isSet(object.heightU8) ? globalThis.Number(object.heightU8) : 0,
      style: isSet(object.style) ? StyleProperties.fromJSON(object.style) : undefined,
    };
  },

  toJSON(message: PointConfig): unknown {
    const obj: any = {};
    if (message.pointType !== 0) {
      obj.pointType = pointTypeToJSON(message.pointType);
    }
    if (message.radiusU8 !== 0) {
      obj.radiusU8 = Math.round(message.radiusU8);
    }
    if (message.widthU8 !== 0) {
      obj.widthU8 = Math.round(message.widthU8);
    }
    if (message.heightU8 !== 0) {
      obj.heightU8 = Math.round(message.heightU8);
    }
    if (message.style !== undefined) {
      obj.style = StyleProperties.toJSON(message.style);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PointConfig>, I>>(base?: I): PointConfig {
    return PointConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PointConfig>, I>>(object: I): PointConfig {
    const message = createBasePointConfig();
    message.pointType = object.pointType ?? 0;
    message.radiusU8 = object.radiusU8 ?? 0;
    message.widthU8 = object.widthU8 ?? 0;
    message.heightU8 = object.heightU8 ?? 0;
    message.style = (object.style !== undefined && object.style !== null)
      ? StyleProperties.fromPartial(object.style)
      : undefined;
    return message;
  },
};

function createBaseLineConfig(): LineConfig {
  return {
    lineType: 0,
    roundStart: false,
    roundEnd: false,
    dashGapU8: 0,
    dashWidthU8: 0,
    widthU8: 0,
    lengthU8: 0,
    color: undefined,
  };
}

export const LineConfig: MessageFns<LineConfig> = {
  encode(message: LineConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.lineType !== 0) {
      writer.uint32(8).int32(message.lineType);
    }
    if (message.roundStart !== false) {
      writer.uint32(16).bool(message.roundStart);
    }
    if (message.roundEnd !== false) {
      writer.uint32(24).bool(message.roundEnd);
    }
    if (message.dashGapU8 !== 0) {
      writer.uint32(32).uint32(message.dashGapU8);
    }
    if (message.dashWidthU8 !== 0) {
      writer.uint32(40).uint32(message.dashWidthU8);
    }
    if (message.widthU8 !== 0) {
      writer.uint32(48).uint32(message.widthU8);
    }
    if (message.lengthU8 !== 0) {
      writer.uint32(56).uint32(message.lengthU8);
    }
    if (message.color !== undefined) {
      ColorReference.encode(message.color, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LineConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLineConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.lineType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.roundStart = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.roundEnd = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.dashGapU8 = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.dashWidthU8 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.widthU8 = reader.uint32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.lengthU8 = reader.uint32();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.color = ColorReference.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LineConfig {
    return {
      lineType: isSet(object.lineType) ? lineTypeFromJSON(object.lineType) : 0,
      roundStart: isSet(object.roundStart) ? globalThis.Boolean(object.roundStart) : false,
      roundEnd: isSet(object.roundEnd) ? globalThis.Boolean(object.roundEnd) : false,
      dashGapU8: isSet(object.dashGapU8) ? globalThis.Number(object.dashGapU8) : 0,
      dashWidthU8: isSet(object.dashWidthU8) ? globalThis.Number(object.dashWidthU8) : 0,
      widthU8: isSet(object.widthU8) ? globalThis.Number(object.widthU8) : 0,
      lengthU8: isSet(object.lengthU8) ? globalThis.Number(object.lengthU8) : 0,
      color: isSet(object.color) ? ColorReference.fromJSON(object.color) : undefined,
    };
  },

  toJSON(message: LineConfig): unknown {
    const obj: any = {};
    if (message.lineType !== 0) {
      obj.lineType = lineTypeToJSON(message.lineType);
    }
    if (message.roundStart !== false) {
      obj.roundStart = message.roundStart;
    }
    if (message.roundEnd !== false) {
      obj.roundEnd = message.roundEnd;
    }
    if (message.dashGapU8 !== 0) {
      obj.dashGapU8 = Math.round(message.dashGapU8);
    }
    if (message.dashWidthU8 !== 0) {
      obj.dashWidthU8 = Math.round(message.dashWidthU8);
    }
    if (message.widthU8 !== 0) {
      obj.widthU8 = Math.round(message.widthU8);
    }
    if (message.lengthU8 !== 0) {
      obj.lengthU8 = Math.round(message.lengthU8);
    }
    if (message.color !== undefined) {
      obj.color = ColorReference.toJSON(message.color);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LineConfig>, I>>(base?: I): LineConfig {
    return LineConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LineConfig>, I>>(object: I): LineConfig {
    const message = createBaseLineConfig();
    message.lineType = object.lineType ?? 0;
    message.roundStart = object.roundStart ?? false;
    message.roundEnd = object.roundEnd ?? false;
    message.dashGapU8 = object.dashGapU8 ?? 0;
    message.dashWidthU8 = object.dashWidthU8 ?? 0;
    message.widthU8 = object.widthU8 ?? 0;
    message.lengthU8 = object.lengthU8 ?? 0;
    message.color = (object.color !== undefined && object.color !== null)
      ? ColorReference.fromPartial(object.color)
      : undefined;
    return message;
  },
};

function createBaseBarConfig(): BarConfig {
  return { roundU8: 0, widthU8: 0, style: undefined };
}

export const BarConfig: MessageFns<BarConfig> = {
  encode(message: BarConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.roundU8 !== 0) {
      writer.uint32(8).uint32(message.roundU8);
    }
    if (message.widthU8 !== 0) {
      writer.uint32(16).uint32(message.widthU8);
    }
    if (message.style !== undefined) {
      StyleProperties.encode(message.style, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BarConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBarConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.roundU8 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.widthU8 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.style = StyleProperties.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BarConfig {
    return {
      roundU8: isSet(object.roundU8) ? globalThis.Number(object.roundU8) : 0,
      widthU8: isSet(object.widthU8) ? globalThis.Number(object.widthU8) : 0,
      style: isSet(object.style) ? StyleProperties.fromJSON(object.style) : undefined,
    };
  },

  toJSON(message: BarConfig): unknown {
    const obj: any = {};
    if (message.roundU8 !== 0) {
      obj.roundU8 = Math.round(message.roundU8);
    }
    if (message.widthU8 !== 0) {
      obj.widthU8 = Math.round(message.widthU8);
    }
    if (message.style !== undefined) {
      obj.style = StyleProperties.toJSON(message.style);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BarConfig>, I>>(base?: I): BarConfig {
    return BarConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BarConfig>, I>>(object: I): BarConfig {
    const message = createBaseBarConfig();
    message.roundU8 = object.roundU8 ?? 0;
    message.widthU8 = object.widthU8 ?? 0;
    message.style = (object.style !== undefined && object.style !== null)
      ? StyleProperties.fromPartial(object.style)
      : undefined;
    return message;
  },
};

function createBaseWidgetRuler(): WidgetRuler {
  return {
    rulerType: 0,
    direction: 0,
    scaleConfig: undefined,
    mainLine: undefined,
    radiusU16: 0,
    currentValue: undefined,
    points: [],
  };
}

export const WidgetRuler: MessageFns<WidgetRuler> = {
  encode(message: WidgetRuler, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rulerType !== 0) {
      writer.uint32(8).int32(message.rulerType);
    }
    if (message.direction !== 0) {
      writer.uint32(16).int32(message.direction);
    }
    if (message.scaleConfig !== undefined) {
      ScaleValueConfig.encode(message.scaleConfig, writer.uint32(26).fork()).join();
    }
    if (message.mainLine !== undefined) {
      LineConfig.encode(message.mainLine, writer.uint32(34).fork()).join();
    }
    if (message.radiusU16 !== 0) {
      writer.uint32(40).uint32(message.radiusU16);
    }
    if (message.currentValue !== undefined) {
      NumberValue.encode(message.currentValue, writer.uint32(50).fork()).join();
    }
    for (const v of message.points) {
      Point.encode(v!, writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetRuler {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetRuler();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.rulerType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.direction = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.scaleConfig = ScaleValueConfig.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.mainLine = LineConfig.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.radiusU16 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.currentValue = NumberValue.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.points.push(Point.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetRuler {
    return {
      rulerType: isSet(object.rulerType) ? widgetRuler_RulerTypeFromJSON(object.rulerType) : 0,
      direction: isSet(object.direction) ? widgetRuler_RulerDirectionFromJSON(object.direction) : 0,
      scaleConfig: isSet(object.scaleConfig) ? ScaleValueConfig.fromJSON(object.scaleConfig) : undefined,
      mainLine: isSet(object.mainLine) ? LineConfig.fromJSON(object.mainLine) : undefined,
      radiusU16: isSet(object.radiusU16) ? globalThis.Number(object.radiusU16) : 0,
      currentValue: isSet(object.currentValue) ? NumberValue.fromJSON(object.currentValue) : undefined,
      points: globalThis.Array.isArray(object?.points) ? object.points.map((e: any) => Point.fromJSON(e)) : [],
    };
  },

  toJSON(message: WidgetRuler): unknown {
    const obj: any = {};
    if (message.rulerType !== 0) {
      obj.rulerType = widgetRuler_RulerTypeToJSON(message.rulerType);
    }
    if (message.direction !== 0) {
      obj.direction = widgetRuler_RulerDirectionToJSON(message.direction);
    }
    if (message.scaleConfig !== undefined) {
      obj.scaleConfig = ScaleValueConfig.toJSON(message.scaleConfig);
    }
    if (message.mainLine !== undefined) {
      obj.mainLine = LineConfig.toJSON(message.mainLine);
    }
    if (message.radiusU16 !== 0) {
      obj.radiusU16 = Math.round(message.radiusU16);
    }
    if (message.currentValue !== undefined) {
      obj.currentValue = NumberValue.toJSON(message.currentValue);
    }
    if (message.points?.length) {
      obj.points = message.points.map((e) => Point.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetRuler>, I>>(base?: I): WidgetRuler {
    return WidgetRuler.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetRuler>, I>>(object: I): WidgetRuler {
    const message = createBaseWidgetRuler();
    message.rulerType = object.rulerType ?? 0;
    message.direction = object.direction ?? 0;
    message.scaleConfig = (object.scaleConfig !== undefined && object.scaleConfig !== null)
      ? ScaleValueConfig.fromPartial(object.scaleConfig)
      : undefined;
    message.mainLine = (object.mainLine !== undefined && object.mainLine !== null)
      ? LineConfig.fromPartial(object.mainLine)
      : undefined;
    message.radiusU16 = object.radiusU16 ?? 0;
    message.currentValue = (object.currentValue !== undefined && object.currentValue !== null)
      ? NumberValue.fromPartial(object.currentValue)
      : undefined;
    message.points = object.points?.map((e) => Point.fromPartial(e)) || [];
    return message;
  },
};

function createBaseWidgetRectAngle(): WidgetRectAngle {
  return { chamferType: 0, chamferRadiusU8: 0, line: undefined };
}

export const WidgetRectAngle: MessageFns<WidgetRectAngle> = {
  encode(message: WidgetRectAngle, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.chamferType !== 0) {
      writer.uint32(8).int32(message.chamferType);
    }
    if (message.chamferRadiusU8 !== 0) {
      writer.uint32(16).uint32(message.chamferRadiusU8);
    }
    if (message.line !== undefined) {
      LineConfig.encode(message.line, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetRectAngle {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetRectAngle();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.chamferType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.chamferRadiusU8 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.line = LineConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetRectAngle {
    return {
      chamferType: isSet(object.chamferType) ? widgetRectAngle_ChamferTypeFromJSON(object.chamferType) : 0,
      chamferRadiusU8: isSet(object.chamferRadiusU8) ? globalThis.Number(object.chamferRadiusU8) : 0,
      line: isSet(object.line) ? LineConfig.fromJSON(object.line) : undefined,
    };
  },

  toJSON(message: WidgetRectAngle): unknown {
    const obj: any = {};
    if (message.chamferType !== 0) {
      obj.chamferType = widgetRectAngle_ChamferTypeToJSON(message.chamferType);
    }
    if (message.chamferRadiusU8 !== 0) {
      obj.chamferRadiusU8 = Math.round(message.chamferRadiusU8);
    }
    if (message.line !== undefined) {
      obj.line = LineConfig.toJSON(message.line);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetRectAngle>, I>>(base?: I): WidgetRectAngle {
    return WidgetRectAngle.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetRectAngle>, I>>(object: I): WidgetRectAngle {
    const message = createBaseWidgetRectAngle();
    message.chamferType = object.chamferType ?? 0;
    message.chamferRadiusU8 = object.chamferRadiusU8 ?? 0;
    message.line = (object.line !== undefined && object.line !== null)
      ? LineConfig.fromPartial(object.line)
      : undefined;
    return message;
  },
};

function createBaseWidgetLinear(): WidgetLinear {
  return { linearType: 0, points: [], mainLine: undefined };
}

export const WidgetLinear: MessageFns<WidgetLinear> = {
  encode(message: WidgetLinear, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.linearType !== 0) {
      writer.uint32(8).int32(message.linearType);
    }
    for (const v of message.points) {
      Point.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.mainLine !== undefined) {
      LineConfig.encode(message.mainLine, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetLinear {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetLinear();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.linearType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.points.push(Point.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.mainLine = LineConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetLinear {
    return {
      linearType: isSet(object.linearType) ? widgetLinear_LinearTypeFromJSON(object.linearType) : 0,
      points: globalThis.Array.isArray(object?.points) ? object.points.map((e: any) => Point.fromJSON(e)) : [],
      mainLine: isSet(object.mainLine) ? LineConfig.fromJSON(object.mainLine) : undefined,
    };
  },

  toJSON(message: WidgetLinear): unknown {
    const obj: any = {};
    if (message.linearType !== 0) {
      obj.linearType = widgetLinear_LinearTypeToJSON(message.linearType);
    }
    if (message.points?.length) {
      obj.points = message.points.map((e) => Point.toJSON(e));
    }
    if (message.mainLine !== undefined) {
      obj.mainLine = LineConfig.toJSON(message.mainLine);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetLinear>, I>>(base?: I): WidgetLinear {
    return WidgetLinear.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetLinear>, I>>(object: I): WidgetLinear {
    const message = createBaseWidgetLinear();
    message.linearType = object.linearType ?? 0;
    message.points = object.points?.map((e) => Point.fromPartial(e)) || [];
    message.mainLine = (object.mainLine !== undefined && object.mainLine !== null)
      ? LineConfig.fromPartial(object.mainLine)
      : undefined;
    return message;
  },
};

function createBaseWidgetArc(): WidgetArc {
  return { arcType: 0, center: undefined, radiusU16: 0, startAngleU16: 0, endAngleU16: 0 };
}

export const WidgetArc: MessageFns<WidgetArc> = {
  encode(message: WidgetArc, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.arcType !== 0) {
      writer.uint32(8).int32(message.arcType);
    }
    if (message.center !== undefined) {
      Point.encode(message.center, writer.uint32(18).fork()).join();
    }
    if (message.radiusU16 !== 0) {
      writer.uint32(24).uint32(message.radiusU16);
    }
    if (message.startAngleU16 !== 0) {
      writer.uint32(32).uint32(message.startAngleU16);
    }
    if (message.endAngleU16 !== 0) {
      writer.uint32(40).uint32(message.endAngleU16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetArc {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetArc();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.arcType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.center = Point.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.radiusU16 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.startAngleU16 = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.endAngleU16 = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetArc {
    return {
      arcType: isSet(object.arcType) ? widgetArc_ArcTypeFromJSON(object.arcType) : 0,
      center: isSet(object.center) ? Point.fromJSON(object.center) : undefined,
      radiusU16: isSet(object.radiusU16) ? globalThis.Number(object.radiusU16) : 0,
      startAngleU16: isSet(object.startAngleU16) ? globalThis.Number(object.startAngleU16) : 0,
      endAngleU16: isSet(object.endAngleU16) ? globalThis.Number(object.endAngleU16) : 0,
    };
  },

  toJSON(message: WidgetArc): unknown {
    const obj: any = {};
    if (message.arcType !== 0) {
      obj.arcType = widgetArc_ArcTypeToJSON(message.arcType);
    }
    if (message.center !== undefined) {
      obj.center = Point.toJSON(message.center);
    }
    if (message.radiusU16 !== 0) {
      obj.radiusU16 = Math.round(message.radiusU16);
    }
    if (message.startAngleU16 !== 0) {
      obj.startAngleU16 = Math.round(message.startAngleU16);
    }
    if (message.endAngleU16 !== 0) {
      obj.endAngleU16 = Math.round(message.endAngleU16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetArc>, I>>(base?: I): WidgetArc {
    return WidgetArc.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetArc>, I>>(object: I): WidgetArc {
    const message = createBaseWidgetArc();
    message.arcType = object.arcType ?? 0;
    message.center = (object.center !== undefined && object.center !== null)
      ? Point.fromPartial(object.center)
      : undefined;
    message.radiusU16 = object.radiusU16 ?? 0;
    message.startAngleU16 = object.startAngleU16 ?? 0;
    message.endAngleU16 = object.endAngleU16 ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
