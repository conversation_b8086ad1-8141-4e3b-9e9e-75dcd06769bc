#pragma once

#include "urq/compile.h"
#include "urq/preload.h" // IWYU pragma: keep
#include <stdio.h>       // IWYU pragma: export
#include <stdlib.h>      // IWYU pragma: export

#ifndef URQ__TODO_H
#define URQ__TODO_H
#ifdef __cplusplus
extern "C" {
#endif

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
#define TODO(id)                                                               \
    printf(__FILE__ ":%d\n  TODO: %lld\n", __LINE__, id);                      \
    urq_exit(1)
#else
#define TODO(id)                                                               \
    printf(__FILE__ ":%d\n  TODO: %ld\n", __LINE__, id);                       \
    urq_exit(1)
#endif

#ifdef __cplusplus
}
#endif
#endif // URQ__TODO_H
