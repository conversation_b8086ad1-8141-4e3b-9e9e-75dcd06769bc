#include "urq/widget.h"
#include "lvgl.h"
#include "urq/linear/conf.h"
#include "urq/linear/widget.h"
#include "urq/log/verbose.h"
#include "urq/page/page.h"
#include "urq/preload.h"
#include "urq/project/project.h"
#include "urq/req/action.h"
#include "urq/text/ptr_list.h"
#include "urq/text/text.h"
#include "urq/user_data.h"
#include "urq/util/util.h"
#include "urq/widget/property.h"
#include "urq/widget/type.h"
#include <lvgl/src/widgets/lv_line.h>

#define Self urq_linear_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_LINEAR_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_linear_widget_config, NULL, urq_linear_widget_set_state,
        urq_linear_widget_set_property);

    widget->common_conf = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;

    if (widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);
        if (widget->common_conf != NULL) {
            if (widget->common_conf->styles != NULL) {
                for (size_t idx = 0; idx < widget->common_conf->styles->size;
                     ++idx) {
                    urq_style_t *style = widget->common_conf->styles->data[idx];
                    if (style != NULL && style->font.marquee != NULL) {
                        urq_marquee_disable(style->font.marquee);
                    }
                }
            }
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
        }
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (urq_event_filter_exit(class_p, e)) {
        return;
    }
    Self *widget = (Self *)lv_event_get_target(e);
    urq_widget_common_conf_t *common_conf = widget->common_conf;
    static uint32_t count = 0;
    // 检查是否执行点击事件
    if (urq_event_check_exec_click(e, &count, common_conf)) {
        // exec
        printf("可执行\n");
    }
    if (common_conf != NULL && common_conf->has_action) {
        if (urq_page_add_frame_cb3(
                widget->user_data.page, urq_req_wirte_set_action, urq_noop3,
                (void *)widget->user_data.page,
                (void *)(intptr_t)common_conf->id, (void *)(intptr_t)e->code)) {
            printf("添加成功\n");
        }
    }
}

lv_obj_t *urq_linear_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj;

    obj = lv_obj_class_create_obj(&URQ_LINEAR_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

void urq_linear_widget_conf(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;

    urq_used(mut_conf);
    urq_linear_widget_conf_t *conf = widget->conf;
    widget->obj = lv_line_create(self);
    lv_line_set_points(widget->obj, conf->points, conf->points_size);
    printf(
        "linear widget conf: %d, %d, %d\n", conf->points_size,
        conf->points[0].x, conf->points[0].y);
}

void urq_linear_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;

    widget->common_conf = mut_conf->common_conf;
    urq_widget_config(self, mut_conf, urq_linear_widget_conf);
}

void urq_linear_widget_set_property_callback(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    urq_used(self);
    urq_used(index);
    urq_used(value);
    switch (property_id) {
    // case URQ_PROPERTY_ID_TEXT:
    //     lv_label_set_text(widget->label, (const char *)value);
    //     break;
    default:
        break;
    }
}

void urq_linear_widget_set_property(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    urq_linear_widget_t *const widget = (urq_linear_widget_t *)self;
    urq_widget_set_property(
        self, widget->common_conf, property_id, index, value,
        urq_linear_widget_set_property_callback);
}

void urq_linear_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platfrom);
}
