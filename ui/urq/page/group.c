#include "urq/page/group.h"
#include "urq/errno.h"
#include "urq/log/verbose.h"
#include "urq/page/conf.h"
#include "urq/page/map.h"
#include "urq/preload.h"
#include "urq/style/style.h"
#include "urq/util/style.h"
#include <errno.h>

#define Self urq_page_group_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_PAGE_GROUP_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(urq_page_group_t),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *group = (Self *)obj;
    group->pages = urq_page_map_new();
    urq_style_init_inplace(&group->style);
    group->trigger_data = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *group = (Self *)obj;

    if (group->pages != NULL) {
        urq_page_map_free(group->pages);
    }
    urq_page_group_close(obj);
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (lv_obj_event_base(class_p, e) != LV_RES_OK) {
        return;
    }
    // log_v("page event: %s\n", urq_lv_event_code(e->code));
}

lv_obj_t *urq_page_group_create(lv_obj_t *parent)
{
    lv_obj_t *obj = lv_obj_class_create_obj(&URQ_PAGE_GROUP_CLASS, parent);
    lv_obj_class_init_obj(obj);

    Self *widget = (urq_page_group_t *)obj;

    if (widget->pages == NULL) {
        urq_page_group_close(obj);
        lv_obj_del(obj);
        errno = URQ_ENOMEM;
        return NULL;
    }
    return obj;
}

void urq_page_group_close(lv_obj_t *self)
{
    urq_page_group_t *group = (urq_page_group_t *)self;
    if (self != NULL) {
        if (group->trigger_data != NULL) {
            urq_free(group->trigger_data);
            group->trigger_data = NULL;
        }
    }
}

int urq_page_group_conf(
    lv_obj_t *self, urq_project_t *project, urq_page_group_conf_t *mv_conf)
{
    urq_page_group_t *widget = (urq_page_group_t *)self;
    urq_page_conf_t conf;

    lv_coord_t w =
        mv_conf->size.w == 0 ? project->display->size.w : mv_conf->size.w;
    lv_coord_t h =
        mv_conf->size.h == 0 ? project->display->size.h : mv_conf->size.h;

    widget->style = mv_conf->style;
    lv_obj_set_size(self, project->size.w, project->size.h);

    if (project->beyond_display) {
        lv_obj_t *background = lv_obj_create(self);
        lv_obj_remove_style_all(background);
        lv_obj_set_size(background, w, h);

        urq_set_style_page_bg(&project->env, background, widget->style.bg);
        lv_obj_set_style_bg_color(self, lv_color_hex(0xeeeeee), LV_PART_MAIN);
        lv_obj_set_style_bg_opa(self, LV_OPA_COVER, LV_PART_MAIN);
    }

    widget->id = mv_conf->id;
    widget->project = project;
    widget->fs = &project->fs;
    widget->page_no = mv_conf->page_no;

    // TODO
    if (mv_conf->popup_type == URQ_PAGE_POPUP_TYPE_UNSPECIFIED) {
    } else if (mv_conf->popup_type == URQ_PAGE_POPUP_TYPE_HALF_MODEL) {
    } else if (mv_conf->popup_type == URQ_PAGE_POPUP_TYPE_MODEL) {
    } else if (mv_conf->popup_type == URQ_PAGE_POPUP_TYPE_FLOATING) {
    }

    for (uint32_t i = 0; i < mv_conf->group_id_list.size; i++) {
        lv_obj_t *page = urq_page_create(self);
        if (page == NULL) {
            log_e("create page error\n");
            return -1;
        }
        urq_page_conf_init_inplace(&conf);
        conf.id = (urq_page_id_t)mv_conf->group_id_list.data[i];
        conf.type = mv_conf->type;
        lv_obj_set_size(page, project->size.w, project->size.h);
        if (!project->beyond_display) {
            urq_set_style_page_bg(&project->env, page, widget->style.bg);
        }

        urq_page_conf(page, project, &conf);
        urq_page_conf_free_inplace(&conf);
        if (urq_page_map_add(widget->pages, (urq_page_t *)page)) {
            log_e("add page error\n");
            return -1;
        }
        if (conf.id != widget->id) {
            lv_obj_set_style_opa(
                page, (lv_opa_t)project->env.var_sys.overlay_opa, LV_PART_MAIN);
        }
    }

    // TODO
    // urq_page_load_property(project);
    // urq_fs_project_load_widget_property_async(project, widget->id);

    return 0;
}
