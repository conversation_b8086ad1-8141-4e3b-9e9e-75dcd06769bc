
#pragma once

#include "klib/khash.h"
#include "lvgl.h"
#include "urq_conf/page/group_conf.h"
#include <stdint.h>

#ifndef URQ__PAGE__GROUP_MAP_H
#define URQ__PAGE__GROUP_MAP_H
#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

/// @brief lvgl 表
KHASH_MAP_INIT_INT(urq_page_group_map, urq_page_group_conf_t *)
#pragma GCC diagnostic pop // 恢复之前的警告状态

/// @brief lvgl 表
typedef khash_t(urq_page_group_map) urq_page_group_map_t;

/// @brief 创建新的组件表
///
/// @return 新的组件表
urq_page_group_map_t *urq_page_group_map_new(void)
    __attribute__((__warn_unused_result__()));

/// @brief 释放组件表
///
/// @param self 组件表
/// @return void
void urq_page_group_map_free(urq_page_group_map_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 调整大小
///
/// @param self 组件表
/// @param size 大小
/// @return void
void urq_page_group_map_resize(urq_page_group_map_t *self, uint32_t size)
    __attribute__((__nonnull__(1)));

/// @brief 添加一个 lvgl 组件
///
/// @param self 多语言表
/// @param id   多语言ID
/// @param text 多语言文本
/// @return 是否添加成功
int urq_page_group_map_add_ptr(
    urq_page_group_map_t *const self, uint32_t id,
    urq_page_group_conf_t *page_group_conf)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

int urq_page_group_map_add(
    urq_page_group_map_t *const self, uint32_t id,
    urq_page_group_conf_t page_group_conf)
    __attribute__((__nonnull__(1), __warn_unused_result__()));
/// @brief 获取 lvgl 组件
///
/// @param self      多语言表
/// @param id        组件ID
/// @param out_value 组件
/// @return 是否获取成功
urq_page_group_conf_t *urq_page_group_map_get(
    const urq_page_group_map_t *self, uint32_t id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 判断 lvgl 组件是否存在
///
/// @param self 多语言表
/// @param id   组件ID
/// @return 是否存在
bool urq_page_group_map_has(const urq_page_group_map_t *self, uint32_t id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));
#ifdef __cplusplus
}
#endif

#endif // URQ__LVGL__MAP_H
