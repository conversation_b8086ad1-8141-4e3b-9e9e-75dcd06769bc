#include "urq/page/load.h"
#include "lvgl.h"
#include "urq/errno.h"
#include "urq/fs/file.h"
#include "urq/fs/project.h"
#include "urq/log/verbose.h"
#include "urq/page/group.h"
#include "urq/page/page.h"
#include "urq/project/project.h"
#include "urq/user_data.h"
#include "urq/widget.h"
#include "urq_conf/datastruct/map/lvgl.h"
#include "urq_conf/system_variable.h"
#include "urq_conf/widget/conf.h"
#include "urq_conf/widget/group_conf.h"
#include "urq_parse/i18n_map.h"
#include "urq_parse/set_property.h"
#include "urq_parse/widget_group.h"

void urq_page_load_property(void *self)
{
    return;
    urq_project_t *project = (urq_project_t *)self;
    urq_fs_file_t *file;
    urq_page_group_t *pages = (urq_page_group_t *)project->page_showing;

    urq_page_t *page = urq_page_map_get(pages->pages, pages->id);
    if (urq_parse__page_property(
            &project->env, page->widget_property_map, file)) {
        {
            urq_fs_file_free(file);
            log_e("parse widget property failed, errno: %d\n", errno);
            return;
        }
        urq_fs_file_free(file);
    }
}

void urq_page_load_language(void *self, const uint8_t *data, size_t size)
{
    urq_page_t *page = (urq_page_t *)self;
    urq_project_t *project = (urq_project_t *)page->project;
    urq_used(data);
    urq_used(size);
    urq_used(project);

    // if(page->i18n_map != NULL) {
    //     urq_i18n_map_free(page->i18n_map);
    // }
    // page->i18n_map = urq_i18n_map_new();

    // if (urq_parse__i18n_map(
    //         data, size,
    //         (urq_i18n_lang_id_t)
    //             project->env.var_sys.registers.data[URQ_DEVICE_LOCAL_ADDR_LANGUAGE],
    //         page->i18n_map)) {
    //     log_e("parse language failed, errno: %d\n", errno);
    //     return;
    // }
}

void urq_page_load_language_callback(void *self)
{
    urq_page_t *page = (urq_page_t *)self;
    urq_fs_file_t *file;
    if (urq_fs_project_load_language(page->project, page->id, &file)) {
        if (errno == URQ_EPENDING || errno == URQ_RELOAD) {
            log_d("read language pending\n");
            if (urq_page_add_frame_cb1(
                    page, urq_page_load_language_callback, urq_noop1, self)) {
                log_w("add page frame callback error\n");
            }
            return;
        }
        // urq_fs_file_free(file);
        return;
    }
    urq_page_load_language(
        (lv_obj_t *)page, urq_fs_file_data(file), file->size);

    urq_fs_file_free(file);
}

void urq_page_load_foreach_widget(
    lv_obj_t *obj, lv_obj_t *parent, urq_widget_conf_list_t *widgets_conf)
{
    urq_page_t *page = (urq_page_t *)obj;
    urq_project_t *project = page->project;
    urq_widget_conf_t clone_list[widgets_conf->size];

    urq_widget_conf_t *node_conf;
    for (uint32_t i = 0; i < widgets_conf->size; i++) {
        node_conf = (urq_widget_conf_t *)widgets_conf->data[i];
        clone_list[i] = *node_conf;
        urq_widget_get(page, parent, node_conf);
    }

    if (project->display->clone_map != NULL) {
        urq_lvgl_map_t *clone_widget_map =
            urq_lvgl_pair_map_get_map(project->display->clone_map, page->id);

        if (clone_widget_map != NULL) {
            for (uint32_t i = 0; i < widgets_conf->size; i++) {
                urq_widget_conf_t _widget_conf = clone_list[i];
                lv_obj_t *clone_obj = urq_lvgl_map_get(
                    clone_widget_map, (uint32_t)_widget_conf.id);

                if (clone_obj != NULL) {
                    urq_user_data_t *user_data =
                        urq_obj_get_user_data(clone_obj);
                    urq_widget_conf_t *_conf =
                        urq_malloc(sizeof(urq_widget_conf_t));
                    urq_widget_conf_init_inplace(_conf);
                    *_conf = _widget_conf;
                    user_data->conf(clone_obj, _conf);
                }
            }
        }
    }
}

void urq_page_load_widgets(lv_obj_t *obj, const uint8_t *data, size_t size)
{
    urq_page_t *page = (urq_page_t *)obj;
    urq_project_t *project = (urq_project_t *)page->project;

    urq_widget_group_conf_t widget_group;
    log_d("begin load page widget, id: %d\n", page->id);

    urq_widget_group_init_inplace(&widget_group);
    if (urq_parse__widget_group(
            page->id, data, size, &project->env, &project->env.var_sys,
            &widget_group)) {
        urq_widget_group_free_inplace(&widget_group);
        log_e("parse widget group error\n");
        return;
    }

    urq_page_load_foreach_widget(obj, obj, &widget_group.widgets);
    urq_widget_group_free_inplace(&widget_group); // 手动释放，组件都被移走了
}

void urq_page_load_widget_callback(void *self)
{
    urq_page_t *page = (urq_page_t *)self;
    urq_fs_file_t *file;
    if (urq_fs_project_load_widget_group(page->project, page->id, &file)) {
        if (errno == URQ_EPENDING || errno == URQ_RELOAD) {
            log_d("read widget group pending\n");
            if (urq_page_add_frame_cb1(
                    page, urq_page_load_widget_callback, urq_noop1, self)) {
                log_w("add page frame callback error\n");
            }
            return;
        }
        // urq_fs_file_free(file);
        return;
    }
    urq_page_load_widgets((lv_obj_t *)page, urq_fs_file_data(file), file->size);

    urq_fs_file_free(file);
}
