#pragma once

#include "lvgl.h"
#include "urq/arc/conf.h"
#include "urq/user_data.h"
#include <lvgl/src/misc/lv_area.h>
#include <stdint.h>

#ifndef URQ__ARC__WIDGET_H
#define URQ__ARC__WIDGET_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 父组件
    lv_obj_t super;
    /// @brief user data 字段
    urq_user_data_t user_data;
    lv_obj_t *obj;
    /// @brief 点
    lv_point_t *points;
    /// @brief 点数
    uint16_t points_size;
    /// @brief 组件配置
    urq_arc_widget_conf_t *conf;
    /// @brief 通用配置
    urq_widget_common_conf_t *common_conf;
} urq_arc_widget_t;

/// @brief 创建 arc_widget
/// @param parent 当前组件所属的页面
/// @returns 组件
lv_obj_t *urq_arc_widget_create(lv_obj_t *parent)
    __attribute__((__nonnull__(1)));

/// @brief 配置 arc_widget
/// @param self     组件自身
/// @param mut_conf 配置的内容
/// @returns 组件
void urq_arc_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
    __attribute__((__nonnull__(1, 2)));

/// @brief 设置 arc_widget 的属性
/// @param self     组件自身
/// @param property_id 属性ID
/// @param index 索引
/// @param value 值
void urq_arc_widget_set_property(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
    __attribute__((__nonnull__(1)));

/// @brief 设置 arc_widget 的状态
/// @param self 组件自身
/// @param state 状态
void urq_arc_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
    __attribute__((__nonnull__(1)));

#ifdef __cplusplus
}
#endif
#endif // URQ__ARC__WIDGET_H
