#include "urq/ruler/widget.h"
#include "urq/font.h"
#include "urq/log/debug.h"
#include "urq/util/color.h"
#include "urq/util/control.h"
#include "urq/util/font.h"
#include "urq/util/state.h"
#include "urq/widget.h"
#include <math.h>
#include <stdio.h>

typedef urq_ruler_widget_t Self;

// 前向声明
static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);
static void urq_ruler_widget_conf(lv_obj_t *self, urq_widget_conf_t *mut_conf);
static void urq_ruler_widget_draw_cb(lv_event_t *e);

// 组件类定义
const lv_obj_class_t URQ_RULER_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF / 4,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_ruler_widget_config, NULL, urq_ruler_widget_set_state,
        NULL);

    widget->conf = NULL;
    widget->common_conf = NULL;
    widget->current_value = 0;
    widget->size.w = LV_DPI_DEF;
    widget->size.h = LV_DPI_DEF / 4;

    // 添加绘制事件回调
    lv_obj_add_event_cb(
        obj, urq_ruler_widget_draw_cb, LV_EVENT_DRAW_MAIN, widget);
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;

    if (widget->conf != NULL) {
        urq_ruler_widget_conf_free_inplace(widget->conf);
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    urq_used(class_p);

    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *obj = lv_event_get_target(e);
    Self *widget = (Self *)obj;

    if (code == LV_EVENT_CLICKED) {
        // 处理点击事件，可以用于设置当前值
        lv_point_t point;
        lv_indev_get_point(lv_indev_get_act(), &point);

        // 根据点击位置计算对应的刻度值
        if (widget->conf != NULL) {
            lv_coord_t relative_pos;
            lv_coord_t total_size;

            if (widget->conf->type == URQ_RULER_TYPE_HORIZONTAL) {
                relative_pos = point.x - lv_obj_get_x(obj);
                total_size = widget->size.w;
            } else if (widget->conf->type == URQ_RULER_TYPE_VERTICAL) {
                relative_pos = point.y - lv_obj_get_y(obj);
                total_size = widget->size.h;
            } else {
                return; // 弧形刻度尺暂不支持点击
            }

            if (relative_pos >= 0 && relative_pos <= total_size) {
                lv_coord_t min_val = widget->conf->scale.min_value;
                lv_coord_t max_val = widget->conf->scale.max_value;

                if (min_val != -1 && max_val != -1) {
                    float ratio = (float)relative_pos / (float)total_size;
                    widget->current_value =
                        min_val + (lv_coord_t)(ratio * (max_val - min_val));
                    lv_obj_invalidate(obj);
                }
            }
        }
    }
}

lv_obj_t *urq_ruler_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj = lv_obj_class_create_obj(&URQ_RULER_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

void urq_ruler_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;
    widget->conf = mut_conf->config.ruler;
    widget->common_conf = mut_conf->common_conf;
    widget->size = mut_conf->size;

    lv_obj_set_size(self, mut_conf->size.w, mut_conf->size.h);

    urq_widget_config(self, mut_conf, NULL);
}

void urq_ruler_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platform)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platform);
}

void urq_ruler_widget_set_value(lv_obj_t *self, lv_coord_t value)
{
    Self *widget = (Self *)self;
    if (widget->current_value != value) {
        widget->current_value = value;
        lv_obj_invalidate(self);
    }
}

lv_coord_t urq_ruler_widget_get_value(lv_obj_t *self)
{
    Self *widget = (Self *)self;
    return widget->current_value;
}

void urq_ruler_widget_set_range(
    lv_obj_t *self, lv_coord_t min_value, lv_coord_t max_value)
{
    Self *widget = (Self *)self;
    if (widget->conf != NULL) {
        widget->conf->scale.min_value = min_value;
        widget->conf->scale.max_value = max_value;
        lv_obj_invalidate(self);
    }
}

void urq_ruler_widget_add_mark(
    lv_obj_t *self, lv_coord_t value, const char *label, lv_color_t color)
{
    urq_used(self);
    urq_used(value);
    urq_used(label);
    urq_used(color);
    // TODO: 实现自定义标记功能
}

void urq_ruler_widget_clear_marks(lv_obj_t *self)
{
    urq_used(self);
    // TODO: 实现清除标记功能
}

// 计算对齐后的刻度位置
static lv_coord_t urq_calculate_aligned_tick_position(
    lv_coord_t obj_start, lv_coord_t obj_size, int tick_index, int total_ticks,
    urq_ruler_alignment_t alignment)
{
    lv_coord_t available_space;
    lv_coord_t tick_spacing;
    lv_coord_t start_offset;

    switch (alignment) {
    case URQ_RULER_ALIGNMENT_START:
        // 左对齐/上对齐：刻度从起始位置开始
        return obj_start + (lv_coord_t)((obj_size * tick_index) / total_ticks);

    case URQ_RULER_ALIGNMENT_CENTER:
        // 居中对齐：刻度在中间区域均匀分布
        if (total_ticks <= 1) {
            return obj_start + obj_size / 2;
        }
        available_space = obj_size;
        tick_spacing = available_space / total_ticks;
        start_offset = tick_spacing / 2;
        return obj_start + start_offset +
               (lv_coord_t)(tick_spacing * tick_index);

    case URQ_RULER_ALIGNMENT_END:
        // 右对齐/下对齐：刻度从结束位置开始
        return obj_start + obj_size -
               (lv_coord_t)((obj_size * (total_ticks - tick_index)) /
                            total_ticks);

    default:
        return obj_start + (lv_coord_t)((obj_size * tick_index) / total_ticks);
    }
}

// 绘制水平刻度尺
static void urq_draw_horizontal_ruler(
    lv_event_t *e, Self *widget, lv_draw_ctx_t *draw_ctx)
{
    lv_obj_t *obj = lv_event_get_target(e);
    urq_ruler_widget_conf_t *conf = widget->conf;
    urq_curve_scale_t *scale = &conf->scale;

    lv_coord_t obj_x = lv_obj_get_x(obj);
    lv_coord_t obj_y = lv_obj_get_y(obj);
    lv_coord_t obj_w = widget->size.w;
    lv_coord_t obj_h = widget->size.h;

    urq_project_t *project = widget->user_data.project;

    // 绘制主线
    if (conf->main_line != NULL) {
        lv_draw_line_dsc_t line_dsc;
        lv_draw_line_dsc_init(&line_dsc);
        line_dsc.color =
            urq_get_color(project->env.theme_color, &conf->main_line->color);
        line_dsc.width = conf->main_line->width;

        // lv_coord_t line_y =
        //     obj_y +
        //     (conf->direction == URQ_RULER_DIRECTION_TOP_LEFT ? 0 : obj_h -
        //     1);
        lv_coord_t line_y = obj_y + obj_h - 1;
        lv_point_t p1 = {obj_x, line_y};
        lv_point_t p2 = {obj_x + obj_w, line_y};
        lv_draw_line(draw_ctx, &line_dsc, &p1, &p2);
    }

    // 计算刻度参数
    if (scale->min_value == -1 || scale->max_value == -1)
        return;

    lv_coord_t value_range = scale->max_value - scale->min_value;
    if (value_range <= 0)
        return;

    // 绘制主刻度
    if (scale->main_tick_count > 0) {
        lv_draw_line_dsc_t tick_dsc;
        lv_draw_line_dsc_init(&tick_dsc);
        tick_dsc.color =
            urq_get_color(project->env.theme_color, &scale->main_color);
        tick_dsc.width = scale->main_tick_width;

        for (int i = 0; i <= scale->main_tick_count; i++) {
            // 使用对齐功能计算刻度位置
            lv_coord_t tick_x = urq_calculate_aligned_tick_position(
                obj_x, obj_w, i, scale->main_tick_count, conf->alignment);
            lv_coord_t tick_y1, tick_y2;

            lv_point_t p1 = {tick_x, tick_y1};
            lv_point_t p2 = {tick_x, tick_y2};
            lv_draw_line(draw_ctx, &tick_dsc, &p1, &p2);

            // 绘制标签
            lv_coord_t value =
                (lv_coord_t)(scale->min_value +
                             (value_range * i) / scale->main_tick_count);
            char label_text[32];

            snprintf(label_text, sizeof(label_text), "%d", (int)value);

            lv_draw_label_dsc_t label_dsc;
            lv_draw_label_dsc_init(&label_dsc);
            lv_coord_t label_y =
                obj_y + obj_h / 2 - lv_font_get_line_height(label_dsc.font) / 2;

            lv_area_t label_area;
            label_area.x1 = tick_x - 20;
            label_area.y1 = label_y;
            label_area.x2 = tick_x + 20;
            label_area.y2 = label_y + lv_font_get_line_height(label_dsc.font);

            lv_draw_label(draw_ctx, &label_dsc, &label_area, label_text, NULL);
        }
    }
}

// 绘制垂直刻度尺
static void urq_draw_vertical_ruler(
    lv_event_t *e, Self *widget, lv_draw_ctx_t *draw_ctx)
{
    lv_obj_t *obj = lv_event_get_target(e);
    urq_ruler_widget_conf_t *conf = widget->conf;
    urq_curve_scale_t *scale = &conf->scale;

    lv_coord_t obj_x = lv_obj_get_x(obj);
    lv_coord_t obj_y = lv_obj_get_y(obj);
    lv_coord_t obj_w = widget->size.w;
    lv_coord_t obj_h = widget->size.h;

    urq_project_t *project = widget->user_data.project;

    // 绘制主线
    if (conf->main_line != NULL) {
        lv_draw_line_dsc_t line_dsc;
        lv_draw_line_dsc_init(&line_dsc);
        line_dsc.color =
            urq_get_color(project->env.theme_color, &conf->main_line->color);
        line_dsc.width = conf->main_line->width;

        lv_coord_t line_x = obj_x;
        lv_point_t p1 = {line_x, obj_y};
        lv_point_t p2 = {line_x, obj_y + obj_h};
        lv_draw_line(draw_ctx, &line_dsc, &p1, &p2);
    }

    // 计算刻度参数
    if (scale->min_value == -1 || scale->max_value == -1)
        return;

    lv_coord_t value_range = scale->max_value - scale->min_value;
    if (value_range <= 0)
        return;

    // 绘制主刻度
    if (scale->main_tick_count > 0) {
        lv_draw_line_dsc_t tick_dsc;
        lv_draw_line_dsc_init(&tick_dsc);
        tick_dsc.color =
            urq_get_color(project->env.theme_color, &scale->main_color);
        tick_dsc.width = scale->main_tick_width;

        for (int i = 0; i <= scale->main_tick_count; i++) {
            lv_coord_t tick_y =
                (lv_coord_t)(obj_y + obj_h -
                             (obj_h * i) / scale->main_tick_count); // 从下往上
            lv_coord_t tick_x1, tick_x2;

            tick_x1 = obj_x;
            tick_x2 = obj_x + scale->main_tick_len;

            lv_point_t p1 = {tick_x1, tick_y};
            lv_point_t p2 = {tick_x2, tick_y};
            lv_draw_line(draw_ctx, &tick_dsc, &p1, &p2);

            // 绘制标签
            lv_coord_t value =
                (lv_coord_t)(scale->min_value +
                             (value_range * i) / scale->main_tick_count);
            char label_text[32];

            snprintf(label_text, sizeof(label_text), "%d", (int)value);

            lv_draw_label_dsc_t label_dsc;
            lv_draw_label_dsc_init(&label_dsc);

            lv_coord_t label_x;
            label_x = obj_x + obj_w / 2 - 20;

            lv_area_t label_area;
            label_area.x1 = label_x;
            label_area.y1 =
                tick_y - lv_font_get_line_height(label_dsc.font) / 2;
            label_area.x2 = label_x + 40;
            label_area.y2 =
                tick_y + lv_font_get_line_height(label_dsc.font) / 2;

            lv_draw_label(draw_ctx, &label_dsc, &label_area, label_text, NULL);
        }
    }
}

// 绘制弧形刻度尺
static void urq_draw_arc_ruler(
    lv_event_t *e, Self *widget, lv_draw_ctx_t *draw_ctx)
{
    lv_obj_t *obj = lv_event_get_target(e);
    urq_ruler_widget_conf_t *conf = widget->conf;
    urq_curve_scale_t *scale = &conf->scale;

    lv_coord_t obj_x = lv_obj_get_x(obj);
    lv_coord_t obj_y = lv_obj_get_y(obj);
    lv_coord_t obj_w = widget->size.w;
    lv_coord_t obj_h = widget->size.h;

    lv_coord_t center_x = obj_x + obj_w / 2;
    lv_coord_t center_y = obj_y + obj_h / 2;

    urq_project_t *project = widget->user_data.project;

    // 绘制主弧线
    if (conf->main_line != NULL) {
        lv_draw_arc_dsc_t arc_dsc;
        lv_draw_arc_dsc_init(&arc_dsc);
        arc_dsc.color =
            urq_get_color(project->env.theme_color, &conf->main_line->color);
        arc_dsc.width = conf->main_line->width;
        arc_dsc.start_angle = 0;
        arc_dsc.end_angle = 360;
        lv_point_t p1 = {center_x, center_y};

        lv_draw_arc(draw_ctx, &arc_dsc, &p1, 10, 0, 360);
    }

    // 计算刻度参数
    if (scale->min_value == -1 || scale->max_value == -1)
        return;

    lv_coord_t value_range = scale->max_value - scale->min_value;
    if (value_range <= 0)
        return;

    // 绘制主刻度
    if (scale->main_tick_count > 0) {
        lv_draw_line_dsc_t tick_dsc;
        lv_draw_line_dsc_init(&tick_dsc);
        tick_dsc.color =
            urq_get_color(project->env.theme_color, &scale->main_color);
        tick_dsc.width = scale->main_tick_width;

        for (int i = 0; i <= scale->main_tick_count; i++) {
            // 计算当前刻度的角度
            float angle_ratio = (float)i / (float)scale->main_tick_count;
            int16_t current_angle = (int16_t)(angle_ratio * 360);

            // 转换为弧度
            float angle_rad = (float)current_angle * M_PI / 180.0f;

            // 计算刻度线的起点和终点
            lv_coord_t inner_radius = conf->radius - scale->main_tick_len;
            lv_coord_t outer_radius = conf->radius;

            lv_coord_t x1 =
                center_x + (lv_coord_t)(inner_radius * cosf(angle_rad));
            lv_coord_t y1 =
                center_y + (lv_coord_t)(inner_radius * sinf(angle_rad));
            lv_coord_t x2 =
                center_x + (lv_coord_t)(outer_radius * cosf(angle_rad));
            lv_coord_t y2 =
                center_y + (lv_coord_t)(outer_radius * sinf(angle_rad));

            lv_point_t p1 = {x1, y1};
            lv_point_t p2 = {x2, y2};
            lv_draw_line(draw_ctx, &tick_dsc, &p1, &p2);

            // 绘制标签
            lv_coord_t value =
                (lv_coord_t)scale->min_value +
                (lv_coord_t)((value_range * i) / scale->main_tick_count);
            char label_text[32];

            snprintf(label_text, sizeof(label_text), "%d", (int)value);

            // TODO
            lv_draw_label_dsc_t label_dsc;
            lv_draw_label_dsc_init(&label_dsc);

            lv_coord_t label_x =
                center_x + (lv_coord_t)(conf->radius * cosf(angle_rad)) - 15;
            lv_coord_t label_y = center_y +
                                 (lv_coord_t)(conf->radius * sinf(angle_rad)) -
                                 lv_font_get_line_height(label_dsc.font) / 2;

            lv_area_t label_area;
            label_area.x1 = label_x;
            label_area.y1 = label_y;
            label_area.x2 = label_x + 30;
            label_area.y2 = label_y + lv_font_get_line_height(label_dsc.font);

            lv_draw_label(draw_ctx, &label_dsc, &label_area, label_text, NULL);
        }
    }
}

// 主绘制回调函数
static void urq_ruler_widget_draw_cb(lv_event_t *e)
{
    lv_obj_t *obj = lv_event_get_target(e);
    Self *widget = (Self *)lv_event_get_user_data(e);
    lv_draw_ctx_t *draw_ctx = lv_event_get_draw_ctx(e);

    if (widget->conf == NULL) {
        return;
    }

    // 根据刻度尺类型选择绘制函数
    switch (widget->conf->type) {
    case URQ_RULER_TYPE_HORIZONTAL:
        urq_draw_horizontal_ruler(e, widget, draw_ctx);
        break;
    case URQ_RULER_TYPE_VERTICAL:
        urq_draw_vertical_ruler(e, widget, draw_ctx);
        break;
    case URQ_RULER_TYPE_ARC:
    case URQ_RULER_TYPE_CIRCLE:
        urq_draw_arc_ruler(e, widget, draw_ctx);
        break;
    default:
        break;
    }

    // 绘制当前值指示器（如果需要）
    if (widget->current_value >= widget->conf->scale.min_value &&
        widget->current_value <= widget->conf->scale.max_value) {

        lv_draw_rect_dsc_t indicator_dsc;
        lv_draw_rect_dsc_init(&indicator_dsc);
        indicator_dsc.bg_color = lv_color_hex(0xFF0000); // 红色指示器
        indicator_dsc.bg_opa = LV_OPA_70;
        indicator_dsc.radius = 2;

        lv_coord_t obj_x = lv_obj_get_x(obj);
        lv_coord_t obj_y = lv_obj_get_y(obj);
        lv_coord_t obj_w = widget->size.w;
        lv_coord_t obj_h = widget->size.h;

        lv_coord_t value_range =
            widget->conf->scale.max_value - widget->conf->scale.min_value;
        float value_ratio =
            (float)(widget->current_value - widget->conf->scale.min_value) /
            (float)value_range;

        lv_area_t indicator_area;

        if (widget->conf->type == URQ_RULER_TYPE_HORIZONTAL) {
            lv_coord_t indicator_x = obj_x + (lv_coord_t)(obj_w * value_ratio);
            indicator_area.x1 = indicator_x - 2;
            indicator_area.y1 = obj_y;
            indicator_area.x2 = indicator_x + 2;
            indicator_area.y2 = obj_y + obj_h;
        } else if (widget->conf->type == URQ_RULER_TYPE_VERTICAL) {
            lv_coord_t indicator_y =
                obj_y + obj_h - (lv_coord_t)(obj_h * value_ratio);
            indicator_area.x1 = obj_x;
            indicator_area.y1 = indicator_y - 2;
            indicator_area.x2 = obj_x + obj_w;
            indicator_area.y2 = indicator_y + 2;
        } else {
            // 弧形刻度尺的指示器绘制
            return; // 暂时跳过弧形指示器
        }

        lv_draw_rect(draw_ctx, &indicator_dsc, &indicator_area);
    }
}
