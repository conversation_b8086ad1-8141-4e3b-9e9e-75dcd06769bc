#pragma once

#include "lvgl.h"
#include "urq/conf/map/int_int.h"
#include "urq/conf/map/lvgl.h"
#include "urq/conn/conn.h"
#include "urq/datastruct/circular_link.h"
#include "urq/datastruct/lv_circular.h"
#include "urq/device/map.h"
#include "urq/display/conf.h"
#include "urq/fs/fs.h"
#include "urq/keyboard/trigger_data.h"
#include "urq/preload.h"
#include "urq/widget/parse_context.h"

#ifndef URQ__PROJECT__PROJECT_H
#define URQ__PROJECT__PROJECT_H

// struct _urq_page_t;
struct urq_page_group_t;

/// @brief 工程
typedef struct {
    /// @brief 继承自 lv_obj_t
    lv_obj_t base;
    /// @brief 与服务端的连接
    urq_conn_t conn;
    /// @brief 文件系统
    urq_fs_t fs; // 文件系统
    /// @brief 页面配置列表
    // urq_page_conf_list_t pages;
    /// @brief 当前显示配置
    urq_display_conf_t *display;
    /// @brief 解析上下文
    urq_widget_parse_context_t env;
    /// @brief 显示中的 `urq_page_group` 对象
    lv_obj_t *page_showing;
    /// @brief 显示中的 键盘
    lv_obj_t *keyboard_showing;
    /// @brief 显示中的 消息
    urq_lvgl_map_t *message_showing;
    /// @brief 映射表: 设备 id，设备配置
    urq_device_map_t *device_map;
    /// @brief 页面缓存
    // urq_circular_t *save_pages;
    urq_lvgl_map_t *cache_showing;
    /// @brief 缓存页面ID
    urq_clink_t *save_page_id;
    /// @brief 消息状态表
    urq_int_int_map_t *message_status;
    /// @brief 尺寸
    urq_size_t size;
    // 是否超出显示
    bool beyond_display;
} urq_project_t;

/// @brief 创建 project 组件
/// @param parent 父对象
/// @returns 返回 project 组件
lv_obj_t *urq_project_create(lv_obj_t *parent);

/// @brief 设置工程的主机
/// @param self 项目
/// @param iid  全局实例 id
/// @param ip   主机 ip
/// @param port 主机端口
/// @returns 工程是否设置成功
int urq_project_set_host(lv_obj_t *self, int iid, uint32_t ip, uint16_t port)
    __attribute__((__nonnull__(1))) __attribute__((__warn_unused_result__()));

/// @brief 显示画面
/// @param project 项目
/// @param page_id 页面 id
/// @returns 是否出错
///
/// 如果当前页面已经显示，则不进行任何操作，并且不算出错
///
/// 返回的错误仅表示画面是否被切换，并不包含画面显示过程中引发的新的错误
int urq_project_show_page(lv_obj_t *obj, urq_page_id_t page_id)
    __attribute__((__nonnull__(1)));

/// @brief 显示叠加画面
/// @param project 项目
/// @param page_id 页面 id
/// @param opa 透明度
/// @returns 是否出错
int urq_project_show_overlay_page(
    lv_obj_t *obj, urq_page_id_t page_id, uint8_t opa)
    __attribute__((__nonnull__(1)));

/// @brief 关闭画面
/// @param project 项目
/// @returns 是否出错
int urq_project_close_page(lv_obj_t *obj) __attribute__((__nonnull__(1)));

/// @brief 关闭键盘
/// @param project 项目
/// @returns 是否出错
int urq_project_close_keyboard(lv_obj_t *obj) __attribute__((__nonnull__(1)));

/// @brief 显示画面
/// @param project 项目
/// @param page_id 页面 id
/// @param trigger_widget 触发事件的图元
/// @returns 是否出错
///
/// 如果当前页面已经显示，则不进行任何操作，并且不算出错
int urq_project_show_page_keyboard(
    lv_obj_t *obj, urq_page_id_t page_id,
    urq_keyboard_trigger_data_t *trigger_data) __attribute__((__nonnull__(1)));

/// @brief 显示消息弹窗
/// @param project 项目
/// @param page_id 页面 id
/// @returns 是否出错
lv_obj_t *urq_project_show_page_message(lv_obj_t *obj, urq_page_id_t page_id)
    __attribute__((__nonnull__(1)));

/// @brief 关闭消息弹窗
/// @param project 项目
/// @param page_id 页面 id
/// @returns 是否出错
bool urq_project_close_page_message(lv_obj_t *obj, urq_page_id_t page_id)
    __attribute__((__nonnull__(1)));

/// @brief 半帧事件
/// @param self 项目
/// @returns void
void urq_project_frame_half_cb(lv_obj_t *self);

/// @brief 帧更新回调
/// @param self 项目
/// @returns void
void urq_project_frame_cb(lv_obj_t *self);

// ---------------------------------------------------------

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
/// @brief 强制修改画面
/// @param project 项目
/// @param page_id 页面 id
/// @returns void
void urq_project_show_page_force(lv_obj_t *const obj, int32_t page_id)
    __attribute__((__nonnull__(1)));

/// @brief 重新加载 display 配置
/// @param self 项目
/// @returns void
void urq_project_reload_display(lv_obj_t *self);
#endif

#endif // URQ__PROJECT__PROJECT_H
