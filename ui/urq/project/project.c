#include "urq/project/project.h"
#include "lvgl.h"
#include "urq/errno.h"
#include "urq/fs/fs.h"
#include "urq/ip.h"
#include "urq/log/debug.h"
#include "urq/page/group.h"
#include "urq/platform.h"
#include "urq/preload.h"
#include "urq/project/cb.h"
#include "urq/sleep.h"
#include "urq/user_data.h"
#include "urq_conf/conn/conn.h"
#include "urq_conf/datastruct/circular_link.h"

#include "urq_conf/display.h"
#include "urq_conf/page/conf_map.h"
#include "urq_conf/req/asset.h"
#include <string.h>
#include <unistd.h>

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
#include "urq/project/load.h"
#endif

#define Self urq_project_t

// lvgl object class
static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_PROJECT_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(urq_project_t),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    urq_project_t *project = (urq_project_t *)obj;
    urq_conn_init(&project->conn);

    lv_obj_clear_flag(obj, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_clear_flag(obj, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_style_border_width(obj, 0, 0);

    project->display = urq_malloc(sizeof(urq_display_conf_t));
    urq_display_conf_init_inplace(project->display);

    lv_obj_t *top = lv_obj_create(obj);
    lv_obj_remove_style_all(top);
    lv_obj_set_pos(top, 0, 0);
    lv_obj_set_size(top, URQ_PROJECT_WIDTH, URQ_PROJECT_HEIGHT);
    lv_obj_clear_flag(top, LV_OBJ_FLAG_SCROLLABLE);
    project->display->top_layer = top;

    lv_obj_t *page = lv_obj_create(obj);
    lv_obj_remove_style_all(page);
    lv_obj_set_pos(page, 0, 0);
    lv_obj_set_size(page, URQ_PROJECT_WIDTH, URQ_PROJECT_HEIGHT);
    lv_obj_clear_flag(page, LV_OBJ_FLAG_SCROLLABLE);
    project->display->page_layer = page;

    urq_fs_init_inplace(&project->fs);
    urq_widget_parse_context_init_inplace(&project->env);

    // TODO
    project->cache_showing = urq_lvgl_map_new();
    project->save_page_id = urq_clink_create();

    project->display->pages = NULL;
    project->keyboard_showing = NULL;
    project->message_showing = NULL;
    project->page_showing = NULL;
    project->message_status = NULL;
    project->size.w = URQ_PROJECT_WIDTH;
    project->size.h = URQ_PROJECT_HEIGHT;
    project->beyond_display = false;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *project = (Self *)obj;
    urq_conn_deinit(&project->conn);
    if (project->display != NULL) {
        urq_display_conf_free_inplace(project->display);
        urq_free(project->display);
        project->display = NULL;
    }
    urq_fs_free_inplace(&project->fs);
    urq_widget_parse_context_free_inplace(&project->env);

    // project->top_layer = NULL; // 这个对象不需要释放，它将被 lvgl 管理
    // project->lay= NULL; // 这个对象不需要释放，它将被 lvgl 管理
    project->page_showing = NULL; // 这个对象不需要释放，它将被 lvgl 管理

    if (project->save_page_id) {
        urq_clink_clean(project->save_page_id);
        project->save_page_id = NULL;
    }

    if (project->cache_showing) {
        urq_lvgl_map_free(project->cache_showing);
        project->cache_showing = NULL;
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (lv_obj_event_base(class_p, e) != LV_RES_OK) {
        log_e("event error, errno: %d\n", errno);
        return;
    }
}

lv_obj_t *urq_project_create(lv_obj_t *parent)
{
    // 创建对象
    lv_obj_t *obj = lv_obj_class_create_obj(&URQ_PROJECT_CLASS, parent);
    lv_obj_class_init_obj(obj);

    return obj;
}

int urq_project_set_host(lv_obj_t *self, int iid, uint32_t ip, uint16_t port)
{
    char ip_str[16];
    log_i(
        "set host, iid: %d, ip: %s, port: %d\n", iid,
        urq_ip4_to_string(ip, ip_str), port);
    urq_project_t *project = (urq_project_t *)self;

    // 配置连接
    urq_conn_set_cb_operator(&project->conn, project, urq_project_cb_operator);
    urq_conn_set_cb_page_var(&project->conn, project, urq_project_cb_var);

    if (urq_conn_set_host(&project->conn, iid, ip, port)) {
        log_e("set host error: %s\n", urq_errno_to_string(errno));
        return -1;
    }
    urq_fs_set_host(&project->fs, iid, ip, port);

    log_d("connect to server\n");
    while (urq_conn_connect(&project->conn) != 0) {
        urq_sleep(10);
    }
    log_i("socket connect success\n");

    project->env.is_local = ip == 0;
    project->env.platform = URQ_PLATFORM_LINUX_ARM_V7HF;

    // 加载启动所需的信息
    if (urq_req_wirte_set_asset(&project->conn)) {
        log_e("init conn error: %s\n", urq_errno_to_string(errno));
        return -1;
    }
    return 0;
}

int __urq_project_show_page(
    urq_project_t *const project, lv_obj_t **show, lv_obj_t *parent,
    urq_page_id_t page_id)
{
    urq_page_group_t *page;

    // TODO 缓存返回
    if (parent == NULL) {
        return -1;
    }

    if (*show != NULL) {
        page = (urq_page_group_t *)*show;
        if (page->id == page_id) {
            return 0;
        }
    }

    if (*show != NULL) {
        urq_page_group_close(*show);
        lv_obj_del(*show);
        // lv_obj_add_flag(*show, LV_OBJ_FLAG_HIDDEN);
        *show = NULL;
    }

    urq_page_group_conf_t *page_conf =
        urq_page_group_conf_map_get(project->display->pages, page_id);

    if (page_conf == NULL) {
        log_w("page not found: %d\n", page_id);
        return -1;
    }

    *show = urq_page_group_create(parent);
    if (*show == NULL) {
        log_e("create page error\n");
        return -1;
    }
    urq_page_group_conf(*show, project, page_conf);
    // TODO
    // urq_req_wirte_set_page(&project->conn, (urq_page_id_t)page_id);

    return 0;
}

int urq_project_show_overlay_page(
    lv_obj_t *obj, urq_page_id_t page_id, uint8_t opa)
{
    urq_project_t *project = (urq_project_t *)obj;
    urq_page_group_t *page_showing = (urq_page_group_t *)project->page_showing;

    if (project->page_showing == NULL) {
        return -1;
    }

    if (urq_page_map_has_key(page_showing->pages, page_id)) {
        return 0;
    }

    urq_page_group_conf_t *page_conf =
        urq_page_group_conf_map_get(project->display->pages, page_id);
    if (page_conf == NULL) {
        log_w("page not found: %d\n", page_id);
        return -1;
    }

    lv_obj_t *_show = urq_page_create(project->page_showing);
    if (_show == NULL) {
        log_e("create page error\n");
        return -1;
    }

    urq_page_conf_t conf;
    urq_page_conf_init_inplace(&conf);
    conf.id = (urq_page_id_t)page_id;
    conf.type = page_conf->type;
    lv_obj_move_background(_show);
    lv_obj_set_style_opa(_show, opa, LV_PART_MAIN);
    lv_obj_clear_flag(_show, LV_OBJ_FLAG_CLICKABLE); // 不拦截点击事件
    // TODO

    lv_coord_t w =
        page_conf->size.w == 0 ? project->display->size.w : page_conf->size.w;
    lv_coord_t h =
        page_conf->size.h == 0 ? project->display->size.h : page_conf->size.h;
    lv_obj_set_size(_show, w, h);

    urq_page_conf(_show, project, &conf);
    urq_page_conf_free_inplace(&conf);
    if (urq_page_map_add(page_showing->pages, (urq_page_t *)_show)) {
        log_e("add page error\n");
        return -1;
    }

    return 0;
}

int urq_project_show_page(lv_obj_t *const obj, urq_page_id_t page_id)
{
    urq_project_t *project = (urq_project_t *)obj;

    // if(urq_lvgl_map_has(project->cache_showing, page_id)) {
    //     lv_obj_t *tmp = urq_lvgl_map_get(project->cache_showing, page_id);
    //     if (tmp != NULL) {
    //         lv_obj_add_flag(project->page_showing, LV_OBJ_FLAG_HIDDEN);
    //         project->page_showing = NULL;
    //         project->page_showing = tmp;
    //         // urq_page_group_t *_page = (urq_page_group_t
    //         *)project->page_showing; lv_obj_clear_flag(project->page_showing,
    //         LV_OBJ_FLAG_HIDDEN); return 0;
    //     }else {
    //         urq_project_close_page((lv_obj_t *)project);
    //     }
    // }
    //  if(project->page_showing != NULL) {
    //          urq_project_close_page((lv_obj_t *)project);
    //  }

    __urq_project_show_page(
        project, &project->page_showing, project->display->page_layer, page_id);
    // 页面缓存

    // urq_clink_append(project->save_page_id, page_id);
    // urq_lvgl_map_add(project->cache_showing, page_id, project->page_showing);
    return 0;
}

int urq_project_show_page_keyboard(
    lv_obj_t *obj, urq_page_id_t page_id,
    urq_keyboard_trigger_data_t *trigger_data)
{
    urq_project_t *project = (urq_project_t *)obj;
    __urq_project_show_page(
        project, &project->keyboard_showing, lv_layer_top(), page_id);
    //__urq_project_show_page(
    //    project, &project->keyboard_showing, project->display->top_layer,
    //    page_id);
    urq_page_group_t *page = (urq_page_group_t *)project->keyboard_showing;
    if (page->trigger_data != NULL) {
        urq_keyboard_trigger_data_free_inplace(page->trigger_data);
        urq_free(page->trigger_data);
    }
    page->trigger_data = trigger_data;

    return 0;
}

lv_obj_t *urq_project_show_page_message(lv_obj_t *obj, urq_page_id_t page_id)
{
    // TODO
    urq_project_t *project = (urq_project_t *)obj;
    if (project->message_showing == NULL) {
        project->message_showing = urq_lvgl_map_new();
    }
    lv_obj_t *tmp = NULL;
    // todo
    __urq_project_show_page(project, &tmp, lv_layer_top(), page_id);
    // 缓存画面
    urq_lvgl_map_add(project->message_showing, (uint32_t)page_id, tmp);

    return tmp;
}

bool urq_project_close_page_message(lv_obj_t *obj, urq_page_id_t page_id)
{
    return false;
    urq_project_t *project = (urq_project_t *)obj;
    if (project->message_showing == NULL) {
        return false;
    }
    lv_obj_t *tmp =
        urq_lvgl_map_get(project->message_showing, (uint32_t)page_id);
    if (tmp == NULL) {
        return false;
    }
    urq_lvgl_map_del(project->message_showing, (uint32_t)page_id);
    lv_obj_del(tmp);

    return true;
}

int urq_project_close_page(lv_obj_t *obj)
{
    urq_project_t *project = (urq_project_t *)obj;
    if (project->page_showing != NULL) {
        urq_page_group_close(project->page_showing);
        lv_obj_del(project->page_showing);
        project->page_showing = NULL;
    }
    return 0;
}

int urq_project_close_keyboard(lv_obj_t *obj)
{
    urq_project_t *project = (urq_project_t *)obj;
    if (project->keyboard_showing != NULL) {
        urq_page_group_close(project->keyboard_showing);
        lv_obj_del(project->keyboard_showing);
        project->keyboard_showing = NULL;
    }
    return 0;
}

void urq_project_frame_half_cb(lv_obj_t *self)
{
    Self *project = (Self *)self;
    urq_conn_poll(&project->conn);
}

void urq_project_frame_cb(lv_obj_t *self)
{
    Self *project = (Self *)self;

    urq_project_frame_half_cb(self);

    if (project->keyboard_showing != NULL) {
        log_v(
            "half frame, project: %p, page: %p\n", project,
            project->keyboard_showing);
        urq_page_group_frame_cb(project->keyboard_showing);
    }

    if (project->page_showing != NULL) {
        log_v(
            "half frame, project: %p, page: %p\n", project,
            project->page_showing);
        urq_page_group_frame_cb(project->page_showing);
    }

    if (project->message_showing != NULL) {
        log_v(
            "half frame, project: %p, page: %p\n", project,
            project->message_showing);

        for (khiter_t k = kh_begin(project->message_showing);
             k != kh_end(project->message_showing); ++k) {
            if (kh_exist(project->message_showing, k)) {
                lv_obj_t *page = kh_val(project->message_showing, k);
                urq_page_group_frame_cb(page);
            }
        }
    }
}

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM

void urq_project_show_page_force(lv_obj_t *obj, int32_t page_id)
{
    Self *project = (Self *)obj;
    if (project->page_showing != NULL) {
        lv_obj_del(project->page_showing);
        project->page_showing = NULL;
    }
    urq_project_show_page(obj, page_id);
}

void urq_project_reload_display(lv_obj_t *self)
{
    urq_project_t *project = (urq_project_t *)self;
    urq_project_load_display(project);
    if (project->page_showing != NULL) {
        lv_obj_del(project->page_showing);
        project->page_showing = NULL;
    }

    urq_project_load_display(project);
}
#endif
