#include "urq/project/load.h"
#include "klib/khash.h"
#include "lvgl.h"
#include "urq/errno.h"
#include "urq/fs/file.h"
#include "urq/fs/project.h"
#include "urq/log/verbose.h"
#include "urq/path.h"
#include "urq/preload.h"
#include "urq/project/project.h"
#include "urq/user_data.h"
#include "urq_conf/project.h"
#include "urq_conf/system_variable.h"
#include "urq_conf/time/cb.h"
#include "urq_conf/util/exit.h"
#include "urq_parse/display.h"
#include "urq_parse/i18n_map.h"
#include "urq_parse/project.h"
#include "urq_parse/style_map.h"

void urq_project_parse_atags(void *self, const uint8_t *const data, size_t size)
{
    urq_used(self);
    urq_used(data);
    urq_used(size);
    return;
}

void urq_project_load_atags(void *self)
{
    urq_used(self);
    // urq_project_t *project = (urq_project_t *)self;
    // urq_fs_file_t *file;    // 文件内容
    // urq_device_id_t id = 0; // 当前需要加载的页面
    // urq_atag_device_t *device_tags;
    // bool not_found;

    // if (project->atags == NULL) {
    //     // project->atags = urq_atag_project_new();
    //     log_i("create project atags\n");

    //    if (project->atags == NULL) {
    //        log_e("create project atags failed\n");
    //        urq_exit(1);
    //        return;
    //    }
    //}

    // if (urq_atag_project_get_device(project->atags, 0) == NULL) {
    //     id = 0;
    // } else {
    //     not_found = true;

    //    // TODO
    //    for (khiter_t k = kh_begin(project->device_map);
    //         k != kh_end(project->device_map); ++k) {
    //        if (kh_exist(project->device_map, k)) {
    //            id = (urq_device_id_t)kh_key(project->device_map, k);
    //            if (urq_atag_project_get_device(project->atags, id) == NULL) {
    //                not_found = false;
    //                break;
    //            }
    //        }
    //    }

    //    if (not_found) {
    //        log_i("all atags loaded\n");
    //        urq_page_id_t page = (urq_page_id_t)project->var_sys.registers
    //                                 .data[URQ_DEVICE_LOCAL_ADDR_BASIC_PAGE_NO];
    //        urq_project_show_page(self, page);
    //        return;
    //    }
    //}

    // log_d("begin load atags, device: %d\n", id);
    // file = NULL;
    // if (urq_fs_project_load_atags(project, id, &file)) {
    //     if (errno == URQ_EPENDING || errno == URQ_RELOAD) {
    //         log_v("var conf pending, page: %d\n", id);
    //         urq_time_cb_add1(0, urq_project_load_atags, urq_noop1, self);
    //         return;
    //     } else {
    //         log_e("atags load error, errno: %d\n", errno);
    //         return;
    //     }
    // }

    // log_d("atags load success, page: %d\n", id);
    // if (urq_parse__atags(
    //         project->atags, id, file->size, urq_fs_file_data(file))) {
    //     urq_fs_file_free(file);
    //     log_e("atags parse failed, errno: %d\n", errno);
    //     urq_exit(1);
    //     return;
    // }
    // urq_fs_file_free(file);

    // device_tags = urq_atag_project_get_device(project->atags, id);
    // log_d("atags load success, page: %d, size: %d\n", id,
    // kh_size(device_tags));

    // if (id == 0) {
    //     // 如果是画面 0， 则创建工程变量
    //     if (project->var_table != NULL) {
    //         log_e("var table already exists\n");
    //         return;
    //     }

    //    device_tags = urq_atag_project_get_device(project->atags, 0);
    //    if (device_tags == NULL) {
    //        log_e("device 0 not found\n");
    //        urq_exit(1);
    //        return;
    //    }
    //    project->var_table = urq_var_table_from_atag_device(device_tags);
    //    if (project->var_table == NULL) {
    //        log_e("create var table failed\n");
    //        urq_exit(1);
    //        return;
    //    }
    //}
    // urq_project_load_atags(self);
}

void urq_project_load_device(void *self)
{
    // urq_project_t *project = (urq_project_t *)self;
    // urq_fs_file_t *file = NULL;

    // if (urq_fs_project_load_device(project, &file)) {
    //     urq_free_if_not_null(file);
    //     if (errno == URQ_EPENDING || errno == URQ_RELOAD) {
    //         log_v("device config pending\n");
    //         urq_time_cb_add1(0, urq_project_load_device, urq_noop1, project);
    //         return;
    //     } else {
    //         log_e(
    //             "load device failed, errno: %d %s\n", errno,
    //             urq_errno_to_string(errno));
    //         return;
    //     }
    // }

    // urq_project_parse_device(self, urq_fs_file_data(file), file->size);
    // urq_fs_file_free(file);
    // if (project->device_map == NULL) {
    //     log_e("parse device failed, errno: %d\n", errno);
    //     urq_exit(1);
    //     return;
    // }
    urq_project_load_project(self);
}

void urq_project_parse_display(
    void *self, const uint8_t *const data, size_t size)
{
    urq_project_t *project = (urq_project_t *)self;
    if (urq_parse__display(
            &project->env, &project->env.var_sys, data, size,
            project->display)) {
        log_e("display parse error, errno: %d\n", errno);
        return;
    }

    if (project->env.var_sys.registers
            .data[URQ_DEVICE_LOCAL_ADDR_BASIC_PAGE_NO] == 0) {
        project->env.var_sys.registers
            .data[URQ_DEVICE_LOCAL_ADDR_BASIC_PAGE_NO] =
            (int16_t)project->display->main_page_id;
    }

    urq_project_show_page(
        self, (urq_page_id_t)project->env.var_sys.registers
                  .data[URQ_DEVICE_LOCAL_ADDR_BASIC_PAGE_NO]);
}

void urq_project_load_display(void *self)
{
    urq_project_t *project = (urq_project_t *)self;
    urq_fs_file_t *file;
    // int32_t page_id;

    if (urq_fs_project_load_display(project, &file)) {
        if (errno == URQ_EPENDING || errno == URQ_RELOAD) {
            log_v("display config pending\n");
            urq_time_cb_add1(0, urq_project_load_display, urq_noop1, project);
            return;
        } else {
            log_e("load display failed, errno: %d\n", errno);
            return;
        }
    }

    urq_project_parse_display(self, urq_fs_file_data(file), file->size);

    urq_fs_file_free(file);

    // urq_project_load_atags(self);
}

void urq_project_load_language(void *self)
{
    urq_project_t *project = (urq_project_t *)self;
    urq_fs_file_t *file;

    if (urq_fs_project_load_language(project, 0, &file)) {
        // urq_fs_file_free(file);
        if (errno == URQ_EPENDING || errno == URQ_RELOAD) {
            log_v("language config pending\n");
            urq_time_cb_add1(0, urq_project_load_language, urq_noop1, project);
            return;
        } else {
            log_e("load language failed, errno: %d\n", errno);
            return;
        }
    }

    urq_project_parse_language(self, urq_fs_file_data(file), file->size);

    // urq_project_load_theme_style(project);
    urq_project_load_display(project);

    urq_fs_file_free(file);
}

void urq_project_reload_language(void *self)
{
    urq_project_t *project = (urq_project_t *)self;
    urq_fs_file_t *file;

    if (urq_fs_project_load_language(project, 0, &file)) {
        // urq_fs_file_free(file);
        if (errno == URQ_EPENDING || errno == URQ_RELOAD) {
            log_v("language config pending\n");
            urq_time_cb_add1(
                0, urq_project_reload_language, urq_noop1, project);
            return;
        } else {
            log_e("load language failed, errno: %d\n", errno);
            return;
        }
    }

    urq_project_parse_language(self, urq_fs_file_data(file), file->size);

    urq_fs_file_free(file);
}

void urq_project_parse_language(
    void *self, const uint8_t *const data, size_t size)
{
    urq_project_t *project = (urq_project_t *)self;
    if (project->env.i18n_map != NULL) {
        urq_i18n_map_free(project->env.i18n_map);
    }
    project->env.i18n_map = urq_i18n_map_new();
    if (urq_parse__i18n_map(
            data, size,
            (urq_i18n_lang_id_t)URQ_GET_DEVICE_SYSTEM_VAR(
                project->env.var_sys, LANGUAGE),
            project->env.i18n_map)) {
        log_e("parse language failed, errno: %d\n", errno);
        return;
    }
}

void urq_project_parse_project(
    void *self, const uint8_t *const data, size_t size)
{

    urq_project_t *project = (urq_project_t *)self;
    urq_used(project);
    urq_project_conf_t conf;

    if (urq_parse__project(data, size, &conf)) {
        log_e("project parse error, errno: %d\n", errno);
        urq_project_conf_free_inplace(&conf);
        return;
    }

    // TODO
    // project->conf = conf;
}

void urq_project_load_project(void *self)
{
    urq_project_t *project = (urq_project_t *)self;
    urq_fs_file_t *file;

    if (urq_fs_project_load_project(project, &file)) {
        log_i("load project.pd failed\n");
        if (errno == URQ_EPENDING || errno == URQ_RELOAD) {
            log_v("project config pending\n");
            urq_time_cb_add1(1, urq_project_load_project, urq_noop1, project);
            return;
        } else {
            log_e("read project.pd error, errno: %d\n", errno);
            return;
        }
    }

    urq_project_parse_project(self, urq_fs_file_data(file), file->size);

    // urq_project_load_language(project);
    urq_project_load_theme_style(project);
}

void urq_project_parse_theme_style(
    void *self, const uint8_t *const data, size_t size)
{
    urq_project_t *project = (urq_project_t *)self;

    if (project->env.theme_style != NULL) {
        urq_theme_style_free(project->env.theme_style);
    }
    project->env.theme_style = urq_theme_style_new();

    if (project->env.theme_color != NULL) {
        urq_theme_color_free(project->env.theme_color);
    }
    project->env.theme_color = urq_theme_color_new();
    if (urq_parse__style_map(
            &project->env,
            (uint16_t)project->env.var_sys.registers
                .data[URQ_DEVICE_LOCAL_ADDR_THEME],
            data, size) == -1) {
        log_e("parse theme style failed, errno: %d\n", errno);
        return;
    }
}

void urq_project_load_theme_style(void *self)
{
    urq_project_t *project = (urq_project_t *)self;
    urq_fs_file_t *file;

    if (urq_fs_project_load_theme_style(project, &file)) {
        if (errno == URQ_EPENDING || errno == URQ_RELOAD) {
            log_v("theme style config pending\n");
            urq_time_cb_add1(
                0, urq_project_load_theme_style, urq_noop1, project);
            return;
        } else {
            log_e("load theme style failed, errno: %d\n", errno);
            return;
        }
        urq_fs_file_free(file);
    }

    urq_project_parse_theme_style(self, urq_fs_file_data(file), file->size);
    urq_project_load_language(project);

    urq_fs_file_free(file);
}

void urq_project_load_files(void *self)
{
    urq_project_t *project = (urq_project_t *)self;
    urq_fs_project_load_device_async(project);
    urq_fs_project_load_project_async(project);
    urq_fs_project_load_language_async(project, URQ_PATH_CONF_DEFAULT_FILE);
    urq_fs_project_load_theme_style_async(project);
    urq_fs_project_load_display_async(project);
    // urq_fs_project_load_atags_async(project, URQ_PATH_CONF_DEFAULT_FILE);
}
