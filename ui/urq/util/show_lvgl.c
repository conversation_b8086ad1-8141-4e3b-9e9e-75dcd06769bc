#include "show_lvgl.h"
#include "urq/errno.h"
#include "urq/i18n/id.h"
#include "urq/log/debug.h"
#include "urq/path.h"
#include "urq/util/color.h"
#include "urq/util/font.h"
#include <string.h>

void urq_init_widget_show(lv_obj_t *obj)
{
    urq_used(obj);
    return;
}

// TODO
// 优化相同路径，
// 是否已经显示检测
void urq_show_lvgl_image(void *u_data, void *u_obj, void *u_graphic)
{
    urq_user_data_t *ud = (urq_user_data_t *)u_data;
    lv_obj_t *obj = (lv_obj_t *)u_obj;
    urq_graphic_t *graphic = (urq_graphic_t *)u_graphic;

    if (graphic == NULL) {
        log_e("graphic is NULL\n");
        return;
    }

    char tmp_path[URQ_PATH_MAX] = {0};
    snprintf(
        tmp_path, URQ_PATH_MAX, "%s/%d/%d.bin",
        ud->project->fs.project_root_path, URQ_PATH_CONF_FOLDER_IMAGE,
        graphic->src);

    if (urq_fs_load_file(&ud->project->fs, tmp_path)) {
        log_w(
            "image pending, path: %s | %s, %d\n", tmp_path,
            urq_errno_to_string(errno), errno);
        if (errno == URQ_EPENDING || errno == URQ_RELOAD) {
            if (urq_page_add_frame_cb3(
                    ud->page, urq_show_lvgl_image, urq_noop3, u_data, u_obj,
                    u_graphic)) {
                log_w("add frame callback error, page: %d\n", ud->page->id);
                return;
            }
        }
        return;
    }

    if (urq_fs_exists(&ud->project->fs, tmp_path, NULL)) {
        log_d("image not exits, path: %s\n", tmp_path);
        return;
    }

    // 设置背景图片
    lv_img_set_src(obj, tmp_path);
    // lv_img_set_zoom(obj, 300);
    //  设置透明度
    lv_obj_set_style_img_opa(obj, graphic->opacity, LV_PART_MAIN);

    // lv_obj_set_style_img_recolor(
    //     obj,
    //         urq_get_color(ud->project->env.theme_color, &graphic->color),
    //     LV_PART_MAIN);

    // lv_obj_set_style_img_recolor_opa(obj,
    // urq_get_opacity(ud->project->env.theme_color, &graphic->color),
    // LV_PART_MAIN);
}

void urq_show_lvgl_text(void *u_data, void *u_obj, void *u_text)
{
    urq_used(u_obj);
    urq_user_data_t *ud = (urq_user_data_t *)u_data;
    urq_text_t *text = (urq_text_t *)u_text;

    if (text->tag_id >= 0) {
        text->content =
            urq_i18n_map_get(ud->project->env.i18n_map, (uint32_t)text->tag_id);
    }

    printf(
        "=====> [widgetid:%d] [content:%s], [tagid:%d], [language:%d]\n",
        ud->id, text->content, text->tag_id,
        URQ_GET_DEVICE_SYSTEM_VAR(ud->project->env.var_sys, LANGUAGE));

    if (text->content != NULL) {
        lv_label_set_text(ud->label, text->content);
        lv_obj_refr_size(ud->label);
    }

    // if (text->font != NULL) {
    //     urq_set_font(ud->label, text->font);
    //     lv_obj_align(ud->label, text->font->text_align, 0, 0);
    // }
}

void urq_show_lvgl_font_style(void *u_data, void *u_obj, void *u_font)
{
    urq_user_data_t *ud = (urq_user_data_t *)u_data;
    lv_obj_t *obj = (lv_obj_t *)u_obj;
    urq_font_t *font = (urq_font_t *)u_font;

    char tmp_path[URQ_PATH_MAX] = {0};
    snprintf(
        tmp_path, URQ_PATH_MAX, "%s/%d/%d.bin",
        ud->project->fs.project_root_path, URQ_PATH_CONF_FOLDER_FONT, font->id);

    if (urq_fs_load_file(&ud->project->fs, tmp_path)) {
        log_w(
            "font pending, path: %s | %s, %d\n", tmp_path,
            urq_errno_to_string(errno), errno);
        if (errno == URQ_EPENDING || errno == URQ_RELOAD) {
            if (urq_page_add_frame_cb3(
                    ud->page, urq_show_lvgl_font_style, urq_noop3, u_data,
                    u_obj, u_font)) {
                log_w("add frame callback error, page: %d\n", ud->page->id);
                return;
            }
        }
        return;
    }

    if (urq_fs_exists(&ud->project->fs, tmp_path, NULL)) {
        log_d("font not exits, path: %s\n", tmp_path);
        return;
    }

    lv_font_t *font_lv = lv_font_load(tmp_path);

    // 设置字体
    if (font_lv != NULL) {
        lv_obj_set_style_text_font(obj, font_lv, LV_PART_MAIN);
    }
}
