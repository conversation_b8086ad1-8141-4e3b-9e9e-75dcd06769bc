#include "style.h"
#include "urq/project/project.h"
#include "urq/user_data.h"
#include "urq/util/color.h"
#include "urq/util/font.h"
#include "urq/util/state.h"
#include "urq/widget/state.h"

void urq_set_style_border(
    lv_obj_t *obj, urq_style_border_t *border, lv_style_selector_t selector)
{
    urq_user_data_t *ud = urq_obj_get_user_data(obj);
    urq_theme_color_t *theme_color = ud->project->env.theme_color;
    if (border->width > 0) {
        lv_border_side_t v = 0;

        if (border->left)
            v |= LV_BORDER_SIDE_LEFT;
        if (border->top)
            v |= LV_BORDER_SIDE_TOP;
        if (border->right)
            v |= LV_BORDER_SIDE_RIGHT;
        if (border->bottom)
            v |= LV_BORDER_SIDE_BOTTOM;

        if (v != 0) {
            // 设置边框颜色
            lv_obj_set_style_border_color(
                obj, urq_get_color(theme_color, &border->color), selector);
            // 设置边框宽度
            lv_obj_set_style_border_width(obj, border->width, selector);
            // 设置边框样式
            lv_obj_set_style_border_side(obj, v, selector);
        }
    }
}

void urq_set_style_draw_border(
    urq_widget_parse_context_t *env, lv_draw_rect_dsc_t *rect_dsc,
    urq_style_border_t *border)
{
    urq_theme_color_t *theme_color = env->theme_color;
    rect_dsc->border_color = urq_get_color(theme_color, &border->color);
    rect_dsc->border_width = border->width;
    rect_dsc->border_opa = urq_get_opacity(theme_color, &border->color);

    // unsigned char v = 0;

    // if (border->left)
    //     v |= LV_BORDER_SIDE_LEFT;
    // if (border->top)
    //     v |= LV_BORDER_SIDE_TOP;
    // if (border->right)
    //     v |= LV_BORDER_SIDE_RIGHT;
    // if (border->bottom)
    //     v |= LV_BORDER_SIDE_BOTTOM;

    // TODO
    // dsc->rect_dsc->border_side = (unsigned char)v;
}

void urq_set_style_shadow(
    lv_obj_t *obj, urq_style_shadow_t *shadow, lv_style_selector_t selector)
{
    urq_user_data_t *ud = urq_obj_get_user_data(obj);
    urq_theme_color_t *theme_color = ud->project->env.theme_color;
    if (shadow->enable) {
        /// 阴影宽度
        lv_obj_set_style_shadow_width(obj, shadow->width, selector);
        /// 阴影扩散
        lv_obj_set_style_shadow_spread(obj, shadow->spread, selector);
        /// 阴影偏移
        lv_obj_set_style_shadow_ofs_x(obj, shadow->offset_x, selector);
        lv_obj_set_style_shadow_ofs_y(obj, shadow->offset_y, selector);
        /// 阴影颜色
        lv_obj_set_style_shadow_color(
            obj, urq_get_color(theme_color, &shadow->bg), selector);
    }
}

void urq_set_style_draw_shadow(
    urq_widget_parse_context_t *env, lv_draw_rect_dsc_t *rect_dsc,
    urq_style_shadow_t *shadow)
{
    if (shadow->enable) {
        urq_theme_color_t *theme_color = env->theme_color;
        rect_dsc->shadow_color = urq_get_color(theme_color, &shadow->bg);
        rect_dsc->shadow_width = shadow->width;
        rect_dsc->shadow_ofs_x = shadow->offset_x;
        rect_dsc->shadow_ofs_y = shadow->offset_y;
        rect_dsc->shadow_spread = shadow->spread;
        rect_dsc->shadow_opa = urq_get_opacity(theme_color, &shadow->bg);
    }
}

void urq_set_style_child_bg(
    lv_obj_t *obj, urq_project_t *project, urq_color_ref_t bg,
    lv_style_selector_t selector)
{
    urq_theme_color_t *theme_color = project->env.theme_color;
    lv_obj_set_style_bg_color(obj, urq_get_color(theme_color, &bg), selector);
    lv_obj_set_style_bg_opa(obj, urq_get_opacity(theme_color, &bg), selector);
}

void urq_set_style_bg(
    lv_obj_t *obj, urq_color_ref_t bg, lv_style_selector_t selector)
{
    urq_user_data_t *ud = urq_obj_get_user_data(obj);
    urq_set_style_child_bg(obj, ud->project, bg, selector);
}

void urq_set_style_page_bg(
    urq_widget_parse_context_t *env, lv_obj_t *obj, urq_color_ref_t bg)
{
    urq_theme_color_t *theme_color = env->theme_color;
    lv_obj_set_style_bg_color(obj, urq_get_color(theme_color, &bg), 0);
    lv_obj_set_style_bg_opa(obj, urq_get_opacity(theme_color, &bg), 0);
}

void urq_set_style_draw_bg(
    urq_widget_parse_context_t *env, lv_draw_rect_dsc_t *rect_dsc,
    urq_color_ref_t bg)
{
    urq_theme_color_t *theme_color = env->theme_color;
    rect_dsc->bg_color = urq_get_color(theme_color, &bg);
    rect_dsc->bg_opa = urq_get_opacity(theme_color, &bg);
}

void urq_set_draw_style(
    urq_widget_parse_context_t *env, lv_obj_draw_part_dsc_t *dsc,
    urq_style_t *style)
{
    // 设置阴影
    if (style->shadow != NULL) {
        urq_set_style_draw_shadow(env, dsc->rect_dsc, style->shadow);
    }
    // 设置边框
    if (style->border != NULL) {
        urq_set_style_draw_border(env, dsc->rect_dsc, style->border);
    }
    // 设置背景
    urq_set_style_draw_bg(env, dsc->rect_dsc, style->bg);
}

void urq_set_style(
    lv_obj_t *obj, urq_style_t *style, lv_style_selector_t selector)
{
    // 设置阴影
    if (style->shadow != NULL) {
        urq_set_style_shadow(obj, style->shadow, selector);
    }
    // 设置边框
    if (style->border != NULL) {
        urq_set_style_border(obj, style->border, selector);
    }
    // 设置背景
    urq_set_style_bg(obj, style->bg, selector);

    urq_set_font(obj, &style->font);
    // urq_widget_data w_data;
    // w_data.data.color_value = &style->bg;
    // urq_widget_property_map_add(
    //     page->widget_property_map, (uint32_t)id,
    //     URQ_PROPERTY_ID_STYLE_BACKGROUND_COLOR, w_data);
}

void urq_set_style_line(
    lv_obj_t *obj, urq_project_t *project, urq_style_line_t *line,
    lv_style_selector_t selector)
{
    urq_theme_color_t *theme_color = project->env.theme_color;
    if (line->dash_gap != 0) {
        lv_obj_set_style_line_dash_gap(obj, line->dash_gap, selector);
    }
    if (line->dash_width != 0) {
        lv_obj_set_style_line_dash_width(obj, line->dash_width, selector);
    }
    if (line->width != 0) {
        lv_obj_set_style_line_width(obj, line->width, selector);
    }
    lv_obj_set_style_line_color(
        obj, urq_get_color(theme_color, &line->color), selector);

    // TODO
    urq_style_line_print(line);
}

void urq_set_style_draw_line(
    urq_widget_parse_context_t *env, lv_draw_line_dsc_t *dsc,
    urq_style_line_t *line)
{
    urq_theme_color_t *theme_color = env->theme_color;
    dsc->width = line->width;
    dsc->dash_gap = line->dash_gap;
    dsc->dash_width = line->dash_width;
    dsc->raw_end = line->round_end;

    dsc->color = urq_get_color(theme_color, &line->color);
    dsc->round_start = line->round_start;
    dsc->round_end = line->round_end;
    dsc->opa = urq_get_opacity(theme_color, &line->color);
}

void urq_set_style_point(lv_obj_t *obj, urq_style_point_t *point)
{
    if (point->style->border != NULL) {
        urq_set_style_border(obj, point->style->border, LV_PART_INDICATOR);
    }
    if (point->style->shadow != NULL) {
        urq_set_style_shadow(obj, point->style->shadow, LV_PART_INDICATOR);
    }
    urq_set_style_bg(obj, point->style->bg, LV_PART_INDICATOR);
    if (point->radius > 0) {
        lv_obj_set_style_radius(obj, point->radius, LV_PART_INDICATOR);
    } else {
        lv_obj_set_style_width(obj, point->width, LV_PART_INDICATOR);
        lv_obj_set_style_height(obj, point->height, LV_PART_INDICATOR);
    }
}

void urq_set_style_draw_part_point(
    urq_widget_parse_context_t *env, lv_obj_draw_part_dsc_t *dsc,
    urq_style_point_t *point)
{
    if (point->style->border != NULL) {
        urq_set_style_draw_border(env, dsc->rect_dsc, point->style->border);
    }
    if (point->style->shadow != NULL) {
        urq_set_style_draw_shadow(env, dsc->rect_dsc, point->style->shadow);
    }

    urq_set_style_draw_bg(env, dsc->rect_dsc, point->style->bg);
    if (point->radius > 0) {
        dsc->rect_dsc->radius = point->radius;
        dsc->draw_area->x1 = dsc->draw_area->x2 - point->radius;
        dsc->draw_area->y1 = dsc->draw_area->y2 - point->radius;
        dsc->draw_area->x2 = dsc->draw_area->x1 + point->radius;
        dsc->draw_area->y2 = dsc->draw_area->y1 + point->radius;
    } else {
        dsc->draw_area->x1 = dsc->draw_area->x2 - point->width;
        dsc->draw_area->y1 = dsc->draw_area->y2 - point->height;
        dsc->draw_area->x2 = dsc->draw_area->x1 + point->width;
        dsc->draw_area->y2 = dsc->draw_area->y1 + point->height;
    }
}

void urq_set_style_draw_main_point(
    urq_widget_parse_context_t *env, lv_draw_ctx_t *draw_ctx,
    lv_coord_t center_x, lv_coord_t center_y, urq_style_point_t *point)
{
    lv_draw_rect_dsc_t rect_dsc;
    lv_draw_rect_dsc_init(&rect_dsc);
    if (point->style->border != NULL) {
        urq_set_style_draw_border(env, &rect_dsc, point->style->border);
    }
    if (point->style->shadow != NULL) {
        urq_set_style_draw_shadow(env, &rect_dsc, point->style->shadow);
    }

    urq_set_style_draw_bg(env, &rect_dsc, point->style->bg);
    lv_area_t p_area;
    if (point->radius > 0) {
        rect_dsc.radius = point->radius;
        p_area.x1 = center_x - point->radius;
        p_area.y1 = center_y - point->radius;
        p_area.x2 = center_x + point->radius;
        p_area.y2 = center_y + point->radius;
    } else {
        p_area.x1 = center_x - point->width;
        p_area.y1 = center_y - point->height;
        p_area.x2 = center_x + point->width;
        p_area.y2 = center_y + point->height;
    }
    lv_draw_rect(draw_ctx, &rect_dsc, &p_area);
}
