#include "urq/util/curve.h"

void urq_curve_set_ticks(
    lv_obj_t *obj, urq_scale_t *scale, uint8_t axis, bool show)
{
    lv_chart_set_axis_tick(
        obj, axis, scale->main_tick_len, scale->sub_tick_len,
        scale->main_tick_count, scale->sub_tick_count, show,
        scale->draw_tick_len);
    if (scale->min_value != -1 && scale->max_value != -1) {
        lv_chart_set_range(obj, axis, scale->min_value, scale->max_value);
    }
}
