#include "urq/util/shape.h"
#include <math.h>

#define M_PI 3.14159265358979323846264338327950288

void urq_calculate_half_arc_points(
    lv_point_t *points, int num_points, lv_point_t center, lv_coord_t lr,
    lv_coord_t sr, float start_angle, float end_angle, float rotate_angle)
{
    for (int i = 0; i < num_points; i++) {
        float t = start_angle + (end_angle - start_angle) * (float)i /
                                    (float)(num_points - 1);
        float cos_t = cosf(t);
        float sin_t = sinf(t);
        float cos_theta_rot = cosf(rotate_angle);
        float sin_theta_rot = sinf(rotate_angle);

        points[i].x = center.x + (lv_coord_t)(lr * cos_t * cos_theta_rot -
                                              sr * sin_t * sin_theta_rot);
        points[i].y = center.y + (lv_coord_t)(lr * cos_t * sin_theta_rot +
                                              sr * sin_t * cos_theta_rot);
    }
}

void urq_calculate_fan_points(
    lv_point_t *points, int num_points, lv_point_t center, lv_coord_t lr,
    lv_coord_t sr, float start_angle, float end_angle, float rotate_angle)
{
    urq_calculate_half_arc_points(
        points, num_points - 1, center, lr, sr, start_angle, end_angle,
        rotate_angle);
    points[num_points - 1] = points[0];
}

void urq_calculate_fan_ring_points(
    lv_point_t *points, int num_points, lv_point_t center, lv_coord_t lr_outer,
    lv_coord_t sr_outer, lv_coord_t lr_inner, lv_coord_t sr_inner,
    float start_angle, float end_angle, float rotate_angle)
{
    int idx = 0;
    int cell_num = num_points / 2;
    // 外弧点（从 theta_start 到 theta_end）
    urq_calculate_half_arc_points(
        &points[idx], cell_num - 1, center, lr_outer, sr_outer, start_angle,
        end_angle, rotate_angle);
    idx += cell_num - 1;

    // 直线1：外弧终点到内弧终点（重复外弧终点）
    points[idx].x = points[idx - 1].x;
    points[idx].y = points[idx - 1].y;
    idx++;

    // 内弧点（从 theta_end 到 theta_start，反向）
    urq_calculate_half_arc_points(
        &points[idx], cell_num - 1, center, lr_inner, sr_inner, end_angle,
        start_angle, rotate_angle);
    idx += cell_num - 1;

    // 直线2：内弧起点到外弧起点（闭合）
    points[idx].x = points[0].x;
    points[idx].y = points[0].y;
    idx++;
}
