#include "urq/util/key_func.h"
#include "urq/page/group.h"
#include "urq/project/project.h"
#include "urq_conf/data/type.h"
#include "urq_conf/widget/keyboard.h"

static bool is_upper_keyboard = true;

void urq_keyboard_key_clear(lv_obj_t *obj) { lv_textarea_set_text(obj, ""); }

void urq_keyboard_key_enter(lv_obj_t *obj, void *arg1)
{
    const char *text = (const char *)arg1;
    urq_project_t *project = (urq_project_t *)obj;
    urq_page_group_t *keyboard = (urq_page_group_t *)project->keyboard_showing;
    urq_keyboard_trigger_data_t *trigger_data = keyboard->trigger_data;

    bool is_false = true;

    if (trigger_data != NULL) {
        if (trigger_data->type == URQ_WIDGET_TYPE_STRING) {
            size_t len = strlen(text);
            if (len >= trigger_data->min_value->uint16_value &&
                len <= trigger_data->max_value->uint16_value) {
                is_false = false;
            }
        } else if (trigger_data->type == URQ_WIDGET_TYPE_NUMBER) {
            double min_value = urq_data_to_double(
                trigger_data->min_value, trigger_data->min_value->data_format);
            double max_value = urq_data_to_double(
                trigger_data->max_value, trigger_data->max_value->data_format);

            double value = atof(text);
            if (value >= min_value && value <= max_value) {
                is_false = false;
            }
        }
    }

    if (!is_false) {
        lv_textarea_set_text(trigger_data->input, text);
        // TODO
    }
    // 取消则不关闭键盘 只关闭弹窗
    // 确认才关闭键盘
    urq_project_close_keyboard(obj);
}

void urq_keyboard_key_space(lv_obj_t *obj) { lv_textarea_add_text(obj, " "); }

void urq_keyboard_key_switch_number(lv_obj_t *obj)
{
    urq_used(urq_keyboard_number);
    // lv_keyboard_set_map(
    //     obj, LV_KEYBOARD_MODE_NUMBER, urq_keyboard_number,
    //     urq_keyboard_number_ctrl);
    lv_btnmatrix_set_map(obj, urq_keyboard_number1);
    lv_btnmatrix_set_ctrl_map(obj, urq_keyboard_number1_ctrl);
}

void urq_keyboard_key_switch_abc(lv_obj_t *obj)
{
    if (is_upper_keyboard) {
        is_upper_keyboard = false;
        lv_btnmatrix_set_map(obj, urq_keyboard_upper_letter);
        // lv_keyboard_set_map(
        //     obj, LV_KEYBOARD_MODE_TEXT_UPPER,
        //     urq_keyboard_upper_letter,
        //     urq_keyboard_lower_letter_ctrl);
    } else {
        is_upper_keyboard = true;
        lv_btnmatrix_set_map(obj, urq_keyboard_lower_letter);
        // lv_keyboard_set_map(
        //     obj, LV_KEYBOARD_MODE_TEXT_LOWER,
        //     urq_keyboard_lower_letter,
        //     urq_keyboard_lower_letter_ctrl);
    }
}
