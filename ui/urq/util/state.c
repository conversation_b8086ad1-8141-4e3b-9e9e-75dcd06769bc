#include "urq/util/state.h"
#include "urq/style/style.h"
#include "urq/util/show_lvgl.h"
#include "urq/util/style.h"
#include "urq/widget/state.h"

bool urq_set_state_graphic(
    lv_obj_t *udata, lv_obj_t *obj, urq_graphic_ptr_list_t *graphics,
    urq_state_t state)
{
    if (graphics != NULL && graphics->size > state) {
        urq_graphic_t *graphic = graphics->data[state];
        urq_show_lvgl_image(udata, obj, graphic);
        return true;
    }
    return false;
}

bool urq_set_state_graphic_with_status(
    lv_obj_t *udata, lv_obj_t *obj, urq_graphic_ptr_list_t *graphics,
    urq_state_t state, urq_no_state_way_t no_state_way)
{
    int rt = urq_set_state_graphic(udata, obj, graphics, state);
    if (rt) {
        if (lv_obj_has_flag(obj, LV_OBJ_FLAG_HIDDEN) &&
            no_state_way == URQ_NO_STATE_WAY_USE_BLANK) {
            lv_obj_clear_flag(obj, LV_OBJ_FLAG_HIDDEN);
        }

    } else {
        if (no_state_way == URQ_NO_STATE_WAY_USE_BLANK) {
            if (!lv_obj_has_flag(obj, LV_OBJ_FLAG_HIDDEN))
                lv_obj_add_flag(obj, LV_OBJ_FLAG_HIDDEN);
        } else {
            // 使用最后一个状态
            if (graphics != NULL && graphics->size > 0) {
                urq_set_state_graphic(
                    udata, obj, graphics, (urq_state_t)(graphics->size - 1));
            }
        }
    }
    return true;
}

bool urq_set_state_style(
    urq_project_t *project, lv_obj_t *obj, urq_style_ptr_list_t *styles,
    urq_state_t state)
{
    if (styles != NULL && styles->size > state) {
        printf(
            "[urq_set_state_style]: state: %d, size: %d\n", state,
            styles->size);
        urq_used(project);
        urq_style_t *style = styles->data[state];
        urq_set_style(obj, style, LV_PART_MAIN);
        return true;
    }
    return false;
}

bool urq_set_state_style_with_status(
    urq_project_t *project, lv_obj_t *obj, urq_style_ptr_list_t *styles,
    urq_state_t state, urq_no_state_way_t no_state_way)
{
    int rt = urq_set_state_style(project, obj, styles, state);
    if (rt) {

    } else {
        if (no_state_way == URQ_NO_STATE_WAY_USE_BLANK) {
            lv_obj_set_style_border_width(obj, 0, LV_PART_MAIN);
            lv_obj_set_style_border_opa(obj, LV_OPA_TRANSP, LV_PART_MAIN);

            lv_obj_set_style_shadow_width(obj, 0, LV_PART_MAIN);
            lv_obj_set_style_shadow_opa(obj, LV_OPA_TRANSP, LV_PART_MAIN);
            lv_obj_set_style_bg_color(obj, lv_color_white(), LV_PART_MAIN);
            lv_obj_set_style_bg_opa(obj, 0, LV_PART_MAIN);
        } else {
            // 使用最后一个状态
            if (styles != NULL && styles->size > 0) {
                urq_set_state_style(
                    project, obj, styles, (urq_state_t)(styles->size - 1));
            }
        }
    }
    return true;
}

bool urq_set_state_text(
    lv_obj_t *udata, lv_obj_t *obj, urq_text_ptr_list_t *texts,
    urq_state_t state)
{
    if (texts != NULL && texts->size > state) {
        urq_text_t *text = texts->data[state];
        urq_show_lvgl_text(udata, obj, text);
        return true;
    }
    return false;
}

bool urq_set_state_text_with_status(
    lv_obj_t *udata, lv_obj_t *obj, urq_text_ptr_list_t *texts,
    urq_state_t state, urq_no_state_way_t no_state_way)
{
    int rt = urq_set_state_text(udata, obj, texts, state);
    if (rt) {
        if (lv_obj_has_flag(obj, LV_OBJ_FLAG_HIDDEN) &&
            no_state_way == URQ_NO_STATE_WAY_USE_BLANK) {
            lv_obj_clear_flag(obj, LV_OBJ_FLAG_HIDDEN);
        }

    } else {
        if (no_state_way == URQ_NO_STATE_WAY_USE_BLANK) {
            if (!lv_obj_has_flag(obj, LV_OBJ_FLAG_HIDDEN))
                lv_obj_add_flag(obj, LV_OBJ_FLAG_HIDDEN);
        } else {
            // 使用最后一个状态
            if (texts != NULL && texts->size > 0) {
                urq_set_state_text(
                    udata, obj, texts, (urq_state_t)(texts->size - 1));
            }
        }
    }
    return true;
}

void urq_set_state(
    lv_obj_t *obj, urq_widget_common_conf_t *common_conf, urq_state_t state,
    uint8_t platfrom)
{
    urq_user_data_t *udata = urq_obj_get_user_data(obj);
    urq_used(platfrom);
    printf("[urq_set_state] id : %d\n", udata->id);

    urq_no_state_way_t state_way = URQ_NO_STATE_WAY_UNSPECIFIED;
    if (common_conf->stat_property != NULL) {
        state_way = common_conf->stat_property->no_state_way;
    }

    if (udata->img != NULL) {
        urq_set_state_graphic_with_status(
            (lv_obj_t *)udata, udata->img, common_conf->graphics, state,
            state_way);
    }

    if (udata->label != NULL) {
        urq_set_state_text_with_status(
            (lv_obj_t *)udata, udata->label, common_conf->texts, state,
            state_way);
    }

    urq_set_state_style_with_status(
        udata->project, obj, common_conf->styles, state, state_way);
}

urq_style_t *urq_get_state_style(
    urq_project_t *project, urq_widget_common_conf_t *com_conf)
{
    int8_t state = project->env.var_sys.state;
    if (com_conf->styles->size > state || com_conf->styles->data[state] == NULL)
        return NULL;
    return com_conf->styles->data[state];
}

urq_style_t *urq_get_state_style_without_empty(
    urq_project_t *project, urq_widget_common_conf_t *com_conf)
{
    urq_style_t *style = urq_get_state_style(project, com_conf);
    if (style == NULL) {
        if (com_conf->styles->size >= 1)
            return com_conf->styles->data[com_conf->styles->size - 1];
    }
    return style;
}

// void urq_set_no_state_style_with_fail(
//     lv_obj_t *obj, urq_widget_common_conf_t *common_conf,
//     urq_no_state_way_t way, int type)
// {
//     if (way == URQ_NO_STATE_WAY_USE_BLANK) {
//         // 空白
//         if (type == 2) {
//             lv_obj_set_style_border_width(obj, 0, LV_PART_MAIN);
//             lv_obj_set_style_border_opa(obj, LV_OPA_TRANSP, LV_PART_MAIN);

//             lv_obj_set_style_shadow_width(obj, 0, LV_PART_MAIN);
//             lv_obj_set_style_shadow_opa(obj, LV_OPA_TRANSP, LV_PART_MAIN);
//             lv_obj_set_style_bg_color(obj, lv_color_white(), LV_PART_MAIN);
//             lv_obj_set_style_bg_opa(obj, 0, LV_PART_MAIN);
//         } else {
//             if (!lv_obj_has_flag(obj, LV_OBJ_FLAG_HIDDEN))
//                 lv_obj_add_flag(obj, LV_OBJ_FLAG_HIDDEN);
//         }
//     } else {
//         // 使用最后一个状态
//         if (type == 0) {
//             urq_set_state_graphic(
//                 obj, obj, common_conf->graphics,
//                 (urq_state_t)(common_conf->graphics->size - 1));
//         } else if (type == 1) {
//             urq_set_state_text(
//                 obj, obj, common_conf->texts,
//                 (urq_state_t)(common_conf->texts->size - 1));
//         } else if (type == 2) {
//             urq_user_data_t *udata = urq_obj_get_user_data(obj);
//             urq_set_state_style(
//                 udata->project, obj, common_conf->styles,
//                 (urq_state_t)(common_conf->styles->size - 1));
//         }
//     }
// }

// void urq_set_no_state_style_with_success(lv_obj_t *obj, urq_no_state_way_t
// way)
// {
//     if (way == URQ_NO_STATE_WAY_USE_BLANK) {
//         // 空白
//         if (lv_obj_has_flag(obj, LV_OBJ_FLAG_HIDDEN)) {
//             lv_obj_clear_flag(obj, LV_OBJ_FLAG_HIDDEN);
//         }
//     }
// }
