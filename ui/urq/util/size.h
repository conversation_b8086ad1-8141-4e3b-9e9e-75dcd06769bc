#pragma once

#include "urq/page.h"
#include "urq_conf/common/size.h"

#ifndef URQ__UTIL__SIZE_H
#define URQ__UTIL__SIZE_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 设置大小
/// @param obj 对象
/// @param page 页面
/// @param id 组件 ID
/// @param size 大小
void urq_set_size(
    lv_obj_t *obj, urq_page_t *page, uint32_t id, urq_size_t *size)
    __attribute__((__nonnull__(1, 2, 4)));

#ifdef __cplusplus
}
#endif

#endif // URQ__UTIL__SIZE_H
