#pragma once

#include "lvgl.h"
#include "urq_conf/common/color.h"
#include "urq_conf/datastruct/map/theme_color.h"

#ifndef URQ__UTIL__COLOR_H
#define URQ__UTIL__COLOR_H
#ifdef __cplusplus
extern "C" {
#endif

lv_color_t urq_get_color(urq_theme_color_t *theme_color, urq_color_ref_t *color)
    __attribute__((__nonnull__(1, 2)));

lv_opa_t urq_get_opacity(urq_theme_color_t *theme_color, urq_color_ref_t *color)
    __attribute__((__nonnull__(1, 2)));

#ifdef __cplusplus
}
#endif

#endif // URQ__UTIL__COLOR_H
