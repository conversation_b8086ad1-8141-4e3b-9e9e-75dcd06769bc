#pragma once

#include "lvgl.h"
#include <stdint.h>

#ifndef URQ__UTIL__SHAPE_H
#define URQ__UTIL__SHAPE_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 计算弧点
/// @param points 点
/// @param num_points 点数
/// @param center 中心点
/// @param lr 长轴半径
/// @param sr 短轴半径
/// @param start_angle 起始角度
/// @param end_angle 结束角度
/// @param rotate_angle 旋转角度
void urq_calculate_half_arc_points(
    lv_point_t *points, int num_points, lv_point_t center, lv_coord_t lr,
    lv_coord_t sr, float start_angle, float end_angle, float rotate_angle)
    __attribute__((__nonnull__(1)));

/// @brief 扇形点
/// @param points 点
/// @param num_points 点数
/// @param center 中心点
/// @param lr 外弧长轴半径
/// @param sr 外弧短轴半径
/// @param start_angle 起始角度
/// @param end_angle 结束角度
/// @param rotate_angle 旋转角度
void urq_calculate_fan_points(
    lv_point_t *points, int num_points, lv_point_t center, lv_coord_t lr,
    lv_coord_t sr, float start_angle, float end_angle, float rotate_angle)
    __attribute__((__nonnull__(1)));

/// @brief 计算环形点
/// @param points 点
/// @param num_points 点数
/// @param center 中心点
/// @param lr_outer 外弧长轴半径
/// @param sr_outer 外弧短轴半径
/// @param lr_inner 内弧长轴半径
/// @param sr_inner 内弧短轴半径
/// @param start_angle 起始角度
/// @param end_angle 结束角度
/// @param rotate_angle 旋转角度
void urq_calculate_fan_ring_points(
    lv_point_t *points, int num_points, lv_point_t center, lv_coord_t lr_outer,
    lv_coord_t sr_outer, lv_coord_t lr_inner, lv_coord_t sr_inner,
    float start_angle, float end_angle, float rotate_angle)
    __attribute__((__nonnull__(1)));

#ifdef __cplusplus
}
#endif

#endif // URQ__UTIL__SHAPE_H
