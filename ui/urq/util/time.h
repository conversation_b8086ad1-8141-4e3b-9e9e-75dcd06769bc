#pragma once

#include "urq_conf/common/time.h"
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <time.h>

#ifndef URQ__UTIL__TIME_H
#define URQ__UTIL__TIME_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 将时间戳转换为指定格式的日期字符串
 * @param timestamp Unix时间戳（秒）
 * @param format 日期格式类型
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @return 成功返回0，失败返回非0值
 */
int urq_timestamp_to_time_string(
    int64_t timestamp, urq_time_format_t format, char *buffer,
    size_t buffer_size);

/**
 * @brief 将时间戳转换为指定格式的日期字符串
 * @param timestamp Unix时间戳（秒）
 * @param format 日期格式类型
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @return 成功返回0，失败返回非0值
 */
int urq_timestamp_to_date_string(
    int64_t timestamp, urq_date_format_t format, char *buffer,
    size_t buffer_size);

/**
 * @brief 将时间戳转换为指定格式的日期时间字符串
 * @param timestamp Unix时间戳（秒）
 * @param date_format 日期格式类型
 * @param time_format 时间格式类型
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 **/
int urq_timestamp_to_datetime_string(
    int64_t timestamp, urq_date_format_t date_format,
    urq_time_format_t time_format, char *buffer, size_t buffer_size);

/**
 * @brief 获取当前时间
 * @return 当前时间
 */
struct tm *urq_get_current_time(void);

#ifdef __cplusplus
}
#endif

#endif // #ifndef __URQ_TIME_H__
