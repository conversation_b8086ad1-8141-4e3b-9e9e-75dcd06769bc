#include "urq/util/color.h"
#include "urq_conf/datastruct/map/theme_color.h"

lv_color_t urq_get_color(urq_theme_color_t *theme_color, urq_color_ref_t *color)
{
    //     if(color->id != 0 && color->id == 0x000000FF) {
    if (color->id >= 0) {
        color->rgba = *urq_theme_color_get(theme_color, color->id);
    }
    return lv_color_make(color->rgba.ch.r, color->rgba.ch.g, color->rgba.ch.b);
}

lv_opa_t urq_get_opacity(urq_theme_color_t *theme_color, urq_color_ref_t *color)
{
    //    if(color->id != 0 && color->id == 0x000000FF) {

    if (color->id >= 0) {
        color->rgba = *urq_theme_color_get(theme_color, color->id);
    }
    return color->rgba.ch.a;
}
