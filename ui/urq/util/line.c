
#include "urq/util/line.h"
#include <math.h>

bool urq_line_param_calc(
    lv_point_t *p1, lv_point_t *p2, urq_linear_param_t *param)
{
    // 计算直线方向向量和长度
    param->x_len = p2->x - p1->x;
    param->y_len = p2->y - p1->y;
    param->len =
        sqrtf(param->x_len * param->x_len + param->y_len * param->y_len);
    if (param->len < 1e-6) {
        return false; // 避免除零
    }

    // 计算法向量（调整为“左上方”）
    param->x_vector =
        param->y_len / param->len; // 法向量 x 分量 (dy, -dx) 取反后为左上方
    param->y_vector = -param->x_len / param->len; // 法向量 y 分量
    if (param->x_vector > 0 ||
        param->y_vector > 0) { // 确保左上方 (X 减小, Y 减小)
        param->x_vector = -param->x_vector;
        param->y_vector = -param->y_vector;
    }
    return true;
}
