#include "urq/clone/widget.h"
#include "lvgl.h"
#include "urq/preload.h"
#include "urq/user_data.h"
#include "urq/util/event.h"
#include "urq/util/state.h"
#include "urq/widget.h"
#include "urq_conf/req/action.h"
#include "urq_conf/widget/clone.h"
#include "urq_conf/widget/type.h"
#include <string.h>

#define Self urq_clone_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_CLONE_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_clone_widget_config, NULL, urq_clone_widget_set_state,
        NULL);

    widget->clone = NULL;
    widget->conf = NULL;
    widget->common_conf = NULL;
    widget->is_clone = false;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    urq_clone_widget_t *widget = (urq_clone_widget_t *)obj;
    urq_clone_widget_conf_t *conf = widget->conf;

    urq_user_data_free(&widget->user_data);

    // 释放克隆组件自己的配置
    if (conf != NULL) {
        urq_clone_widget_conf_free_inplace(conf);
        urq_free(conf);
        widget->conf = NULL;
    }

    if (widget->common_conf != NULL && !widget->is_clone) {
        urq_widget_common_conf_free_inplace(widget->common_conf);
        urq_free(widget->common_conf);
        widget->common_conf = NULL;
    }

    widget->is_clone = false;
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (urq_event_filter_exit(class_p, e)) {
        return;
    }
    Self *widget = (Self *)lv_event_get_target(e);
    urq_widget_common_conf_t *common_conf = widget->common_conf;
    urq_page_t *const page = widget->user_data.page;
    static uint32_t count = 0;
    // 检查是否执行点击事件
    if (urq_event_check_exec_click(e, &count, common_conf)) {
        // exec
        // printf("可执行\n");
    }
    if (common_conf != NULL && common_conf->has_action) {
        if (urq_page_add_frame_cb3(
                page, urq_req_wirte_set_action, urq_noop3, (void *)page,
                (void *)(intptr_t)common_conf->id, (void *)(intptr_t)e->code)) {
            return;
        }
    }
}

lv_obj_t *urq_clone_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj;

    obj = lv_obj_class_create_obj(&URQ_CLONE_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

void urq_clone_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    urq_clone_widget_t *const widget = (urq_clone_widget_t *)self;
    urq_project_t *const project = widget->user_data.project;
    urq_page_t *const page = widget->user_data.page;
    if (!widget->is_clone) {
        widget->is_clone = true;
        widget->conf = mut_conf->config.clone;
        widget->common_conf = mut_conf->common_conf;
        urq_clone_widget_conf_t *conf = widget->conf;
        lv_obj_add_flag(self, LV_OBJ_FLAG_CLICKABLE);

        mut_conf->type = URQ_WIDGET_TYPE_NONE;
        mut_conf->config.pointer = NULL;
        mut_conf->common_conf = NULL;

        lv_obj_set_pos(self, mut_conf->pos.x, mut_conf->pos.y);
        lv_obj_set_size(self, mut_conf->size.w, mut_conf->size.h);
        conf->size = mut_conf->size;
        conf->pos = mut_conf->pos;

        if (project->display->clone_map == NULL) {
            project->display->clone_map = urq_lvgl_pair_map_new();
        }

        if (urq_lvgl_pair_map_add(
                project->display->clone_map, conf->page_id_u16,
                conf->widget_id_u16, self)) {
            return;
        }
    } else {
        urq_widget_common_conf_t *const common_conf = widget->common_conf;
        urq_clone_widget_conf_t *conf = widget->conf;

        urq_lvgl_pair_map_del(
            project->display->clone_map, conf->page_id_u16,
            conf->widget_id_u16);
        // 重置位置
        // 获取当前画面大小
        // lv_coord_t w = (lv_coord_t)(lv_obj_get_width((lv_obj_t*)page) /
        // conf->size.w); lv_coord_t h =
        // (lv_coord_t)(lv_obj_get_height((lv_obj_t*)page) / conf->size.h);
        // // 通过获取页面的wh来设置

        if (mut_conf->common_conf != NULL && common_conf != NULL) {
            if (mut_conf->common_conf->styles != NULL)
                mut_conf->common_conf->styles = common_conf->styles;
            if (mut_conf->common_conf->graphics != NULL)
                mut_conf->common_conf->graphics = common_conf->graphics;
        }

        // 安全删除旧的克隆对象
        if (widget->clone != NULL && lv_obj_is_valid(widget->clone)) {
            lv_obj_del(widget->clone);
            widget->clone = NULL;
        }

        widget->clone = urq_widget_create(self, mut_conf->type);

        if (widget->clone != NULL) {
            urq_user_data_t *user_data = urq_obj_get_user_data(widget->clone);
            if (user_data != NULL) {
                user_data->project = page->project;
                user_data->page = page;
                user_data->type = URQ_WIDGET_TYPE_CLONE;
                user_data->conf(widget->clone, mut_conf);
                // TODO
                urq_clone_widget_set_state(
                    self, page->project->env.var_sys.state, 1);
            }
        }
    }
}

void urq_clone_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *const widget = (Self *)self;
    urq_user_data_t *clone_widget = urq_obj_get_user_data(widget->clone);
    if (clone_widget != NULL && clone_widget->set_state != NULL) {
        clone_widget->set_state(widget->clone, state, platfrom);
    }
}

void urq_clone_widget_refresh_language(lv_obj_t *self)
{
    Self *const widget = (Self *)self;
    urq_user_data_t *clone_widget = urq_obj_get_user_data(widget->clone);
    if (clone_widget != NULL && clone_widget->refresh_language != NULL) {
        clone_widget->refresh_language(widget->clone);
    }
}
