#include "urq/bit/widget.h"
#include "lvgl.h"
#include "urq/preload.h"
#include "urq/user_data.h"
#include "urq/util/control.h"
#include "urq/util/event.h"
#include "urq/util/state.h"
#include "urq/widget.h"
#include "urq_conf/widget/bit.h"
#include "urq_conf/widget/type.h"
#include <string.h>

#define Self urq_bit_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_BIT_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    LV_ASSERT_OBJ(obj, URQ_BIT_WIDGET_CLASS);

    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_bit_widget_config, urq_bit_widget_refresh_language,
        urq_bit_widget_set_state, NULL);

    widget->common_conf = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    urq_bit_widget_t *widget = (urq_bit_widget_t *)obj;
    if (widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);

        if (widget->common_conf != NULL) {
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
            widget->common_conf = NULL;
        }
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (urq_event_filter_exit(class_p, e)) {
        return;
    }

    Self *widget = (Self *)lv_event_get_target(e);
    urq_widget_common_conf_t *common_conf = widget->common_conf;
    urq_page_t *const page = widget->user_data.page;

    static uint32_t count = 0;
    // 检查是否执行点击事件
    if (urq_event_check_exec_click(e, &count, common_conf)) {
        // exec
        // printf("可执行\n");
    }

    if (common_conf != NULL && common_conf->has_action) {
        urq_event_send_action(e, page, common_conf->id);
    }
}

lv_obj_t *urq_bit_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj;

    obj = lv_obj_class_create_obj(&URQ_BIT_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

void urq_bit_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;
    widget->common_conf = mut_conf->common_conf;

    urq_widget_config(self, mut_conf, NULL);
}

void urq_bit_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platfrom);
}

void urq_bit_widget_refresh_language(lv_obj_t *self)
{
    Self *const widget = (Self *)self;
    urq_user_data_t *udata = urq_obj_get_user_data((lv_obj_t *)widget);

    urq_state_t state = widget->common_conf->stat_property->cur_state;
    urq_set_state_text(
        (lv_obj_t *)udata, self, widget->common_conf->texts, state);
}
