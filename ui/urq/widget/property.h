#pragma once

#include "lvgl.h"
#include "urq_conf/widget/common.h"
#include "urq_conf/widget/property_type.h"

#ifndef URQ__WIDGET__PROPERTY_H
#define URQ__WIDGET__PROPERTY_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 接收服务端数据处理属性
int urq_deal_property(
    lv_obj_t *obj, urq_widget_common_conf_t *conf, urq_property_id_t type,
    uint8_t index, uint64_t value) __attribute__((__nonnull__(1)));

#ifdef __cplusplus
}
#endif
#endif // URQ__WIDGET__PROPERTY_H
