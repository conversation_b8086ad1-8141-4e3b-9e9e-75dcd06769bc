#include "urq/word/widget.h"
#include "lvgl.h"
#include "urq/preload.h"
#include "urq/user_data.h"
#include "urq/util/control.h"
#include "urq/util/event.h"
#include "urq/util/state.h"
#include "urq/widget.h"
#include "urq/widget/property.h"
#include "urq_conf/widget/type.h"
#include "urq_conf/widget/word.h"

#define Self urq_word_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_WORD_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    // user data
    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_word_widget_config, NULL, urq_word_widget_set_state,
        urq_word_widget_set_property);
    widget->conf = NULL;
    widget->common_conf = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *widget = (Self *)obj;
    if (widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);
        if (widget->conf != NULL) {
            urq_word_widget_conf_free_inplace(widget->conf);
            urq_free(widget->conf);
            widget->conf = NULL;
        }
        if (widget->common_conf != NULL) {
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
            widget->common_conf = NULL;
        }
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (urq_event_filter_exit(class_p, e)) {
        return;
    }
    Self *widget = (Self *)lv_event_get_target(e);
    urq_widget_common_conf_t *common_conf = widget->common_conf;
    urq_page_t *const page = widget->user_data.page;
    static uint32_t count = 0;
    if (urq_event_check_exec_click(e, &count, common_conf)) {
        // exec
        // printf("可执行\n");
    }
    if (common_conf != NULL && common_conf->has_action) {
        urq_event_send_action(e, page, common_conf->id);
    }
}

lv_obj_t *urq_word_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj;

    obj = lv_obj_class_create_obj(&URQ_WORD_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

void urq_word_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;

    widget->conf = mut_conf->config.word;
    widget->common_conf = mut_conf->common_conf;

    urq_widget_config(self, mut_conf, NULL);
}

void urq_word_widget_set_property_callback(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    urq_used(self);
    urq_used(index);
    urq_used(value);
    switch (property_id) {
    // case URQ_PROPERTY_ID_TEXT:
    //     lv_label_set_text(widget->label, (const char *)value);
    //     break;
    default:
        break;
    }
}

void urq_word_widget_set_property(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    Self *const widget = (Self *)self;
    urq_widget_set_property(
        self, widget->common_conf, property_id, index, value,
        urq_word_widget_set_property_callback);
}

void urq_word_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *const widget = (Self *)self;
    urq_widget_common_conf_t *common_conf = widget->common_conf;

    urq_set_state(self, common_conf, state, platfrom);
}

void urq_word_widget_refresh_language(lv_obj_t *self)
{
    Self *const widget = (Self *)self;
    urq_user_data_t *udata = urq_obj_get_user_data((lv_obj_t *)widget);

    urq_state_t state = widget->common_conf->stat_property->cur_state;
    urq_set_state_text((lv_obj_t *)(udata), self, widget->conf->texts, state);
}
