#include "lvgl.h"
#include "urq/time/time.h"
#include "urq_conf/time/cb.h"
// #include "urq/log/verbose.h"
#include "urq/fs/fs.h" // IWYU pragma: keep
#include "urq/log/debug.h"
#include "urq/project/project.h"
#include "urq_conf/time/update.h"
#include <limits.h>

/// @brief 根节点
lv_obj_t *URQ_ROOT_PROJECT;

/// @brief 主循环时间增加
void urq_main_time_inc()
{
    static const int URQ_FPS = 60;                                // 帧率
    static const urq_time_t URQ_FRAME_MS = 1000 / URQ_FPS;        // 帧时间
    static const urq_time_t URQ_HALF_FRAME_MS = URQ_FRAME_MS / 2; // 半帧时间
    static urq_time_t begin_time = 0;
    static urq_time_t pass_time = 0;
    static bool half_frame_ev = true; // 是否需要执行半帧事件

    log_v("main time inc, project: %p\n", URQ_ROOT_PROJECT);

    urq_time_update();
    pass_time = urq_time_now() - begin_time;
    if (pass_time <= 0) { // 时间异常
        pass_time = 1;
    }

    lv_tick_inc((uint32_t)pass_time);
    if (half_frame_ev && pass_time >= URQ_HALF_FRAME_MS) {
        half_frame_ev = false;
        urq_project_frame_half_cb(URQ_ROOT_PROJECT); // 半帧事件
    }

    if (pass_time >= URQ_FRAME_MS) {
        urq_project_frame_cb(URQ_ROOT_PROJECT); // 一帧时再次执行
        urq_time_cb_poll();
        lv_timer_handler();
        urq_time_update();
        begin_time = urq_time_now();
    }
}
