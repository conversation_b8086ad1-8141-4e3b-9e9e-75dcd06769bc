#include "lvgl.h"
#include "urq/ip.h"
#include "urq/log/verbose.h"
#include "urq/lv/init.h"
#include "urq/main/common.h"
#include "urq/preload.h"
#include "urq/project/project.h"
#include "urq/sleep.h"
#include "urq_conf/system_variable.h"
#include <emscripten.h>
#include <limits.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>

/// @brief 初始化
/// @param id     iid
/// @param width  宽度
/// @param height 高度
/// @param project_width 工程宽度
/// @param project_height 工程高度
/// @returns project
static int _ui_main(int iid, lv_coord_t width, lv_coord_t height);

static int _ui_main(int iid, lv_coord_t width, lv_coord_t height)
{
    lv_indev_t *keyboard;
    URQ_PROJECT_WIDTH = width;
    URQ_PROJECT_HEIGHT = height;
    urq_project_t *project;

    if (urq_lv_init(iid, width, height, &keyboard)) {
        printf("241231235010098, urq_lv_init failed");
    }

    URQ_ROOT_PROJECT = urq_project_create(lv_scr_act());
    project = (urq_project_t *)URQ_ROOT_PROJECT;

    lv_obj_set_size(
        URQ_ROOT_PROJECT, (lv_coord_t)URQ_PROJECT_WIDTH,
        (lv_coord_t)URQ_PROJECT_HEIGHT);
    lv_obj_set_pos(URQ_ROOT_PROJECT, 0, 0);

    // if (project->env.style != NULL) {
    //     urq_color_rgba_t color;
    //     color.rgba = project->env.style->background_color.color_or_var;
    //     if (project->env.style->background_color.is_color_var) {
    //         lv_obj_set_style_bg_color(
    //             URQ_ROOT_PROJECT,
    //             lv_color_make(color.ch.r, color.ch.g, color.ch.b),
    //             LV_PART_MAIN);
    //         lv_obj_set_style_bg_opa(URQ_ROOT_PROJECT, color.ch.a,
    //         LV_PART_MAIN);
    //     }
    // }

    lv_obj_set_size(
        project->display->top_layer, (lv_coord_t)URQ_PROJECT_WIDTH,
        (lv_coord_t)URQ_PROJECT_HEIGHT);
    lv_obj_set_pos(project->display->top_layer, 0, 0);

    lv_obj_set_size(
        project->display->page_layer, (lv_coord_t)URQ_PROJECT_WIDTH,
        (lv_coord_t)URQ_PROJECT_HEIGHT);
    lv_obj_set_pos(project->display->page_layer, 0, 0);

    if (urq_project_set_host(
            URQ_ROOT_PROJECT, iid, urq_ip_make4(127, 0, 0, 1), 8849)) {
        return -1;
    }
    return 0;
}

EMSCRIPTEN_KEEPALIVE
void urq_web_set_errno(int32_t no) { errno = no; }

EMSCRIPTEN_KEEPALIVE
int urq_web_get_errno(void) { return errno; }

EMSCRIPTEN_KEEPALIVE
void *urq_web_malloc(uint32_t size) { return urq_malloc(size); }

EMSCRIPTEN_KEEPALIVE
void urq_web_free(void *ptr) { free(ptr); }

int main(int argc, char *argv[])
{
    while (true) {
        printf("%s:%d\n", __FILE__, __LINE__);
        urq_sleep(1000);
    }
    return 0;
}

EMSCRIPTEN_KEEPALIVE
void urq_web_main(
    const char *working_folder, int iid, int width, int height,
    int project_width, int project_height)
{
    log_i(
        "urq_web_main, working folder: %s, iid: %d, width: %d, height: %d\n",
        working_folder, iid, width, height);
    // urq_fs_mod_init("");
    _ui_main(iid, width, height);
}

EMSCRIPTEN_KEEPALIVE
void urq_web_time_inc() { urq_main_time_inc(); }

EMSCRIPTEN_KEEPALIVE
void urq_web_var_sys_set_u16(urq_device_local_addr_t addr, uint16_t value)
{
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;
    // if (urq_conn_sys_u16_write_single(
    //         &project->conn, URQ_DEVICE_LOCAL_ADDR_PAGE, value)) {
    //     log_e("page set error\n");
    // }
}