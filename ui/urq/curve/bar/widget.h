#pragma once

#include "lvgl.h"
#include "urq/curve/widget.hpp"
#include "urq/user_data.h"
#include "urq_conf/data/type.h"
#include "urq_conf/widget/curve_xy.h"

#ifndef URQ__CURVE__BAR__WIDGET_H
#define URQ__CURVE__BAR__WIDGET_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 父组件
    lv_obj_t supper;
    /// @brief user data 字段
    urq_user_data_t user_data;
    /// @brief 曲线
    lv_obj_t *bar_curve;
} urq_bar_curve_widget_t;

/// @brief 创建 curve_widget
/// @param parent 当前组件所属的页面
/// @returns 组件
lv_obj_t *urq_bar_curve_widget_create(lv_obj_t *parent)
    __attribute__((__nonnull__(1)));

/// @brief 配置 curve_widget
/// @param self     组件自身
/// @param mut_conf 配置的内容
/// @returns 组件
void urq_bar_curve_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
    __attribute__((__nonnull__(1, 2)));

/// @brief 清空组件
/// @param self 组件自身
/// @returns void
void urq_bar_curve_widget_clear(lv_obj_t *self) __attribute__((__nonnull__(1)));

/// @brief 设置曲线数据
/// @param self 组件自身
/// @param data 数据
/// @returns void
void urq_bar_curve_widget_set_data(lv_obj_t *self, lv_coord_t data)
    __attribute__((__nonnull__(1)));

/// @brief 设置曲线数据
/// @param self 组件自身
/// @param y_data 数据
/// @param x_data 数据
/// @returns void
void urq_bar_curve_widget_set_data_with_x(
    lv_obj_t *self, lv_coord_t y_data, lv_coord_t x_data)
    __attribute__((__nonnull__(1)));

#ifdef __cplusplus
}
#endif
#endif // URQ__CURVE__BAR__WIDGET_H
