#pragma once

#include "lvgl.h"
#include "urq/user_data.h"
#include "urq_conf/datastruct/point_circular.h"
#include "urq_conf/widget/common.h"
#include "urq_conf/widget/curve.h"

#ifdef __cplusplus
extern "C" {
#endif

#define URQ_CURVE_WIDGET_MAX_HISTORY_SIZE 1024

typedef struct {
    /// @brief 父组件
    lv_obj_t supper;
    /// @brief user data 字段
    urq_user_data_t user_data;
    /// @brief 配置
    urq_curve_widget_conf_t *conf;
    /// @brief 通用配置
    urq_widget_common_conf_t *common_conf;
    /// @brief 历史数据队列
    urq_point_cqueue_t **history_datas;
    /// @brief 序列
    lv_chart_series_t **series;
    /// @brief 浮标
    lv_chart_cursor_t *cursor;
    /// @brief 曲线
    lv_obj_t *chart;
    /// @brief 下拉列表
    lv_obj_t *dropdown;
    /// @brief 大小
    urq_size_t size;
    /// @brief 起始x索引
    uint16_t startx_idx;
    /// @brief 上一次起始x索引
    uint16_t last_startx_idx;
    /// @brief 点数
    uint16_t zoom_point_num;
    /// @brief 上一次历史数据总数
    uint16_t last_history_count;
    /// @brief 上一次游标点
    uint16_t last_cursor_point;
    /// @brief 上一次拖拽点
    lv_point_t last_drag_point;
    /// @brief 网格水平偏移
    lv_coord_t grid_offset_x;
    /// @brief 网格垂直偏移
    lv_coord_t grid_offset_y;
    /// @brief 是否启用
    bool enable;
    /// @brief 是否启用追踪
    bool enable_trace;
    /// @brief 追踪定时器
    bool has_trace_timer;
} urq_curve_widget_t;

/// @brief 创建 curve_widget
/// @param parent 当前组件所属的页面
/// @returns 组件
lv_obj_t *urq_curve_widget_create(lv_obj_t *parent)
    __attribute__((__nonnull__(1)));

/// @brief 配置 curve_widget
/// @param self     组件自身
/// @param mut_conf 配置的内容
/// @returns 组件
void urq_curve_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
    __attribute__((__nonnull__(1, 2)));

/// @brief 设置组件状态
/// @param self 组件自身
/// @param state 状态
/// @returns void
void urq_curve_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
    __attribute__((__nonnull__(1)));

/// @brief 清空组件
/// @param self 组件自身
/// @returns void
void urq_curve_widget_data_clear(lv_obj_t *self)
    __attribute__((__nonnull__(1)));

/*=======================================================曲线控制====================================================*/
/// @brief 更新曲线
/// @param self 控件对象
/// @returns void
void urq_curve_widget_update_chart(lv_obj_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 全量更新曲线
/// @param self 控件对象
/// @returns void
void urq_curve_widget_update_all_chart(lv_obj_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 增量更新曲线
/// @param self 控件对象
/// @returns void
void urq_curve_widget_update_incremental_chart(lv_obj_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 直接更新图表数据，避免完整重绘
/// @param self 组件自身
/// @returns void
void urq_curve_widget_direct_update(lv_obj_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 运行控制
/// @param self 控件对象
/// @returns void
void urq_curve_widget_control_run(lv_obj_t *self, bool run)
    __attribute__((__nonnull__(1)));

/*=======================================================缩放====================================================*/
/// @brief 缩放x轴
/// @param self 控件对象
/// @returns void
void urq_curve_widget_zoom_x(lv_obj_t *self, float zoom)
    __attribute__((__nonnull__(1)));

/// @brief 缩放y轴
/// @param self 控件对象
/// @param zoom 缩放比例
/// @returns void
void urq_curve_widget_zoom_y(lv_obj_t *self, float zoom)
    __attribute__((__nonnull__(1)));

/*=======================================================设置浮标====================================================*/
/// @brief 显示浮标
/// @param self 控件对象
/// @param show 是否显示
/// @returns void
void urq_curve_widget_set_cursor_visible(lv_obj_t *self, bool show)
    __attribute__((__nonnull__(1)));

/*=======================================================设置xy轴====================================================*/
/// @brief 设置x轴范围
/// @param self 控件对象
/// @param min 最小值
/// @param max 最大值
/// @returns void
void urq_curve_widget_set_x_range(
    lv_obj_t *self, lv_coord_t min, lv_coord_t max)
    __attribute__((__nonnull__(1)));

/// @brief 设置y轴范围
/// @param self 控件对象
/// @param min 最小值
/// @param max 最大值
/// @returns void
void urq_curve_widget_set_y_range(
    lv_obj_t *self, lv_coord_t min, lv_coord_t max)
    __attribute__((__nonnull__(1)));

/// @brief 设置网格偏移
/// @param self 控件对象
/// @param offset_x 水平偏移
/// @param offset_y 垂直偏移
/// @returns void
void urq_curve_widget_set_grid_offset(
    lv_obj_t *self, lv_coord_t offset_x, lv_coord_t offset_y)
    __attribute__((__nonnull__(1)));

/// @brief 获取下一个x轴标签
/// @param self 控件对象
/// @returns 下一个x轴标签
int64_t urq_curve_widget_get_x_next_label(lv_obj_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 设置x轴标签
/// @param self 控件对象
/// @param dsc 绘制描述符
/// @returns void
void urq_curve_widget_set_x_label(lv_obj_t *self, lv_obj_draw_part_dsc_t *dsc)
    __attribute__((__nonnull__(1, 2)));

/// @brief 设置y轴标签
/// @param self 控件对象
/// @param dsc 绘制对象
/// @returns void
void urq_curve_widget_set_y_label(lv_obj_t *self, lv_obj_draw_part_dsc_t *dsc)
    __attribute__((__nonnull__(1, 2)));

/*=======================================================设置曲线====================================================*/
/// @brief 设置曲线宽度
/// @param self 控件对象
/// @param width 宽度
/// @returns void
void urq_curve_widget_set_series_width(
    lv_obj_t *self, uint8_t series_idx, uint8_t width)
    __attribute__((__nonnull__(1)));

/// @brief 设置曲线最大值、最小值
/// @param self 控件对象
/// @param min 最小值
/// @param max 最大值
/// @returns void
void urq_curve_widget_set_series_range(
    lv_obj_t *self, uint8_t series_idx, lv_coord_t min, lv_coord_t max)
    __attribute__((__nonnull__(1)));

/// @brief 设置曲线是否可见
/// @param self 控件对象
/// @param series_idx 系列索引
/// @param visible 是否可见
/// @returns void
void urq_curve_widget_set_series_visible(
    lv_obj_t *self, uint8_t series_idx, bool visible)
    __attribute__((__nonnull__(1)));

/// @brief 设置曲线颜色
/// @param self 控件对象
/// @param series_idx 系列索引
/// @param color 颜色
/// @returns void
void urq_curve_widget_set_series_color(
    lv_obj_t *self, uint8_t series_idx, lv_color_t color)
    __attribute__((__nonnull__(1)));

/*=======================================================曲线数据处理====================================================*/
/// @brief 设置曲线数据
/// @param self 组件自身
/// @param data 数据
/// @returns void
void urq_curve_widget_set_data(
    lv_obj_t *self, uint8_t series_idx, lv_coord_t y_data)
    __attribute__((__nonnull__(1)));

/// @brief 设置所有系列数据
/// @param self 组件自身
/// @param y_data 数据
/// @returns void
void urq_curve_widget_set_all_series_next_value(
    lv_obj_t *self, lv_coord_t y_data) __attribute__((__nonnull__(1)));

/// @brief 设置曲线数据
/// @param self 组件自身
/// @param y_data 数据
/// @param x_data 数据
/// @returns void
void urq_curve_widget_set_data_with_x(
    lv_obj_t *self, uint8_t series_idx, lv_coord_t y_data, lv_coord_t x_data)
    __attribute__((__nonnull__(1)));

/// @brief 解析系列数据
/// @param self 控件对象
/// @param data 数据
/// @returns void
void urq_curve_widget_parse_series_data(lv_obj_t *self, uint8_t *data)
    __attribute__((__nonnull__(1, 2)));

/// @brief 解析历史列数据
/// @param self 控件对象
/// @param data 数据
/// @returns void
void urq_curve_widget_parse_series_history_data(lv_obj_t *self, uint8_t *data)
    __attribute__((__nonnull__(1, 2)));

#ifdef __cplusplus
}
#endif
