#pragma once

#include "lvgl.h"
#include "urq/user_data.h"
#include "urq_conf/linear_param.h"
#include "urq_conf/widget/scale.h"

#ifndef URQ__SCALE__WIDGET_H
#define URQ__SCALE__WIDGET_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 刻度尺组件结构
typedef struct {
    /// @brief 父组件
    lv_obj_t supper;
    /// @brief user data 字段
    urq_user_data_t user_data;
    lv_obj_t *obj;
    /// @brief 连接线参数
    urq_linear_param_t param;
    /// @brief 配置
    urq_scale_widget_conf_t *conf;
    /// @brief 通用配置
    urq_widget_common_conf_t *common_conf;
} urq_scale_widget_t;

/// @brief 创建刻度尺组件
/// @param parent 父组件
/// @return 刻度尺组件对象
lv_obj_t *urq_scale_widget_create(lv_obj_t *parent)
    __attribute__((__nonnull__(1)));

/// @brief 配置刻度尺组件
/// @param self 组件对象
/// @param mut_conf 配置结构
void urq_scale_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
    __attribute__((__nonnull__(1, 2)));

/// @brief 设置刻度尺状态
/// @param self 组件对象
/// @param state 状态
/// @param platform 平台
void urq_scale_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platform)
    __attribute__((__nonnull__(1)));

/// @brief 刷新语言
/// @param self 组件对象
void urq_scale_widget_refresh_language(lv_obj_t *self)
    __attribute__((__nonnull__(1)));

#ifdef __cplusplus
}
#endif

#endif // URQ__RULER__WIDGET_H
