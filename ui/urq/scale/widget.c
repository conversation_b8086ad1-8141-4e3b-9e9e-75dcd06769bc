#include "urq/scale/widget.h"
#include "urq/log/debug.h"
#include "urq/util/color.h"
#include "urq/util/control.h"
#include "urq/util/font.h"
#include "urq/util/line.h"
#include "urq/util/state.h"
#include "urq/util/style.h"
#include "urq/widget.h"
#include "urq_conf/linear_param.h"
#include "urq_conf/style/font.h"
#include "urq_conf/style/line.h"
#include <math.h>
#include <stdint.h>
#include <stdio.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

#define Self urq_scale_widget_t

// 前向声明
static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);
static void urq_draw_line_scale(lv_event_t *e);

// 组件类定义
const lv_obj_class_t URQ_SCALE_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF / 4,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_scale_widget_config, NULL, urq_scale_widget_set_state,
        NULL);

    widget->conf = NULL;
    widget->common_conf = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;

    if (widget->conf != NULL) {
        urq_scale_widget_conf_free_inplace(widget->conf);
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    urq_used(class_p);
    urq_used(e);

    // lv_event_code_t code = lv_event_get_code(e);
}

lv_obj_t *urq_scale_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj = lv_obj_class_create_obj(&URQ_SCALE_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

static void urq_scale_widget_conf(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;
    urq_used(mut_conf); // 标记参数已使用
    urq_scale_widget_conf_t *conf = widget->conf;

    if (conf->type == URQ_SCALE_TYPE_LINE) {
        widget->obj = lv_line_create(self);
        lv_obj_set_size(widget->obj, mut_conf->size.w, mut_conf->size.h);
        lv_line_set_points(widget->obj, conf->point, conf->point_cnt);
        urq_set_style_line(
            widget->obj, widget->user_data.project, conf->line, LV_PART_MAIN);

        urq_line_param_calc(&conf->point[0], &conf->point[1], &widget->param);

        // 将widget作为用户数据传递给line的绘制回调
        lv_obj_add_event_cb(
            widget->obj, urq_draw_line_scale, LV_EVENT_DRAW_MAIN, widget);
    } else if (conf->type == URQ_SCALE_TYPE_ARC) {
        widget->obj = lv_meter_create(self);
        lv_obj_remove_style(widget->obj, NULL, LV_PART_INDICATOR);
        lv_obj_remove_style(widget->obj, NULL, LV_PART_KNOB);
        lv_obj_remove_style(widget->obj, NULL, LV_PART_MAIN);

        lv_obj_set_size(widget->obj, mut_conf->size.w, mut_conf->size.h);
        //
        lv_meter_scale_t *scale = lv_meter_add_scale(widget->obj);
        urq_arc_t *arc = conf->arc;
        urq_scale_t scale_conf = conf->scale;
        lv_meter_set_scale_ticks(
            widget->obj, scale, scale_conf.sub_tick_count,
            scale_conf.sub_tick_width, 10, lv_color_hex(0x000000));
        lv_meter_set_scale_major_ticks(
            widget->obj, scale, scale_conf.main_tick_count,
            scale_conf.main_tick_width, 20, lv_color_black(), 0);
        lv_meter_set_scale_range(
            widget->obj, scale, scale_conf.min_value, scale_conf.max_value,
            (uint32_t)(arc->end_angle - arc->start_angle),
            (uint32_t)conf->rotation_angle);
    }
}

void urq_scale_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;
    widget->conf = mut_conf->config.scale;
    widget->common_conf = mut_conf->common_conf;
    urq_widget_config(self, mut_conf, urq_scale_widget_conf);
}

void urq_scale_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platform)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platform);
}

void urq_scale_widget_add_mark(
    lv_obj_t *self, lv_coord_t value, const char *label, lv_color_t color)
{
    urq_used(self);
    urq_used(value);
    urq_used(label);
    urq_used(color);
    // TODO: 实现自定义标记功能
}

void urq_scale_widget_clear_marks(lv_obj_t *self)
{
    urq_used(self);
    // TODO: 实现清除标记功能
}

// 绘制刻度标签的通用函数
static void urq_draw_tick_label(
    lv_draw_ctx_t *draw_ctx, lv_coord_t value, lv_coord_t x, lv_coord_t y,
    lv_coord_t width, lv_coord_t height)
{
    char label_text[32];
    snprintf(label_text, sizeof(label_text), "%d", (int)value);

    lv_draw_label_dsc_t label_dsc;
    lv_draw_label_dsc_init(&label_dsc);

    lv_area_t label_area;
    label_area.x1 = x - width / 2;
    label_area.y1 = y - height / 2;
    label_area.x2 = x + width / 2;
    label_area.y2 = y + height / 2;

    lv_draw_label(draw_ctx, &label_dsc, &label_area, label_text, NULL);
}

static void urq_draw_line_scale(lv_event_t *e)
{
    Self *widget = (Self *)lv_event_get_user_data(e); // 这是主widget
    lv_draw_ctx_t *draw_ctx = lv_event_get_draw_ctx(e);

    // 安全检查
    if (widget == NULL || widget->conf == NULL) {
        return;
    }

    urq_scale_t *scale = &widget->conf->scale;
    urq_linear_param_t param = widget->param;

    lv_point_t p1 = widget->conf->point[0];

    lv_draw_line_dsc_t tick_dsc;
    lv_draw_line_dsc_init(&tick_dsc);

    tick_dsc.color = urq_get_color(
        widget->user_data.project->env.theme_color, &scale->main_color);
    tick_dsc.width = scale->main_tick_width > 0 ? scale->main_tick_width : 1;
    tick_dsc.opa = LV_OPA_COVER;

    lv_coord_t obj_x = lv_obj_get_x((lv_obj_t *)widget);
    lv_coord_t obj_y = lv_obj_get_y((lv_obj_t *)widget);

    int cnt = (int)(param.len / scale->main_tick_count);
    printf(
        "=====> cnt: %d, param.len: %f, scale->main_tick_count: %d\n", cnt,
        param.len, scale->main_tick_count);

    for (int i = 0; i <= cnt; i++) {
        float t = (float)i / (float)(cnt);

        lv_point_t start = {
            (lv_coord_t)(p1.x + t * param.x_len + obj_x),
            (lv_coord_t)(p1.y + t * param.y_len + obj_y)};

        lv_point_t end = {
            (lv_coord_t)(start.x + scale->main_tick_len * param.x_vector),
            (lv_coord_t)(start.y + scale->main_tick_len * param.y_vector)};

        lv_draw_line(draw_ctx, &tick_dsc, &start, &end);
    }
}
