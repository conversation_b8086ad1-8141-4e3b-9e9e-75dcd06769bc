#include "urq/scale/widget.h"
#include "urq/font.h"
#include "urq/linear/param.h"
#include "urq/log/debug.h"
#include "urq/scale/type.h"
#include "urq/style/line.h"
#include "urq/util/color.h"
#include "urq/util/control.h"
#include "urq/util/font.h"
#include "urq/util/line.h"
#include "urq/util/state.h"
#include "urq/util/style.h"
#include "urq/widget.h"
#include <math.h>
#include <stdio.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

typedef urq_scale_widget_t Self;

// 前向声明
static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);
static void urq_ruler_widget_draw_cb(lv_event_t *e);
static void urq_draw_line_scale(lv_event_t *e);

// 组件类定义
const lv_obj_class_t URQ_SCALE_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF / 4,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_scale_widget_config, NULL, urq_scale_widget_set_state,
        NULL);

    widget->conf = NULL;
    widget->common_conf = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;

    if (widget->conf != NULL) {
        urq_scale_widget_conf_free_inplace(widget->conf);
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    urq_used(class_p);
    urq_used(e);

    // lv_event_code_t code = lv_event_get_code(e);
}

lv_obj_t *urq_scale_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj = lv_obj_class_create_obj(&URQ_SCALE_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

static void urq_scale_widget_conf(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;
    urq_used(mut_conf); // 标记参数已使用

    // 确保配置已经设置
    printf(
        "=====> urq_scale_widget_conf: type=%d, point_cnt=%d\n",
        widget->conf->type, widget->conf->point_cnt);

    if (widget->conf->type == URQ_SCALE_TYPE_HORIZONTAL ||
        widget->conf->type == URQ_SCALE_TYPE_VERTICAL) {
        widget->line = lv_line_create(self);
        lv_line_set_points(
            widget->line, widget->conf->point, widget->conf->point_cnt);
        // urq_set_style_line(
        //     widget->line, widget->user_data.project, widget->conf->line,
        // LV_PART_MAIN);

        urq_line_param_calc(
            &widget->conf->point[0], &widget->conf->point[1], &widget->param);

        printf(
            "====> p1: %d, %d, p2: %d, %d\n", widget->conf->point[0].x,
            widget->conf->point[0].y, widget->conf->point[1].x,
            widget->conf->point[1].y);

        // 将widget作为用户数据传递给line的绘制回调
        lv_obj_add_event_cb(
            widget->line, urq_draw_line_scale, LV_EVENT_DRAW_MAIN, widget);
    }
}

void urq_scale_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;
    widget->conf = mut_conf->config.scale;
    widget->common_conf = mut_conf->common_conf;

    lv_obj_set_size(self, mut_conf->size.w, mut_conf->size.h);

    urq_widget_config(self, mut_conf, urq_scale_widget_conf);
}

void urq_scale_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platform)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platform);
}

void urq_scale_widget_set_range(
    lv_obj_t *self, lv_coord_t min_value, lv_coord_t max_value)
{
    Self *widget = (Self *)self;
    if (widget->conf != NULL) {
        widget->conf->scale.min_value = min_value;
        widget->conf->scale.max_value = max_value;
        lv_obj_invalidate(self);
    }
}

void urq_scale_widget_add_mark(
    lv_obj_t *self, lv_coord_t value, const char *label, lv_color_t color)
{
    urq_used(self);
    urq_used(value);
    urq_used(label);
    urq_used(color);
    // TODO: 实现自定义标记功能
}

void urq_scale_widget_clear_marks(lv_obj_t *self)
{
    urq_used(self);
    // TODO: 实现清除标记功能
}

// 计算对齐后的刻度位置
static lv_coord_t urq_calculate_aligned_tick_position(
    lv_coord_t obj_start, lv_coord_t obj_size, int tick_index, int total_ticks,
    urq_scale_alignment_t alignment)
{
    if (total_ticks <= 0)
        return obj_start;

    switch (alignment) {
    case URQ_SCALE_ALIGNMENT_START:
        // 左对齐/上对齐：刻度从起始位置开始，均匀分布
        if (total_ticks == 1)
            return obj_start;
        return obj_start +
               (lv_coord_t)((obj_size * tick_index) / (total_ticks - 1));

    case URQ_SCALE_ALIGNMENT_CENTER:
        // 居中对齐：刻度在中间区域均匀分布
        if (total_ticks == 1) {
            return obj_start + obj_size / 2;
        }
        lv_coord_t tick_spacing = obj_size / (total_ticks + 1);
        return obj_start + (lv_coord_t)(tick_spacing * (tick_index + 1));

    case URQ_SCALE_ALIGNMENT_END:
        // 右对齐/下对齐：刻度从结束位置开始，均匀分布
        if (total_ticks == 1)
            return obj_start + obj_size;
        return obj_start + obj_size -
               (lv_coord_t)((obj_size * (total_ticks - 1 - tick_index)) /
                            (total_ticks - 1));

    default:
        return obj_start + (lv_coord_t)((obj_size * tick_index) / total_ticks);
    }
}

// 绘制主线的通用函数

// 绘制刻度标签的通用函数
static void urq_draw_tick_label(
    lv_draw_ctx_t *draw_ctx, lv_coord_t value, lv_coord_t x, lv_coord_t y,
    lv_coord_t width, lv_coord_t height)
{
    char label_text[32];
    snprintf(label_text, sizeof(label_text), "%d", (int)value);

    lv_draw_label_dsc_t label_dsc;
    lv_draw_label_dsc_init(&label_dsc);

    lv_area_t label_area;
    label_area.x1 = x - width / 2;
    label_area.y1 = y - height / 2;
    label_area.x2 = x + width / 2;
    label_area.y2 = y + height / 2;

    lv_draw_label(draw_ctx, &label_dsc, &label_area, label_text, NULL);
}

// 绘制次刻度的通用函数
static void urq_draw_sub_ticks_horizontal(
    lv_draw_ctx_t *draw_ctx, urq_curve_scale_t *scale, urq_project_t *project,
    urq_scale_alignment_t alignment, lv_coord_t obj_x, lv_coord_t obj_w,
    lv_coord_t line_y)
{
    if (scale->sub_tick_count <= 0 || scale->main_tick_count <= 0)
        return;

    lv_draw_line_dsc_t sub_tick_dsc;
    lv_draw_line_dsc_init(&sub_tick_dsc);
    sub_tick_dsc.color =
        urq_get_color(project->env.theme_color, &scale->sub_color);
    sub_tick_dsc.width = scale->sub_tick_width;

    // 在每个主刻度间隔中绘制次刻度
    for (int main_i = 0; main_i < scale->main_tick_count; main_i++) {
        lv_coord_t main_tick_x1 = urq_calculate_aligned_tick_position(
            obj_x, obj_w, main_i, scale->main_tick_count + 1, alignment);
        lv_coord_t main_tick_x2 = urq_calculate_aligned_tick_position(
            obj_x, obj_w, main_i + 1, scale->main_tick_count + 1, alignment);

        for (int sub_i = 1; sub_i < scale->sub_tick_count; sub_i++) {
            lv_coord_t sub_tick_x =
                main_tick_x1 + (lv_coord_t)((main_tick_x2 - main_tick_x1) *
                                            sub_i / scale->sub_tick_count);

            lv_coord_t sub_tick_y1 = line_y - scale->sub_tick_len;
            lv_coord_t sub_tick_y2 = line_y;

            lv_point_t p1 = {sub_tick_x, sub_tick_y1};
            lv_point_t p2 = {sub_tick_x, sub_tick_y2};
            lv_draw_line(draw_ctx, &sub_tick_dsc, &p1, &p2);
        }
    }
}

// 绘制弧形刻度尺
static void urq_draw_arc_ruler(
    lv_event_t *e, Self *widget, lv_draw_ctx_t *draw_ctx)
{
    lv_obj_t *obj = lv_event_get_target(e);
    urq_scale_widget_conf_t *conf = widget->conf;
    urq_curve_scale_t *scale = &conf->scale;

    lv_coord_t obj_x = lv_obj_get_x(obj);
    lv_coord_t obj_y = lv_obj_get_y(obj);
    lv_coord_t obj_w = 200;
    lv_coord_t obj_h = 300;

    lv_coord_t center_x = obj_x + obj_w / 2;
    lv_coord_t center_y = obj_y + obj_h / 2;

    urq_project_t *project = widget->user_data.project;

    // 绘制主弧线
    if (conf->scale.line != NULL) {
        lv_draw_arc_dsc_t arc_dsc;
        lv_draw_arc_dsc_init(&arc_dsc);
        arc_dsc.color =
            urq_get_color(project->env.theme_color, &conf->scale.line->color);
        arc_dsc.width = conf->scale.line->width;
        arc_dsc.start_angle = 0;
        arc_dsc.end_angle = 360;
        lv_point_t center_point = {center_x, center_y};

        lv_draw_arc(
            draw_ctx, &arc_dsc, &center_point, (uint16_t)conf->radius, 0, 360);
    }

    // 计算刻度参数
    if (scale->min_value == -1 || scale->max_value == -1)
        return;

    lv_coord_t value_range = scale->max_value - scale->min_value;
    if (value_range <= 0)
        return;

    // 绘制主刻度
    if (scale->main_tick_count > 0) {
        lv_draw_line_dsc_t tick_dsc;
        lv_draw_line_dsc_init(&tick_dsc);
        tick_dsc.color =
            urq_get_color(project->env.theme_color, &scale->main_color);
        tick_dsc.width = scale->main_tick_width;

        for (int i = 0; i <= scale->main_tick_count; i++) {
            // 计算当前刻度的角度
            float angle_ratio = (float)i / (float)scale->main_tick_count;
            int16_t current_angle = (int16_t)(angle_ratio * 360);

            // 转换为弧度
            float angle_rad = (float)current_angle * (float)M_PI / 180.0f;

            // 计算刻度线的起点和终点
            lv_coord_t inner_radius = conf->radius - scale->main_tick_len;
            lv_coord_t outer_radius = conf->radius;

            lv_coord_t x1 =
                center_x + (lv_coord_t)(inner_radius * cosf(angle_rad));
            lv_coord_t y1 =
                center_y + (lv_coord_t)(inner_radius * sinf(angle_rad));
            lv_coord_t x2 =
                center_x + (lv_coord_t)(outer_radius * cosf(angle_rad));
            lv_coord_t y2 =
                center_y + (lv_coord_t)(outer_radius * sinf(angle_rad));

            lv_point_t p1 = {x1, y1};
            lv_point_t p2 = {x2, y2};
            lv_draw_line(draw_ctx, &tick_dsc, &p1, &p2);

            // 绘制标签
            lv_coord_t value =
                (lv_coord_t)scale->min_value +
                (lv_coord_t)((value_range * i) / scale->main_tick_count);

            // 标签位置在刻度外侧
            lv_coord_t label_radius = conf->radius + 10;
            lv_coord_t label_x =
                center_x + (lv_coord_t)(label_radius * cosf(angle_rad));
            lv_coord_t label_y =
                center_y + (lv_coord_t)(label_radius * sinf(angle_rad));

            urq_draw_tick_label(draw_ctx, value, label_x, label_y, 30, 20);
        }
    }

    // 绘制次刻度（弧形）
    if (scale->sub_tick_count > 0 && scale->main_tick_count > 0) {
        lv_draw_line_dsc_t sub_tick_dsc;
        lv_draw_line_dsc_init(&sub_tick_dsc);
        sub_tick_dsc.color =
            urq_get_color(project->env.theme_color, &scale->sub_color);
        sub_tick_dsc.width = scale->sub_tick_width;

        // 在每个主刻度间隔中绘制次刻度
        for (int main_i = 0; main_i < scale->main_tick_count; main_i++) {
            float main_angle1 =
                (float)main_i / (float)scale->main_tick_count * 360.0f;
            float main_angle2 =
                (float)(main_i + 1) / (float)scale->main_tick_count * 360.0f;

            for (int sub_i = 1; sub_i < scale->sub_tick_count; sub_i++) {
                float sub_angle =
                    main_angle1 + (main_angle2 - main_angle1) * (float)sub_i /
                                      (float)scale->sub_tick_count;
                float sub_angle_rad = sub_angle * (float)M_PI / 180.0f;

                lv_coord_t inner_radius = conf->radius - scale->sub_tick_len;
                lv_coord_t outer_radius = conf->radius;

                lv_coord_t x1 =
                    center_x + (lv_coord_t)(inner_radius * cosf(sub_angle_rad));
                lv_coord_t y1 =
                    center_y + (lv_coord_t)(inner_radius * sinf(sub_angle_rad));
                lv_coord_t x2 =
                    center_x + (lv_coord_t)(outer_radius * cosf(sub_angle_rad));
                lv_coord_t y2 =
                    center_y + (lv_coord_t)(outer_radius * sinf(sub_angle_rad));

                lv_point_t p1 = {x1, y1};
                lv_point_t p2 = {x2, y2};
                lv_draw_line(draw_ctx, &sub_tick_dsc, &p1, &p2);
            }
        }
    }
}

// 主绘制回调函数
static void urq_ruler_widget_draw_cb(lv_event_t *e)
{
    Self *widget = (Self *)lv_event_get_user_data(e);
    lv_draw_ctx_t *draw_ctx = lv_event_get_draw_ctx(e);

    if (widget->conf == NULL) {
        return;
    }

    // 根据刻度尺类型选择绘制函数
    switch (widget->conf->type) {
    case URQ_SCALE_TYPE_HORIZONTAL:
    case URQ_SCALE_TYPE_VERTICAL:
        urq_draw_line_scale(e);
        break;
    case URQ_SCALE_TYPE_ARC:
    case URQ_SCALE_TYPE_CIRCLE:
        urq_draw_arc_ruler(e, widget, draw_ctx);
        break;
    default:
        break;
    }
}

// 计算刻度线的垂直方向向量
static void urq_calculate_tick_direction(
    lv_point_t *p1, lv_point_t *p2, float *tick_dx, float *tick_dy,
    lv_coord_t tick_length, urq_scale_direction_t direction)
{
    // 计算主线的方向向量
    float line_dx = (float)(p2->x - p1->x);
    float line_dy = (float)(p2->y - p1->y);
    float line_length = sqrtf(line_dx * line_dx + line_dy * line_dy);

    if (line_length < 1e-6) {
        *tick_dx = 0;
        *tick_dy = 0;
        return;
    }

    // 计算垂直向量（逆时针旋转90度）
    float perp_dx = -line_dy / line_length;
    float perp_dy = line_dx / line_length;

    // 根据方向调整
    switch (direction) {
    case URQ_SCALE_DIRECTION_TOP_LEFT:
        // 保持垂直向量方向
        break;
    case URQ_SCALE_DIRECTION_BOTTOM_RIGHT:
        // 反转垂直向量方向
        perp_dx = -perp_dx;
        perp_dy = -perp_dy;
        break;
    case URQ_SCALE_DIRECTION_BOTH:
        // 双向刻度，长度减半
        tick_length = tick_length / 2;
        break;
    }

    *tick_dx = perp_dx * tick_length;
    *tick_dy = perp_dy * tick_length;
}

static void urq_draw_line_scale(lv_event_t *e)
{
    lv_draw_ctx_t *draw_ctx = lv_event_get_draw_ctx(e);
    urq_used(lv_event_get_target(e)); // line对象，暂时不需要使用
    Self *widget = (Self *)lv_event_get_user_data(e); // 这是主widget

    // 安全检查
    if (widget == NULL || widget->conf == NULL) {
        printf("=====> urq_draw_line_scale: widget or conf is NULL\n");
        return;
    }

    urq_curve_scale_t *scale = &widget->conf->scale;

    // 检查配置有效性
    if (scale->main_tick_count <= 0 || widget->conf->point_cnt < 2) {
        printf(
            "=====> urq_draw_line_scale: invalid config - tick_count=%d, "
            "point_cnt=%d\n",
            scale->main_tick_count, widget->conf->point_cnt);
        return;
    }

    lv_point_t p1 = widget->conf->point[0];
    lv_point_t p2 = widget->conf->point[1];

    // 计算刻度方向向量
    float tick_dx, tick_dy;
    urq_calculate_tick_direction(
        &p1, &p2, &tick_dx, &tick_dy, scale->main_tick_len,
        URQ_SCALE_DIRECTION_TOP_LEFT);

    // 设置绘制样式
    lv_draw_line_dsc_t tick_dsc;
    lv_draw_line_dsc_init(&tick_dsc);

    if (widget->user_data.project != NULL) {
        tick_dsc.color = urq_get_color(
            widget->user_data.project->env.theme_color, &scale->main_color);
    } else {
        tick_dsc.color = lv_color_hex(0x00FF00); // 绿色作为默认颜色
    }

    tick_dsc.width = scale->main_tick_width > 0 ? scale->main_tick_width : 1;
    tick_dsc.opa = LV_OPA_COVER;

    printf(
        "=====> Drawing %d ticks, tick_length=%d, direction=(%f,%f)\n",
        scale->main_tick_count, scale->main_tick_len, tick_dx, tick_dy);

    // 绘制主刻度
    for (int i = 0; i < scale->main_tick_count; i++) {
        // 计算刻度在主线上的位置（使用对齐方式）
        float t;
        if (scale->main_tick_count == 1) {
            t = 0.5f; // 单个刻度居中
        } else {
            t = (float)i / (float)(scale->main_tick_count - 1);
        }

        // 主线上的点
        lv_point_t line_point = {
            (lv_coord_t)(p1.x + t * (p2.x - p1.x)),
            (lv_coord_t)(p1.y + t * (p2.y - p1.y))};

        // 刻度线的起点和终点
        lv_point_t tick_start = {
            (lv_coord_t)(line_point.x), (lv_coord_t)(line_point.y)};

        lv_point_t tick_end = {
            (lv_coord_t)(line_point.x + tick_dx),
            (lv_coord_t)(line_point.y + tick_dy)};

        printf(
            "=====> Tick %d: line_point=(%d,%d), tick=(%d,%d)->(%d,%d)\n", i,
            line_point.x, line_point.y, tick_start.x, tick_start.y, tick_end.x,
            tick_end.y);

        lv_draw_line(draw_ctx, &tick_dsc, &tick_start, &tick_end);

        // 绘制刻度标签（如果需要）
        if (scale->min_value != -1 && scale->max_value != -1) {
            lv_coord_t value_range = scale->max_value - scale->min_value;
            lv_coord_t value = scale->min_value + (lv_coord_t)(value_range * t);

            // 标签位置在刻度线外侧
            lv_coord_t label_x = tick_end.x + (lv_coord_t)(tick_dx * 0.3f);
            lv_coord_t label_y = tick_end.y + (lv_coord_t)(tick_dy * 0.3f);

            urq_draw_tick_label(draw_ctx, value, label_x, label_y, 40, 20);
        }
    }

    // 绘制次刻度
    if (scale->sub_tick_count > 0 && scale->main_tick_count > 1) {
        // 计算次刻度方向向量（通常比主刻度短）
        float sub_tick_dx, sub_tick_dy;
        urq_calculate_tick_direction(
            &p1, &p2, &sub_tick_dx, &sub_tick_dy, scale->sub_tick_len,
            URQ_SCALE_DIRECTION_TOP_LEFT);

        // 设置次刻度绘制样式
        lv_draw_line_dsc_t sub_tick_dsc;
        lv_draw_line_dsc_init(&sub_tick_dsc);

        if (widget->user_data.project != NULL) {
            sub_tick_dsc.color = urq_get_color(
                widget->user_data.project->env.theme_color, &scale->sub_color);
        } else {
            sub_tick_dsc.color = lv_color_hex(0x808080); // 灰色作为默认颜色
        }

        sub_tick_dsc.width =
            scale->sub_tick_width > 0 ? scale->sub_tick_width : 1;
        sub_tick_dsc.opa = LV_OPA_COVER;

        // 在每个主刻度间隔中绘制次刻度
        for (int main_i = 0; main_i < scale->main_tick_count - 1; main_i++) {
            float t1 = (float)main_i / (float)(scale->main_tick_count - 1);
            float t2 =
                (float)(main_i + 1) / (float)(scale->main_tick_count - 1);

            for (int sub_i = 1; sub_i < scale->sub_tick_count; sub_i++) {
                float sub_t = t1 + (t2 - t1) * (float)sub_i /
                                       (float)scale->sub_tick_count;

                // 次刻度在主线上的点
                lv_point_t sub_line_point = {
                    (lv_coord_t)(p1.x + sub_t * (p2.x - p1.x)),
                    (lv_coord_t)(p1.y + sub_t * (p2.y - p1.y))};

                // 次刻度线的起点和终点
                lv_point_t sub_tick_start = {
                    (lv_coord_t)(sub_line_point.x),
                    (lv_coord_t)(sub_line_point.y)};

                lv_point_t sub_tick_end = {
                    (lv_coord_t)(sub_line_point.x + sub_tick_dx),
                    (lv_coord_t)(sub_line_point.y + sub_tick_dy)};

                lv_draw_line(
                    draw_ctx, &sub_tick_dsc, &sub_tick_start, &sub_tick_end);
            }
        }
    }
}
