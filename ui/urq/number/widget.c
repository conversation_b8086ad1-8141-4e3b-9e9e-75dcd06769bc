#include "urq/number/widget.h"
#include "lvgl.h"
#include "urq/page.h"
#include "urq/preload.h"
#include "urq/user_data.h"
#include "urq/util/anim.h"
#include "urq/util/control.h"
#include "urq/util/data.h"
#include "urq/util/event.h"
#include "urq/util/font.h"
#include "urq/util/marquee.h"
#include "urq/util/state.h"
#include "urq/util/style.h"
#include "urq/widget.h"
#include "urq/widget/property.h"
#include "urq_conf/req/action.h"
#include "urq_conf/widget/number.h"
#include "urq_conf/widget/type.h"

#define Self urq_number_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_NUMBER_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(urq_number_widget_t),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_number_widget_config, NULL, urq_number_widget_set_state,
        urq_number_widget_set_property);

    widget->text = lv_textarea_create(obj);
    widget->conf = NULL;
    widget->common_conf = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;

    if (widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);
        if (widget->conf != NULL) {
            urq_number_conf_free_inplace(widget->conf);
            urq_free(widget->conf);
            widget->conf = NULL;
        }
        if (widget->common_conf != NULL) {
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
            widget->common_conf = NULL;
        }
        widget->text = NULL;
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (urq_event_filter_exit(class_p, e)) {
        return;
    }

    Self *widget = (Self *)lv_event_get_target(e);
    urq_widget_common_conf_t *common_conf = widget->common_conf;
    urq_page_t *const page = widget->user_data.page;
    static uint32_t count = 0;
    // 检查是否执行点击事件
    if (urq_event_check_exec_click(e, &count, common_conf)) {
        // exec
        // printf("可执行\n");
    }

    if (common_conf != NULL && common_conf->has_action) {
        if (urq_page_add_frame_cb3(
                page, urq_req_wirte_set_action, urq_noop3, (void *)page,
                (void *)(intptr_t)common_conf->id, (void *)(intptr_t)e->code)) {
        }
    }
}

lv_obj_t *urq_number_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj;

    obj = lv_obj_class_create_obj(&URQ_NUMBER_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

static void urq_textarea_click_event_cb(lv_event_t *e)
{
    Self *widget = (Self *)lv_event_get_user_data(e);
    urq_project_t *const project = widget->user_data.project;
    lv_obj_t *const obj = widget->text;
    if (widget->conf->support_input && widget->conf->keyboard_page_id > 0) {
        urq_keyboard_trigger_data_t *trigger_data =
            urq_malloc(sizeof(urq_keyboard_trigger_data_t));
        trigger_data->max_value = urq_malloc(sizeof(urq_data_t));
        trigger_data->min_value = urq_malloc(sizeof(urq_data_t));
        trigger_data->input = obj;
        trigger_data->max_value = widget->conf->max_value;
        trigger_data->min_value = widget->conf->min_value;
        trigger_data->confirm_win = widget->common_conf->confirm_win;
        trigger_data->type = URQ_WIDGET_TYPE_NUMBER;
        trigger_data->is_init = false;
        urq_event_click_input(
            (lv_obj_t *)project, widget->conf->keyboard_page_id, trigger_data);
    }
}

static void urq_number_widget_conf(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *widget = (Self *)self;
    urq_used(mut_conf);

    // 设置输入框基本属性
    lv_textarea_set_cursor_click_pos(widget->text, false);
    lv_textarea_set_one_line(widget->text, true);

    /// 设置密码模式
    if (widget->conf->pwd_mode) {
        lv_textarea_set_password_mode(widget->text, true);
        lv_textarea_set_password_bullet(widget->text, "*");
    }

    // 设置字体
    // if (widget->conf->font != NULL) {
    //     urq_set_font(self, widget->conf->font);
    // }

    if (!widget->conf->support_input) {
        lv_obj_clear_flag(widget->text, LV_OBJ_FLAG_CLICKABLE);
    } else {
        if (widget->conf->keyboard_page_id > 0) {
            lv_obj_add_event_cb(
                widget->text, urq_textarea_click_event_cb, LV_EVENT_CLICKED,
                self);
        }
    }

    urq_number_widget_set_number_value(self, widget->conf->value->double_value);
}

void urq_number_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;

    widget->conf = mut_conf->config.number;
    widget->common_conf = mut_conf->common_conf;

    urq_widget_config(self, mut_conf, urq_number_widget_conf);
}

void urq_number_widget_set_property_callback(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    urq_used(self);
    urq_used(index);
    urq_used(value);
    switch (property_id) {
    // case URQ_PROPERTY_ID_TEXT:
    //     lv_label_set_text(widget->label, (const char *)value);
    //     break;
    default:
        break;
    }
}

void urq_number_widget_set_property(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    urq_number_widget_t *const widget = (urq_number_widget_t *)self;
    urq_widget_set_property(
        self, widget->common_conf, property_id, index, value,
        urq_number_widget_set_property_callback);
}

void urq_number_widget_set_value(lv_obj_t *self, const char *value)
{
    double v = atof(value);
    urq_number_widget_set_number_value(self, v);
}

void urq_number_widget_set_number_value(lv_obj_t *self, double value)
{
    urq_number_widget_t *const widget = (urq_number_widget_t *)self;
    urq_number_conf_t *const conf = widget->conf;

    int is_less = 0;

    if (conf->min_value->data_format >= DATA_TYPE_INT32) {
        if (conf->min_value->int32_value > value ||
            value > conf->max_value->int32_value) {
            if (conf->min_value->int32_value > value) {
                is_less = 1;
            } else if (conf->max_value->int32_value < value) {
                is_less = -1;
            }
        }
    } else {
        if (conf->min_value->double_value > value ||
            value > conf->max_value->double_value) {
            if (conf->min_value->double_value > value) {
                is_less = 1;
            } else if (conf->max_value->double_value < value) {
                is_less = -1;
            }
        }
    }

    static lv_anim_t anim;
    if (is_less == 1) {
        urq_set_anim_flashing(
            widget->text, &anim, conf->less_than_min_flash_time);
        urq_set_style_bg(
            (lv_obj_t *)widget, conf->less_than_min_color, LV_PART_MAIN);
    } else if (is_less == -1) {
        urq_set_anim_flashing(
            widget->text, &anim, conf->greater_than_max_flash_time);
        urq_set_style_bg(
            (lv_obj_t *)widget, conf->greater_than_max_color, LV_PART_MAIN);
    }

    char buf[128] = {0};
    char *prefix = "";
    if (value < 0) {
        prefix = "-";
    } else if (widget->conf->show_plus_sign) {
        prefix = "+";
    }

    /// 格式化数字
    snprintf(
        buf, sizeof(buf), "%s%0*.*f", prefix, widget->conf->integer_digits,
        widget->conf->decimal_digits, value);

    /// 添加千位分隔符
    if (widget->conf->show_thousands_separator) {
        char out[128] = {0};
        urq_add_thousands_separator(buf, out, sizeof(out));
        lv_textarea_set_text(widget->text, out);
    } else {
        lv_textarea_set_text(widget->text, buf);
    }
}

void urq_number_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platfrom);
}
