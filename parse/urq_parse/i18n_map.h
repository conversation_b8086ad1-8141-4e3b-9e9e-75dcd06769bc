#pragma once

#include "urq/fs/file.h"
#include "urq/i18n/id.h"
#include "urq_conf/datastruct/map/i18n.h"

#ifndef URQ_PARSE__I18N_MAP_H
#define URQ_PARSE__I18N_MAP_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 解析 i18n 配置
///
/// @param file     配置文件
/// @param lang_id  语言 id
/// @param out_map  输出 i18n map
/// @returns 0 成功，-1 失败
int urq_parse__i18n_map(
    const uint8_t *const data, size_t size, const urq_i18n_lang_id_t lang_id,
    urq_i18n_map_t *mut_map)
    __attribute__((__nonnull__(1, 4), __warn_unused_result__()));

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ_PARSE__I18N_MAP_H
