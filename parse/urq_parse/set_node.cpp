#include "urq_parse/set_node.hpp"
#include "urq/log/debug.h"
#include "urq/widget/conf.h"
#include "urq/widget/type.h"
#include "urq_parse/common/set_confirm.hpp"
#include "urq_parse/common/set_graphic.hpp"
#include "urq_parse/common/set_state_property.hpp"
#include "urq_parse/util.hpp"
#include "urq_parse/widget/bar_curve.hpp"
#include "urq_parse/widget/bit.hpp"
#include "urq_parse/widget/button.hpp"
#include "urq_parse/widget/calendar.hpp"
#include "urq_parse/widget/clone.hpp"
#include "urq_parse/widget/dropList.hpp"
#include "urq_parse/widget/group.hpp"
#include "urq_parse/widget/image.hpp"
#include "urq_parse/widget/keyboard.hpp"
#include "urq_parse/widget/label.hpp"
#include "urq_parse/widget/linear.hpp"
#include "urq_parse/widget/meter.hpp"
#include "urq_parse/widget/number.hpp"
#include "urq_parse/widget/rollerList.hpp"
#include "urq_parse/widget/ruler.hpp"
#include "urq_parse/widget/scatter_curve.hpp"
#include "urq_parse/widget/string.hpp"
#include "urq_parse/widget/trend_curve.hpp"
#include "urq_parse/widget/word.hpp"
#include "urq_parse/widget/xy_curve.hpp"
namespace urq::parse {

static inline int set_type(
    Context &ctx, urq_widget_conf_t *&ptr, Widget const &proto)
{
    using WidgetCase = Widget::WidgetCase;
    switch (proto.widget_case()) {
    case WidgetCase::kWidgetNumber:
        ptr->type = URQ_WIDGET_TYPE_NUMBER;
        return widget::set_number(ctx, ptr->config.number, proto);
    case WidgetCase::kWidgetString:
        ptr->type = URQ_WIDGET_TYPE_STRING;
        return widget::set_string(ctx, ptr->config.string, proto);
    case WidgetCase::kWidgetText:
        ptr->type = URQ_WIDGET_TYPE_LABEL_WIDGET;
        return widget::set_label(ctx, ptr->config.label, proto);
    case WidgetCase::kWidgetGraphic:
        ptr->type = URQ_WIDGET_TYPE_IMAGE_WIDGET;
        return widget::set_image(ctx, ptr->config.image, proto);
    case WidgetCase::kWidgetBit:
        ptr->type = URQ_WIDGET_TYPE_BIT;
        return widget::set_bit(ctx, ptr->config.bit, proto);
    case WidgetCase::kWidgetWord:
        ptr->type = URQ_WIDGET_TYPE_WORD;
        return widget::set_word(ctx, ptr->config.word, proto);
    case WidgetCase::kWidgetMatrixButton:
        ptr->type = URQ_WIDGET_TYPE_KEYBOARD;
        return widget::set_keyboard(ctx, ptr->config.keyboard, proto);
    case WidgetCase::kWidgetButton:
        ptr->type = URQ_WIDGET_TYPE_BUTTON;
        return widget::set_button(ctx, ptr->config.button, proto);
    case WidgetCase::kWidgetClone:
        ptr->type = URQ_WIDGET_TYPE_CLONE;
        return widget::set_clone(ctx, ptr->config.clone, proto);
    case WidgetCase::kWidgetTrendCurve:
        ptr->type = URQ_WIDGET_TYPE_TREND_CURVE;
        return widget::set_trend_curve(ctx, ptr->config.trend_curve, proto);
    case WidgetCase::kWidgetXyCurve:
        ptr->type = URQ_WIDGET_TYPE_XY_CURVE;
        return widget::set_xy_curve(ctx, ptr->config.xy_curve, proto);
    case WidgetCase::kWidgetBarCurve:
        ptr->type = URQ_WIDGET_TYPE_BAR_CURVE;
        return widget::set_bar_curve(ctx, ptr->config.bar_curve, proto);
    case WidgetCase::kWidgetScatterCurve:
        ptr->type = URQ_WIDGET_TYPE_SCATTER_CURVE;
        return widget::set_scatter_curve(ctx, ptr->config.scatter_curve, proto);
    case WidgetCase::kWidgetCalendar:
        ptr->type = URQ_WIDGET_TYPE_CALENDAR;
        return widget::set_calendar(ctx, ptr->config.calendar, proto);
    case WidgetCase::kWidgetRollerList:
        ptr->type = URQ_WIDGET_TYPE_ROLLER_LIST;
        return widget::set_rollerList(ctx, ptr->config.roller_list, proto);
    case WidgetCase::kWidgetDropList:
        ptr->type = URQ_WIDGET_TYPE_DROP_LIST;
        return widget::set_dropList(ctx, ptr->config.drop_list, proto);
    case WidgetCase::kWidgetGroup:
        ptr->type = URQ_WIDGET_TYPE_GROUP;
        return widget::set_group(ctx, ptr->config.group, proto);
    case WidgetCase::kWidgetMeter:
        ptr->type = URQ_WIDGET_TYPE_METER;
        return widget::set_meter(ctx, ptr->config.meter, proto);
    case WidgetCase::kWidgetRuler:
        ptr->type = URQ_WIDGET_TYPE_RULER;
        return widget::set_ruler(ctx, ptr->config.ruler, proto);
    case WidgetCase::kWidgetLinear:
        ptr->type = URQ_WIDGET_TYPE_LINEAR;
        return widget::set_linear(ctx, ptr->config.linear, proto);
    default:
        log_e("unknown widget type: %d\n", proto.widget_case());
        return -1;
    }

    return -1;
}

/// @brief 设置组件的位置
///
/// @param ctx   上下文
/// @param dst   配置的目标结构
/// @param proto 配置的来源结构
/// @return int 是否设置成功
inline int set_pos(
    Context &ctx, urq_widget_conf_t &dst, const Widget &proto) noexcept
{
    urq_used(ctx);
    if (proto.has_location()) {
        dst.pos.x = (lv_coord_t)proto.location().left_i16();
        dst.pos.y = (lv_coord_t)proto.location().top_i16();
    } else {
        // TODO: 处理未设置位置的情况
        dst.pos.x = 0;
        dst.pos.y = 0;
    }

    return 0;
}

/// @brief 设置组件的大小
///
/// @param ctx   上下文
/// @param dst   配置的目标结构
/// @param proto 配置的来源结构
/// @return int  是否设置成功
inline int set_size(
    Context &ctx, urq_widget_conf_t &dst, const Widget &proto) noexcept
{
    urq_used(ctx);

    if (proto.has_size()) {
        dst.size.w = (lv_coord_t)proto.size().width_i16();
        dst.size.h = (lv_coord_t)proto.size().height_i16();
    } else {
        // TODO: 处理未设置大小的情况
        dst.size.w = 100;
        dst.size.h = 100;
    }

    return 0;
}

int set_node(Context &ctx, urq_widget_conf_t *&ptr, Widget const &proto)
{
    ptr = malloc<urq_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_widget_conf_init_inplace(ptr);
    // 图元ID
    ptr->id = (urq_widget_id_t)proto.id_u16();
    if (ptr->common_conf == NULL) {
        ptr->common_conf = malloc<urq_widget_common_conf_t>();
        if (ptr->common_conf == nullptr) {
            return -1;
        }
        urq_widget_common_conf_init_inplace(ptr->common_conf);
    }

    ptr->common_conf->id = (urq_widget_id_t)proto.id_u16();

    if (proto.has_state_property()) {
        urq::parse::common::set_state_property(
            ctx, ptr->common_conf->stat_property, proto.state_property());
    }

    ptr->common_conf->min_press_time = (uint16_t)proto.min_press_time_u16();
    ptr->common_conf->min_press_interval =
        (uint16_t)proto.min_press_interval_u16();

    if (proto.has_confirm_param()) {
        urq::parse::common::set_confirm(
            ctx, ptr->common_conf->confirm_win, proto.confirm_param());
    }

    if (proto.style().size() > 0) {
        urq::parse::common::set_style_list(
            ctx, ptr->common_conf->styles, proto.style());
    }

    if (proto.graphic().size() > 0) {
        urq::parse::common::set_graphic_list(
            ctx, ptr->common_conf->graphics, proto.graphic());
    }

    if (proto.text().size() > 0) {
        urq::parse::common::set_text_list(
            ctx, ptr->common_conf->texts, proto.text());
    }

    if (proto.actions_u16().size() > 0) {
        ptr->common_conf->has_action = true;
    }

    if (set_type(ctx, ptr, proto)) {
        urq_widget_conf_free_inplace(ptr);
        urq_free(ptr);
        return -1;
    }

    // ptr->id = id;
    if (set_pos(ctx, *ptr, proto)) {
        urq_widget_conf_free_inplace(ptr);
        free(ptr);
        return -1;
    }
    if (set_size(ctx, *ptr, proto)) {
        urq_widget_conf_free_inplace(ptr);
        free(ptr);
        return -1;
    }

    printf(
        "parse widget: [widgetid: %d] [widgettype: %s]\n", ptr->common_conf->id,
        urq_widget_type_to_string(ptr->type));

    return 0;
}

} // namespace urq::parse
