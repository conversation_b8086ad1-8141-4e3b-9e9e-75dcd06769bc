#pragma once

#include "urq/fs/file.h"
#include "urq/preload.h"
#include "urq_conf/system_variable.h"
#include "urq_conf/widget/group_conf.h"
#include "urq_conf/widget/parse_context.h"

#ifndef URQ_PARSE__WIDGET_GROUP_H
#define URQ_PARSE__WIDGET_GROUP_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 解析组件列表
/// @param group_id 当前要加载的 widget group 索引
/// @param ref_file 资源文件
/// @param ref_ctx  上下文
/// @param ref_var_system 变量系统
/// @param out_conf 输出配置
/// @return 0 成功，-1 失败
int urq_parse__widget_group(
    urq_page_id_t group_id,                          //
    const uint8_t *const data,                       //
    size_t size,                                     //
    const urq_widget_parse_context_t *const ref_ctx, //
    const urq_device_var_t *const ref_var_system,    //
    // const urq_atag_project_t *const ref_atags,       //
    urq_widget_group_conf_t *const out_conf   //
    )                                         //
    __attribute__((__nonnull__(2, 4, 5)))     //
    __attribute__((__warn_unused_result__())) //
    ;

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ_PARSE__WIDGET_GROUP_H
