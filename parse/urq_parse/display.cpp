#include "urq_parse/display.h"
#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/set_display.hpp"
#include "znd/project/v1/display.pb.h"

int urq_parse__display(
    const urq_widget_parse_context_t *const ref_context,
    const urq_device_var_t *const ref_var_system, const uint8_t *const data,
    size_t size, urq_display_conf_t *out_display)
{
    urq_page_id_t page_id = 0;

    znd::project::v1::DisplayList display_list;
    urq::parse::Context ctx(*ref_context, *ref_var_system, page_id);
    try {
        display_list.ParseFromArray(data, (int)size);
    } catch (const std::exception &e) {
        errno = EINVAL;
        return -1;
    }

    if (urq::parse::set_display(ctx, out_display, display_list)) {
        return -1;
    }

    return 0;
}
