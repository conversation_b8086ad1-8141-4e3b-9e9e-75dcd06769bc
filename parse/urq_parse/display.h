#pragma once

#include "urq_conf/display.h"
#include "urq_conf/system_variable.h"
#include "urq_conf/widget/parse_context.h"

#ifndef URQ_PARSE__DISPLAY_H
#define URQ_PARSE__DISPLAY_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 解析显示配置
int urq_parse__display(
    const urq_widget_parse_context_t *const ctx,  //
    const urq_device_var_t *const ref_var_system, //
    const uint8_t *const data,                    //
    size_t size,                                  //
    urq_display_conf_t *out_display               //
    ) __attribute__((__nonnull__(1, 2, 3, 5)));

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ_PARSE__DISPLAY_H
