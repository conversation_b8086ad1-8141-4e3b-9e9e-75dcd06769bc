#pragma once

#include "urq/preload.h"
#include "urq_conf/common/graphic.h"
#include "urq_conf/datastruct/list/graphic.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/ListBuilder.hpp"
#include "urq_parse/common/set_style.hpp"
#include "urq_parse/util.hpp"
#ifndef URQ_PARSE__COMMON__SET_GRAPHIC_HPP
#define URQ_PARSE__COMMON__SET_GRAPHIC_HPP
namespace urq::parse::common {

/// @brief 设置图形
static inline int set_graphic(
    Context &ctx, urq_graphic_t *&dst,
    znd::project::v1::GraphicReference const &proto)
{
    urq_used(ctx);
    if (dst == NULL) {
        dst = (urq_graphic_t *)urq_malloc(sizeof(urq_graphic_t));
        urq_graphic_init_inplace(dst);
        if (dst == NULL) {
            return -1;
        }
    }

    dst->graphic_type = (urq_graphic_type_t)proto.graphic_type();
    dst->src = proto.src();
    if (proto.has_opacity_u8()) {
        dst->opacity = (lv_opa_t)proto.opacity_u8();
    }

    if (proto.has_recolor()) {
        set_color(ctx, dst->color, proto.recolor());
    }
    return 0;
    return -1;
}

/// @brief 设置图片列表
/// @param ctx 编译上下文
/// @param dst 目标动作列表
/// @param src 源动作列表
/// @returns 0 on success, -1 on failure
static inline int set_graphic_list(
    Context &ctx, urq_graphic_ptr_list_t *&dst,
    RepeatedPtrField<znd::project::v1::GraphicReference> const &proto) noexcept
{
    if (proto.empty()) {
        return 0;
    }
    ListBuilder<urq_graphic_t *> builder(urq_graphic_free_inplace);
    if (builder.set_capacity((size_t)proto.size())) {
        return -1;
    }

    if (dst == NULL) {
        dst = (urq_graphic_ptr_list_t *)urq_malloc(
            sizeof(urq_graphic_ptr_list_t));
        urq_graphic_ptr_list_init_inplace(dst);
    }

    for (auto const &_graphic : proto) {
        urq_graphic_t *graphic = nullptr;
        if (set_graphic(ctx, graphic, _graphic)) {
            return -1;
        }
        builder.push(graphic);
    }
    builder.build(dst->size, dst->data);
    return 0;
}

} // namespace urq::parse::common

#endif // URQ_PARSE__COMMON__SET_GRAPHIC_HPP
