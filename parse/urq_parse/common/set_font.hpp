#pragma once

#include "lvgl.h"
#include "urq/log/log.h"
#include "urq_conf/style/font.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/set_pos_offset.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__COMMON__SET_FONT_HPP
#define URQ_PARSE__COMMON__SET_FONT_HPP
namespace urq::parse::common {

#if 0
    static int set_font(
    const urq_widget_parse_context_t &widget_ctx, urq_font_t *&dst,
    FontProperties const &proto)
{
    urq_used(widget_ctx);
    if (dst == NULL) {
        dst = (urq_font_t *)urq_malloc(sizeof(urq_font_t));
        if (dst == NULL) {
            return -1;
        }
        urq_font_init_inplace(dst);
    }
    dst->id = (uint8_t)proto.font_id_u8();

    if (proto.has_text_padding()) {
        set_pos_offset(widget_ctx, dst->text_padding, proto.text_padding());
    }

    if (proto.has_text_align())
        dst->text_align = (lv_align_t)proto.text_align();

    if (proto.has_text_color()) {
        set_color(widget_ctx, dst->color, proto.text_color());
    }

    if (proto.has_blink_interval_u8())
        dst->blink_interval = (uint8_t)proto.blink_interval_u8();

    if (proto.has_marquee()) {
        set_marquee(widget_ctx, dst->marquee, proto.marquee());
    }

    return 0;
}

/// @brief 设置字体
inline static int set_font(
    Context &ctx, urq_font_t *&dst, FontProperties const &proto)
{
    set_font(ctx.get_context(), dst, proto);
    return 0;
}

/// @brief 设置字体
/// 根据给定的 font[language_id] 设置字体
///
/// @param ctx   上下文
/// @param dst   目标字体
/// @param proto 字体属性
/// @return int
inline static int set_font_list(
    Context &ctx, urq_font_t *&dst,
    RepeatedPtrField<znd::project::v1::FontProperties> const &proto)
{
    if (proto.size() <= ctx.language_id()) {
        urq_log_w(
            "Font properties is not set for language %d\n", ctx.language_id());
        return 0;
    }
    return set_font(ctx, dst, proto[ctx.language_id()]);
}
#endif

} // namespace urq::parse::common
#endif // URQ_PARSE__COMMON__SET_FONT_HPP
