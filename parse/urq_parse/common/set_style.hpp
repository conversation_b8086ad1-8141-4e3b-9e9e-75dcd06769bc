#pragma once

#include "urq/preload.h"
#include "urq/style/style.h"
#include "urq/theme/color.h"
#include "urq/todo.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/ListBuilder.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/common/set_font.hpp"
#include "urq_parse/set_marquee.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__COMMON__SET_STYLE_HPP
#define URQ_PARSE__COMMON__SET_STYLE_HPP

namespace urq::parse::common {

static inline void set_style_border(
    const urq_widget_parse_context_t &context, urq_style_border_t *&conf,
    ::znd::project::v1::StyleBorder const &proto) noexcept
{
    if (conf == NULL) {
        conf = (urq_style_border_t *)urq_malloc(sizeof(urq_style_border_t));
        urq_style_border_init_inplace(conf);
    }
    conf->width = (lv_coord_t)proto.width_u8();
    conf->left = proto.left();
    conf->top = proto.top();
    conf->right = proto.right();
    conf->bottom = proto.bottom();
    conf->internal = proto.internal();

    if (proto.has_color()) {
        set_color(context, conf->color, proto.color());
    }
}

/// @brief 设置边框宽
static inline void set_style_border(
    Context &ctx, urq_style_border_t *&conf,
    ::znd::project::v1::StyleBorder const &proto) noexcept
{
    set_style_border(ctx.get_context(), conf, proto);
}

static inline void set_style_shadow(
    const urq_widget_parse_context_t &context, urq_style_shadow_t *&conf,
    ::znd::project::v1::StyleShadow const &proto) noexcept
{
    if (conf == NULL) {
        conf = (urq_style_shadow_t *)urq_malloc(sizeof(urq_style_shadow_t));
        urq_style_shadow_init_inplace(conf);
    }

    if (proto.has_color()) {
        set_color(context, conf->bg, proto.color());
    }
    conf->enable = proto.enable();
    conf->offset_x = (lv_coord_t)proto.offset_x_i16();
    conf->offset_y = (lv_coord_t)proto.offset_y_i16();
    conf->spread = (lv_coord_t)proto.spread_i16();
    conf->width = (lv_coord_t)proto.width_i16();
}

/// @brief 设置阴影
static inline void set_style_shadow(
    Context &ctx, urq_style_shadow_t *&conf,
    ::znd::project::v1::StyleShadow const &proto) noexcept
{
    set_style_shadow(ctx.get_context(), conf, proto);
}

/// @brief 设置样式
static inline int set_style(
    Context &ctx, urq_style_t &conf, StyleProperties const &proto,
    bool ref) noexcept
{
    urq_used(ref);

    const urq_style_t *temp_style = NULL;
    if (proto.has_style_id_u8()) {
        temp_style = ctx.get_theme_style(proto.style_id_u8());
    }

    if (proto.has_background_color()) {
        set_color(ctx, conf.bg, proto.background_color());
    } else {
        if (temp_style != NULL)
            conf.bg = temp_style->bg;
    }

    if (proto.has_border_props()) {
        set_style_border(ctx, conf.border, proto.border_props());
    } else {
        if (temp_style != NULL && temp_style->border != NULL) {
            conf.border = temp_style->border;
        }
    }

    if (proto.has_shadow_props()) {
        set_style_shadow(ctx, conf.shadow, proto.shadow_props());
    } else {
        if (temp_style != NULL && temp_style->shadow != NULL) {
            conf.shadow = temp_style->shadow;
        }
    }

    if (proto.has_font_id_u8()) {
        conf.font.id = ctx.get_font_id(proto.font_id_u8());
    }

    if (proto.has_text_location()) {
        auto _text_map = proto.text_location().multi_lang();
        auto it = _text_map.find((uint32_t)ctx.language_id());
        if (it != _text_map.end()) {
            conf.font.text_align = (lv_align_t)it->second.text_align();
            if (it->second.has_text_padding()) {
                set_pos_offset(
                    ctx, conf.font.text_padding, it->second.text_padding());
            }
        }
    }

    if (proto.has_text_color()) {
        set_color(ctx, conf.font.color, proto.text_color());
    }

    if (proto.has_blink_interval_u8())
        conf.font.blink_interval = (uint8_t)proto.blink_interval_u8();

    if (proto.has_marquee()) {
        ::urq::parse::set_marquee(ctx, conf.font.marquee, proto.marquee());
    }
    return 0;
}

/// @brief 设置样式
static inline int set_style(
    Context &ctx, urq_style_t *&conf, StyleProperties const &proto) noexcept
{
    if (conf == NULL) {
        conf = (urq_style_t *)urq_malloc(sizeof(urq_style_t));
        urq_style_init_inplace(conf);
    }

    const urq_style_t *temp_style = NULL;
    if (proto.has_style_id_u8()) {
        temp_style = ctx.get_theme_style(proto.style_id_u8());
    }

    if (proto.has_background_color()) {
        set_color(ctx, conf->bg, proto.background_color());
    } else {
        if (temp_style != NULL)
            conf->bg = temp_style->bg;
    }

    if (proto.has_border_props()) {
        set_style_border(ctx, conf->border, proto.border_props());
    } else {
        if (temp_style != NULL && temp_style->border != NULL) {
            conf->border = temp_style->border;
        }
    }

    if (proto.has_shadow_props()) {
        set_style_shadow(ctx, conf->shadow, proto.shadow_props());
    } else {
        if (temp_style != NULL && temp_style->shadow != NULL) {
            conf->shadow = temp_style->shadow;
        }
    }

    if (proto.has_font_id_u8()) {
        conf->font.id = ctx.get_font_id(proto.font_id_u8());
    }

    if (proto.has_text_location()) {
        auto _text_map = proto.text_location().multi_lang();
        auto it = _text_map.find((uint32_t)ctx.language_id());
        if (it != _text_map.end()) {
            conf->font.text_align = (lv_align_t)it->second.text_align();
            if (it->second.has_text_padding()) {
                set_pos_offset(
                    ctx, conf->font.text_padding, it->second.text_padding());
            }
        }
    }

    if (proto.has_text_color()) {
        set_color(ctx, conf->font.color, proto.text_color());
    }

    if (proto.has_blink_interval_u8())
        conf->font.blink_interval = (uint8_t)proto.blink_interval_u8();

    if (proto.has_marquee()) {
        ::urq::parse::set_marquee(ctx, conf->font.marquee, proto.marquee());
    }

    return 0;
}

/// @brief 设置样式列表
static inline int set_style_list(
    Context &ctx, urq_style_ptr_list_t *&conf,
    RepeatedPtrField<StyleProperties> const &proto) noexcept
{
    ListBuilder<urq_style_t *> builder(urq_style_free_inplace);
    builder.set_capacity((size_t)proto.size());

    if (conf == NULL) {
        conf = (urq_style_ptr_list_t *)urq_malloc(sizeof(urq_style_ptr_list_t));
        urq_style_ptr_list_init_inplace(conf);
    }

    for (int i = 0; i < proto.size(); i++) {
        auto &style = proto[i];
        urq_style_t *state = nullptr;
        if (set_style(ctx, state, style)) {
            return -1;
        }
        builder.push(state);
    }

    builder.build(conf->size, conf->data);
    return 0;
}

} // namespace urq::parse::common

#endif // URQ_PARSE__COMMON__SET_STYLE_HPP
