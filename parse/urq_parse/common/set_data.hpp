#pragma once

#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__COMMON__SET_DATA_HPP
#define URQ_PARSE__COMMON__SET_DATA_HPP

namespace urq::parse::common {

static inline int set_data_all_type(
    Context &ctx, urq_data_t *&data,
    const ::znd::project::v1::NumberValue &proto) noexcept
{
    urq_used(ctx);

    if (data == NULL) {
        data = malloc<urq_data_t>();
        if (data == NULL) {
            return -1;
        }
        urq_data_init_inplace(data);
    }
    switch (proto.from_case()) {
    case ::znd::project::v1::NumberValue::kValueI8:
        data->data_format = DATA_TYPE_INT8;
        data->int8_value = (int8_t)proto.value_i8();
        break;
    case ::znd::project::v1::NumberValue::kValueU8:
        data->data_format = DATA_TYPE_UINT8;
        data->uint8_value = (uint8_t)proto.value_u8();
        break;
    case ::znd::project::v1::NumberValue::kValueI16:
        data->data_format = DATA_TYPE_INT16;
        data->int16_value = (int16_t)proto.value_i16();
        break;
    case ::znd::project::v1::NumberValue::kValueU16:
        data->data_format = DATA_TYPE_UINT16;
        data->uint16_value = (uint16_t)proto.value_u16();
        break;
    case ::znd::project::v1::NumberValue::kValueI32:
        data->data_format = DATA_TYPE_INT32;
        data->int32_value = (int32_t)proto.value_i32();
        break;
    case ::znd::project::v1::NumberValue::kValueU32:
        data->data_format = DATA_TYPE_UINT32;
        data->uint32_value = (uint32_t)proto.value_u32();
        break;
    case ::znd::project::v1::NumberValue::kValueFloat:
        data->data_format = DATA_TYPE_FLOAT;
        data->float_value = (float)proto.value_float();
        break;
    case ::znd::project::v1::NumberValue::kValueDouble:
        data->data_format = DATA_TYPE_DOUBLE;
        data->double_value = (double)proto.value_double();
        break;
    default:
        return -1;
    }
    return 0;
}

} // namespace urq::parse::common

#endif // URQ_PARSE__COMMON__SET_DATA_HPP