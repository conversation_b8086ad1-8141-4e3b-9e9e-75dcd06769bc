#pragma once

#include "lvgl.h"
#include "urq/color.h"
#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "znd/project/v1/style.pb.h"

#ifndef URQ_PARSE__COMMON__SET_COLOR_HPP
#define URQ_PARSE__COMMON__SET_COLOR_HPP
namespace urq::parse::common {

static void set_color(
    const urq_widget_parse_context_t &widget_ctx, urq_color_ref_t &conf,
    ::znd::project::v1::ColorReference const &proto) noexcept
{
    if (proto.has_color_var_id_u8()) {
        conf.id = (int8_t)proto.color_var_id_u8();
        urq_color_rgba_t *color = urq_theme_color_get(
            widget_ctx.theme_color, proto.color_var_id_u8());
        if (color != NULL) {
            conf.rgba.rgba = color->rgba;
        }
    } else if (proto.has_color_value_u32()) {
        conf.rgba.rgba = proto.color_value_u32();
    }
}

/// @brief 设置边框宽
static inline void set_color(
    Context &ctx, urq_color_ref_t &conf,
    ::znd::project::v1::ColorReference const &proto) noexcept
{
    set_color(ctx.get_context(), conf, proto);
}

} // namespace urq::parse::common
#endif // URQ_PARSE__COMMON__SET_COLOR_HPP