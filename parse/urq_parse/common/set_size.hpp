#pragma once

#include "urq/size.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"
#include <sys/types.h>

#ifndef URQ_PARSE__COMMON__SET_SIZE_HPP
#define URQ_PARSE__COMMON__SET_SIZE_HPP
namespace urq::parse::common {

/// @brief 设置大小
inline static int set_size(
    Context &ctx, urq_size_t &dst, ::znd::project::v1::Size const &proto)
{
    urq_used(ctx);
    dst.w = (int16_t)proto.width();
    dst.h = (int16_t)proto.height();

    return 0;
}

} // namespace urq::parse::common
#endif // URQ_PARSE__SET_FONT_HPP
