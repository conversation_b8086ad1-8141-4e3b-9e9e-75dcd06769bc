#pragma once

// #include "urq_conf/widget/label.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_text.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__WIDGET__LABEL_HPP
#define URQ_PARSE__WIDGET__LABEL_HPP
namespace urq::parse::widget {

/// @brief 设置 widget 下的 label 组件
/// 传进来的的配置需要是已经初始化过的
/// @param ctx   编译上下文
/// @param ptr   节点配置指针
/// @param proto 控件配置
/// @param size  祖先控件大小
/// @return 0 成功, -1 失败
static inline int set_label(
    Context &ctx, urq_label_widget_conf_t *&ptr,
    ::znd::project::v1::Widget const &proto) noexcept
{
    ptr =
        (urq_label_widget_conf_t *)urq_malloc(sizeof(urq_label_widget_conf_t));
    if (ptr == nullptr) {
        return -1;
    }
    urq_label_widget_conf_init_inplace(ptr);

    urq_used(ctx);
    urq_used(proto);
    return 0;
}

} // namespace urq::parse::widget
#endif // URQ_PARSE__WIDGET__LABEL_HPP