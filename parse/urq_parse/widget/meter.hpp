#pragma once

#include "urq_conf/widget/meter.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_font.hpp"
#include "urq_parse/common/set_point.hpp"
#include "urq_parse/common/set_scale.hpp"
#include "urq_parse/common/set_style.hpp"
#include "urq_parse/util.hpp"
#include "urq_parse/widget/arc.hpp"
#include "urq_parse/widget/scale_label.hpp"

#ifndef URQ_PARSE__WIDGET__METER_HPP
#define URQ_PARSE__WIDGET__METER_HPP

namespace urq::parse::widget {

/// @brief 滚动条输入
static inline int set_meter(
    Context &ctx, urq_meter_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    if (ptr == NULL) {
        ptr = (urq_meter_widget_conf_t *)urq_malloc(
            sizeof(urq_meter_widget_conf_t));
        if (ptr == NULL) {
            return -1;
        }
        urq_meter_widget_conf_init_inplace(ptr);
    }
    const auto &meter = proto.widget_meter();
    common::set_scale(ctx, ptr->scale, meter.scale_config());
    common::set_line(ctx, ptr->pointer_config, meter.pointer_config());
    set_arc_conf(ctx, ptr->arc, meter.arc());
    set_scale_label(ctx, ptr->scale_label, meter.scale_label_config());

    common::set_color(ctx, ptr->lower_limit_color, meter.lower_limit_color());
    common::set_color(ctx, ptr->upper_limit_color, meter.upper_limit_color());
    common::set_color(ctx, ptr->middle_color, meter.middle_color());

    ptr->lower_limit = (uint16_t)meter.lower_limit_u16();
    ptr->upper_limit = (uint16_t)meter.upper_limit_u16();
    ptr->limit_width = (uint8_t)meter.limit_width_u8();
    ptr->limit_radius = (uint8_t)meter.limit_radius_u8();

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__IMAGE_HPP
