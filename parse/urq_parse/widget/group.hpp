#pragma once

#include "urq_conf/widget/group.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_text.hpp"
#include "urq_parse/set_node_list.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__GROUP_HPP
#define URQ_PARSE__WIDGET__GROUP_HPP

namespace urq::parse::widget {

/// @brief 位显示输入
static inline int set_group(
    Context &ctx, urq_group_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_group_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_group_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);

    auto &group = proto.widget_group();
    if (group.sub_widgets_size() > 0) {
        ::urq::parse::set_node_list(ctx, ptr->widgets, group.sub_widgets());
    }

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__IMAGE_HPP
