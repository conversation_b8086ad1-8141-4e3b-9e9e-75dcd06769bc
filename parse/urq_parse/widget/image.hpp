#pragma once

#include "urq/preload.h"
#include "urq_conf/widget/image.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__WIDGET__IMAGE_HPP
#define URQ_PARSE__WIDGET__IMAGE_HPP

namespace urq::parse::widget {

/// @brief 数值显示输入
static inline int set_image(
    Context &ctx, urq_image_widget_conf_t *&ptr,
    const ::znd::project::v1::Widget &proto) noexcept
{
    urq_used(ctx);
    urq_used(proto);
    ptr =
        (urq_image_widget_conf_t *)urq_malloc(sizeof(urq_image_widget_conf_t));
    if (ptr == nullptr) {
        return -1;
    }
    urq_image_widget_conf_init_inplace(ptr);

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__IMAGE_HPP
