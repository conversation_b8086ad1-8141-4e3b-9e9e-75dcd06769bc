#pragma once

#include "urq_conf/widget/drop_list.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/set_dir.hpp"
#include "urq_parse/set_symbol.hpp"
#include "urq_parse/util.hpp"
#include "urq_parse/widget/optionList.hpp"

#ifndef URQ_PARSE__WIDGET__DROPLIST_HPP
#define URQ_PARSE__WIDGET__DROPLIST_HPP

namespace urq::parse::widget {

/// @brief 下拉列表输入
static inline int set_dropList(
    Context &ctx, urq_dropList_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_dropList_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_dropList_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);

    auto &dropList = proto.widget_drop_list();
    set_optionList(ctx, ptr->conf, dropList.list());
    set_dir(ctx, ptr->direction, dropList.direction());
    ptr->always_open = dropList.always_open();
    set_symbol(ctx, &ptr->droplist_symbol, dropList.drop_list_symbol());
    if (dropList.has_list_bg_color()) {
        ::urq::parse::common::set_color(
            ctx, ptr->list_bg_color, dropList.list_bg_color());
    }

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__IMAGE_HPP
