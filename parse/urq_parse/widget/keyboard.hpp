#pragma once

#include "urq_conf/datastruct/map/string.h"
#include "urq_conf/style/style.h"
#include "urq_conf/widget/keyboard.h"
#include "urq_conf/widget/keyboard/info.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_graphic.hpp"
#include "urq_parse/common/set_style.hpp"
#include "urq_parse/common/set_text.hpp"
#include "urq_parse/util.hpp"
#include "znd/project/v1/display.pb.h"
#include <cstddef>
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__KEYBOARD_HPP
#define URQ_PARSE__WIDGET__KEYBOARD_HPP

namespace urq::parse::widget {

static inline int set_keyboard_info(
    Context &ctx, urq_keyboard_info_t *&ptr,
    const znd::project::v1::KeyboardInfo &proto) noexcept
{
    urq_used(ctx);
    if (ptr == nullptr) {
        ptr = malloc<urq_keyboard_info_t>(sizeof(urq_keyboard_info_t));
        if (ptr == nullptr) {
            return -1;
        }
        urq_keyboard_info_init_inplace(ptr);
    }
    if (proto.has_max_value_widget_id()) {
        ptr->max_value_widget_id = proto.max_value_widget_id();
    }
    if (proto.has_min_value_widget_id()) {
        ptr->min_value_widget_id = proto.min_value_widget_id();
    }
    if (proto.has_current_value_widget_id()) {
        ptr->current_value_widget_id = proto.current_value_widget_id();
    }
    if (proto.has_candidate_location()) {
        ptr->candidate_location.x =
            (lv_coord_t)proto.candidate_location().left_i16();
        ptr->candidate_location.y =
            (lv_coord_t)proto.candidate_location().top_i16();
    }
    if (proto.has_candidate_size()) {
        ptr->candidate_size.w = (lv_coord_t)proto.candidate_size().width_i16();
        ptr->candidate_size.h = (lv_coord_t)proto.candidate_size().height_i16();
    }
    return 0;
}

/// @brief 键盘输入
// static inline int set_key(
//     Context &ctx, urq_keyboard_base_conf_t *&ptr,
//     const znd::project::v1::ButtonDefine &proto) noexcept
//{
//     urq_used(ctx);
//     urq_used(proto);
//
//     ptr = malloc<urq_keyboard_base_conf_t>();
//     if (ptr == nullptr) {
//         return -1;
//     }
//     urq_keyboard_base_conf_init_inplace(ptr);
//
//     ctx.set_conf((urq_widget_conf_t *)ptr);
//
//     // TODO: 设置键盘
//     return 0;
// }

/// @brief 键盘输入
static inline int set_keyboard(
    Context &ctx, urq_keyboard_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{

    ptr = malloc<urq_keyboard_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_keyboard_widget_conf_init_inplace(ptr);

    auto keyboard = proto.widget_matrix_button();
    if (keyboard.has_style()) {
        ptr->style = malloc<urq_style_t>();
        if (ptr->style == nullptr) {
            return -1;
        }
        urq_style_init_inplace(ptr->style);
        common::set_style(ctx, ptr->style, keyboard.style());
    }

    ptr->type = (urq_keyboard_type_t)keyboard.layout();

    if (keyboard.buttons_size() > 0 &&
        ptr->type == URQ_KEYBOARD_LAYOUT_UNSPECIFIED) {
        ListBuilder<urq_keyboard_base_conf_t *> builder(
            urq_keyboard_base_conf_free_inplace);
        builder.set_capacity((size_t)keyboard.buttons_size());

        ptr->key_styles = malloc<urq_base_info_list_t>();
        if (ptr->key_styles == nullptr) {
            return -1;
        }
        urq_base_info_list_init_inplace(ptr->key_styles);
        ptr->ctrl = malloc<urq_btnmatrix_ctrl_list_t>();
        ptr->ctrl->keys = malloc<lv_btnmatrix_ctrl_t>(
            sizeof(lv_btnmatrix_ctrl_t) * (size_t)keyboard.buttons_size());
        ptr->ctrl->size = (size_t)keyboard.buttons_size();

        ptr->value = malloc<urq_str_list_t>();
        ptr->value->data =
            malloc<char *>(sizeof(char *) * (size_t)keyboard.buttons_size());
        ptr->value->size = (size_t)keyboard.buttons_size();

        ptr->show_map = urq_string_map_new();

        // TODO: 设置键盘
        for (int i = 0; i < keyboard.buttons_size(); i++) {
            const auto &button = keyboard.buttons(i);

            urq_keyboard_base_conf_t *conf = malloc<urq_keyboard_base_conf_t>();
            urq_keyboard_base_conf_init_inplace(conf);

            if (button.style_size() > 0) {
                conf->styles = malloc<urq_style_ptr_list_t>();
                urq_style_ptr_list_init_inplace(conf->styles);
                common::set_style_list(ctx, conf->styles, button.style());
            }

            if (button.graphic_size() > 0) {
                urq_graphic_ptr_list_t *graphics =
                    malloc<urq_graphic_ptr_list_t>();
                urq_graphic_ptr_list_init_inplace(graphics);
                common::set_graphic_list(ctx, conf->graphics, button.graphic());
            }

            // 默认都给1吗
            ptr->ctrl->keys[i] = (uint16_t)button.button_width_u16();

            char *tmp = NULL;
            char *value = NULL;
            // 显示
            // TODO
            if (button.button_functions_size() < 0)
                return -1;
            auto btn_func = button.button_functions(0);

            if (!btn_func.text().has_tag_id_u16()) {
                urq_strdup(btn_func.text().input().single_lang().c_str());
            } else {
                tmp = urq_strdup(ctx.get_string(
                    (urq_i18n_id_t)btn_func.text().tag_id_u16()));
            }
            // 值
            if (btn_func.has_button_key_code()) {
                if (btn_func.button_key_code() ==
                    znd::project::v1::ButtonKeyCode::BUTTON_KEY_CODE_END_LINE) {
                    value = urq_strdup(urq_keyboard_get_funccode_str(
                        (urq_keyboard_func_code_t)btn_func.button_key_code()));
                }
            } else if (btn_func.has_unicode()) {
                value = urq_strdup(btn_func.unicode().c_str());
            }

            if (tmp != NULL) {
                // 存储到map
                urq_string_map_add(ptr->show_map, tmp, value);
                ptr->value->data[i] = tmp;
            } else {
                ptr->value->data[i] = value;
            }

            builder.push(conf);
        }
        builder.build(ptr->key_styles->size, ptr->key_styles->keys);
    }
    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__IMAGE_HPP
