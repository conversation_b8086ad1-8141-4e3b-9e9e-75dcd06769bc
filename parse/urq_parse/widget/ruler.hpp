#pragma once

#include "urq/ruler/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/common/set_data.hpp"
#include "urq_parse/common/set_line.hpp"
#include "urq_parse/common/set_scale.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__RULER_HPP
#define URQ_PARSE__WIDGET__RULER_HPP

namespace urq::parse::widget {

/// @brief 刻度尺组件解析
static inline int set_ruler(
    Context &ctx, urq_ruler_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_ruler_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_ruler_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);

    auto &ruler = proto.widget_ruler();

    // 设置刻度尺类型
    ptr->type = (urq_ruler_type_t)ruler.ruler_type();

    // 解析刻度配置
    if (ruler.has_scale_config()) {
        common::set_scale(ctx, ptr->scale, ruler.scale_config());
    }

    // 解析主线配置
    if (ruler.has_main_line()) {
        common::set_line(ctx, ptr->main_line, ruler.main_line());
    }

    ptr->radius = (lv_coord_t)ruler.radius_u16();

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__RULER_HPP
