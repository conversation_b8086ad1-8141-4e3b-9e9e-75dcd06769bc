#pragma once

#include "urq_conf/widget/scale.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/common/set_data.hpp"
#include "urq_parse/common/set_line.hpp"
#include "urq_parse/common/set_scale.hpp"
#include "urq_parse/util.hpp"
#include "urq_parse/widget/arc.hpp"
#include "urq_parse/widget/scale_label.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__SCALE_HPP
#define URQ_PARSE__WIDGET__SCALE_HPP

namespace urq::parse::widget {

/// @brief 刻度尺组件解析
static inline int set_scale(
    Context &ctx, urq_scale_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_scale_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_scale_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);

    auto &scale = proto.widget_scale();

    if (scale.has_arc()) {
        ptr->type = URQ_SCALE_TYPE_ARC;
        set_arc_conf(ctx, ptr->arc, scale.arc());
    } else if (scale.has_line()) {
        ptr->type = URQ_SCALE_TYPE_LINE;
        common::set_line(ctx, ptr->line, scale.line());
        // 解析主线配置
        if (scale.points_size() > 1) {
            ptr->point = malloc<lv_point_t>((size_t)scale.points_size());
            for (int i = 0; i < scale.points_size(); i++) {
                ptr->point[i].x = (lv_coord_t)scale.points(i).x_i16();
                ptr->point[i].y = (lv_coord_t)scale.points(i).y_i16();
            }
            ptr->point_cnt = (uint8_t)scale.points_size();
        }
    } else {
        return -1;
    }
    // 解析刻度配置
    if (scale.has_scale_config()) {
        common::set_scale(ctx, ptr->scale, scale.scale_config());
    }

    if (scale.has_scale_label_config()) {
        set_scale_label(ctx, ptr->scale_label, scale.scale_label_config());
    }

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__RULER_HPP
