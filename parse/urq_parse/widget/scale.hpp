#pragma once

#include "urq/scale/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/common/set_data.hpp"
#include "urq_parse/common/set_line.hpp"
#include "urq_parse/common/set_scale.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__SCALE_HPP
#define URQ_PARSE__WIDGET__SCALE_HPP

namespace urq::parse::widget {

/// @brief 刻度尺组件解析
static inline int set_scale(
    Context &ctx, urq_scale_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_scale_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_scale_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);

    auto &scale = proto.widget_scale();

    // 设置刻度尺类型
    ptr->type = (urq_scale_type_t)scale.scale_type();

    // 解析刻度配置
    if (scale.has_scale_config()) {
        common::set_scale(ctx, ptr->scale, scale.scale_config());
    }

    // 解析主线配置
    if (scale.has_main_line()) {
        common::set_line(ctx, ptr->line, scale.main_line());
    }

    ptr->radius = (lv_coord_t)scale.radius_u16();

    if (scale.points_size() > 1) {
        ptr->point = malloc<lv_point_t>((size_t)scale.points_size());
        for (int i = 0; i < scale.points_size(); i++) {
            ptr->point[i].x = (lv_coord_t)scale.points(i).x_i16();
            ptr->point[i].y = (lv_coord_t)scale.points(i).y_i16();
        }
        ptr->point_cnt = (uint8_t)scale.points_size();
    }

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__RULER_HPP
