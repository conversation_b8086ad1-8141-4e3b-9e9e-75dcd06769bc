#pragma once

#include "urq_conf/widget/calendar.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_font.hpp"
#include "urq_parse/common/set_style.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__CALENDAR_HPP
#define URQ_PARSE__WIDGET__CALENDAR_HPP

namespace urq::parse::widget {

/// @brief 日历显示输入
static inline int set_calendar(
    Context &ctx, urq_calendar_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_calendar_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_calendar_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);

    auto &calendar = proto.widget_calendar();
    ptr->type = (urq_date_show_type_t)calendar.calendar_type();
    // if(calendar.has_font())
    //     ::urq::parse::common::set_font(ctx, ptr->font, calendar.font());
    if (calendar.has_week_bg_color())
        ::urq::parse::common::set_color(
            ctx, ptr->week_bg_color, calendar.week_bg_color());
    if (calendar.has_week_font_color())
        ::urq::parse::common::set_color(
            ctx, ptr->week_font_color, calendar.week_font_color());
    if (calendar.has_today_bg_color())
        ::urq::parse::common::set_color(
            ctx, ptr->today_bg_color, calendar.today_bg_color());
    if (calendar.has_today_font_color())
        ::urq::parse::common::set_color(
            ctx, ptr->today_font_color, calendar.today_font_color());
    if (calendar.has_highlight_color())
        ::urq::parse::common::set_color(
            ctx, ptr->highlight_color, calendar.highlight_color());
    if (calendar.has_highlight_font_color())
        ::urq::parse::common::set_color(
            ctx, ptr->highlight_font_color, calendar.highlight_font_color());

    if (calendar.highlight_date_config_size() > 0) {
        for (int i = 0; i < calendar.highlight_date_config_size(); i++) {
            auto &date = calendar.highlight_date_config(i);
            ptr->hightlight_date =
                (lv_calendar_date_t *)urq_malloc(sizeof(lv_calendar_date_t));
            ptr->hightlight_date->year = (uint16_t)date.year_u16();
            ptr->hightlight_date->month = (int8_t)date.month_u8();
            ptr->hightlight_date->day = (int8_t)date.day_u8();
        }
        ptr->hightlight_date_num =
            (uint16_t)calendar.highlight_date_config_size();
    }

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__IMAGE_HPP
