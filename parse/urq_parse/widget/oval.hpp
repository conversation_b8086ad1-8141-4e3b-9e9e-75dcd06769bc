#pragma once

#include "urq/arc/oval.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__OVAL_HPP
#define URQ_PARSE__WIDGET__OVAL_HPP

namespace urq::parse::widget {

static inline int set_oval(
    Context &ctx, urq_oval_t &ptr, const znd::project::v1::Arc &proto) noexcept
{
    urq_used(ctx);
    urq_oval_init_inplace(&ptr);

    if (proto.has_l_radius_i16()) {
        ptr.lr = (lv_coord_t)proto.l_radius_i16();
    }
    ptr.sr = (lv_coord_t)proto.s_radius_i16();
    ptr.end_angle = (lv_coord_t)proto.end_angle_i16();
    ptr.start_angle = (lv_coord_t)proto.start_angle_i16();

    return 0;
}

static inline int set_oval(
    Context &ctx, urq_oval_t *&ptr, const znd::project::v1::Arc &proto) noexcept
{
    ptr = malloc<urq_oval_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_oval_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);
    urq_used(proto);

    set_oval(ctx, *ptr, proto);

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__OVAL_HPP
