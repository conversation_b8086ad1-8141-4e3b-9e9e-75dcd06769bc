
#pragma once

#include "urq_conf/common/scale_label.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__SCALE_LABEL_HPP
#define URQ_PARSE__WIDGET__SCALE_LABEL_HPP

namespace urq::parse::widget {

static inline int set_scale_label(
    Context &ctx, urq_curve_scale_label_t &ptr,
    const znd::project::v1::ScaleLabelConfig &proto) noexcept
{
    urq_curve_scale_label_init_inplace(&ptr);

    ptr.show_type = (urq_label_show_type_t)proto.show_type();
    if (proto.has_offset_radius_u8()) {
        ptr.offset_radius = (lv_coord_t)proto.offset_radius_u8();
    }
    if (proto.has_offset_len_u8()) {
        ptr.offset_len = (lv_coord_t)proto.offset_len_u8();
    }
    if (proto.has_decimal_places_u8()) {
        ptr.decimal_places = (uint8_t)proto.decimal_places_u8();
    }
    if (proto.has_integer_places_u8()) {
        ptr.integer_places = (uint8_t)proto.integer_places_u8();
    }
    if (proto.has_color()) {
        common::set_color(ctx, ptr.color, proto.color());
    }

    return 0;
}

/// @brief 刻度尺组件解析
static inline int set_scale_label(
    Context &ctx, urq_curve_scale_label_t *&ptr,
    const znd::project::v1::ScaleLabelConfig &proto) noexcept
{
    ptr = malloc<urq_curve_scale_label_t>();
    if (ptr == nullptr) {
        return -1;
    }
    set_scale_label(ctx, *ptr, proto);
    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__RULER_HPP
