#pragma once

#include "urq/arc/round.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__ROUND_HPP
#define URQ_PARSE__WIDGET__ROUND_HPP

namespace urq::parse::widget {

static inline int set_round(
    Context &ctx, urq_round_t &ptr, const znd::project::v1::Arc &proto) noexcept
{
    urq_used(ctx);
    urq_round_init_inplace(&ptr);

    ptr.r = (lv_coord_t)proto.s_radius_i16();
    ptr.end_angle = (uint16_t)proto.end_angle_i16();
    ptr.start_angle = (uint16_t)proto.start_angle_i16();

    return 0;
}

static inline int set_round(
    Context &ctx, urq_round_t *&ptr,
    const znd::project::v1::Arc &proto) noexcept
{
    ptr = malloc<urq_round_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_round_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);
    urq_used(proto);

    set_round(ctx, *ptr, proto);

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__LINEAR_HPP
