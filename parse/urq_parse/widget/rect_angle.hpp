#pragma once

#include "urq_conf/widget/rectangle.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/common/set_line.hpp"
#include "urq_parse/common/set_scale.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__RECT_ANGLE_HPP
#define URQ_PARSE__WIDGET__RECT_ANGLE_HPP

namespace urq::parse::widget {

static inline int set_rect_angle(
    Context &ctx, urq_rect_angle_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_rect_angle_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_rect_angle_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);
    urq_used(proto);

    auto &rectangle = proto.widget_rect_angle();
    ptr->chamfer_type = (urq_chamfer_type_t)rectangle.chamfer_type();
    ptr->chamfer_radius = (uint8_t)rectangle.chamfer_radius_u8();
    common::set_line(ctx, ptr->line, rectangle.line());

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__RECT_ANGLE_HPP
