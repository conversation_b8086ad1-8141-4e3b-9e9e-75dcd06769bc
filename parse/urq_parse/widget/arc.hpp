#pragma once

#include "urq/arc/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/common/set_line.hpp"
#include "urq_parse/common/set_scale.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__ARC_HPP
#define URQ_PARSE__WIDGET__ARC_HPP

namespace urq::parse::widget {

static inline int set_arc(
    Context &ctx, urq_arc_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_arc_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_arc_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);
    urq_used(proto);

    auto &arc = proto.widget_arc();
    ptr->type = (urq_arc_type_t)arc.arc_type();
    ptr->center.x = (lv_coord_t)arc.center().x_i16();
    ptr->center.y = (lv_coord_t)arc.center().y_i16();
    ptr->radius = (uint16_t)arc.radius_u16();
    ptr->start_angle = (uint16_t)arc.start_angle_u16();
    ptr->end_angle = (uint16_t)arc.end_angle_u16();

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__LINEAR_HPP
