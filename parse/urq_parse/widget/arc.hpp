#pragma once

#include "urq_conf/transform/shape.h"
#include "urq_conf/widget/arc.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/common/set_line.hpp"
#include "urq_parse/common/set_point.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__ARC_HPP
#define URQ_PARSE__WIDGET__ARC_HPP

namespace urq::parse::widget {

static inline int set_arc_conf(
    Context &ctx, urq_arc_t &ptr, const znd::project::v1::Arc &proto) noexcept
{

    urq_arc_init_inplace(&ptr);

    ptr.start_angle = (lv_coord_t)proto.start_angle_i16();
    ptr.end_angle = (lv_coord_t)proto.end_angle_i16();
    ptr.sr = (lv_coord_t)proto.s_radius_i16();
    if (proto.has_l_radius_i16()) {
        ptr.lr = (lv_coord_t)proto.l_radius_i16();
    }
    if (proto.has_center()) {
        ptr.center.x = (lv_coord_t)proto.center().x_i16();
        ptr.center.y = (lv_coord_t)proto.center().y_i16();
    }
    if (proto.has_line()) {
        common::set_line(ctx, ptr.line, proto.line());
    }
    if (proto.has_point_config()) {
        common::set_point(ctx, ptr.point_config, proto.point_config());
    }

    return 0;
}

static inline int set_arc_conf(
    Context &ctx, urq_arc_t *&ptr, const znd::project::v1::Arc &proto) noexcept
{
    ptr = malloc<urq_arc_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_arc_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);
    urq_used(proto);
    set_arc_conf(ctx, *ptr, proto);

    return 0;
}
static inline int set_arc(
    Context &ctx, urq_arc_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_arc_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_arc_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);
    urq_used(proto);

    auto &arc = proto.widget_arc();
    set_arc_conf(ctx, ptr->arc, arc.arc());
    if (arc.has_inner_arc()) {
        set_arc_conf(ctx, ptr->inner_arc, arc.inner_arc());
    }
    ptr->type = (urq_arc_type_t)arc.arc_type();
    ptr->rotate_angle = (float)arc.rotate_angle_i16();

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__LINEAR_HPP
