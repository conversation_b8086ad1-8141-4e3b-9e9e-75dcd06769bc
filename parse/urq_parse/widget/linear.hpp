#pragma once

#include "urq_conf/widget/linear.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/common/set_data.hpp"
#include "urq_parse/common/set_line.hpp"
#include "urq_parse/common/set_scale.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__LINEAR_HPP
#define URQ_PARSE__WIDGET__LINEAR_HPP

namespace urq::parse::widget {

static inline int set_linear(
    Context &ctx, urq_linear_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_linear_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_linear_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);

    auto &linear = proto.widget_linear();
    ptr->type = (urq_linear_type_t)linear.linear_type();
    common::set_line(ctx, ptr->line, linear.line());
    if (linear.points_size() > 1) {
        ptr->points = malloc<lv_point_t>((size_t)linear.points_size());
        for (int32_t i = 0; i < linear.points_size(); i++) {
            ptr->points[i].x = (lv_coord_t)linear.points(i).x_i16();
            ptr->points[i].y = (lv_coord_t)linear.points(i).y_i16();
        }
        ptr->points_size = (uint16_t)linear.points_size();
    } else {
        return -1;
    }

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__LINEAR_HPP
