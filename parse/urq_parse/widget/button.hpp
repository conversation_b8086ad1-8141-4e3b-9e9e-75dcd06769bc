#pragma once

#include "urq_conf/widget/button.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_text.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__BUTTON_HPP
#define URQ_PARSE__WIDGET__BUTTON_HPP

namespace urq::parse::widget {

/// @brief 按钮输入
static inline int set_button(
    Context &ctx, urq_button_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_button_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_button_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);
    urq_used(proto);

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__BUTTON_HPP
