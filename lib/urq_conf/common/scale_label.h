#pragma once

#include "urq_conf/style/line.h"

#ifndef URQ__CURVE__SCALE_LABEL_H
#define URQ__CURVE__SCALE_LABEL_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 刻度显示类型
typedef enum {
    /// @brief 不显示
    URQ_CURVE_SCALE_SHOW_TYPE_NONE,
    /// @brief 显示默认
    URQ_CURVE_SCALE_SHOW_TYPE_DEFAULT,
    /// @brief 显示点数
    URQ_CURVE_SCALE_SHOW_TYPE_POINT,
    /// @brief 显示时间
    URQ_CURVE_SCALE_SHOW_TYPE_TIME,
} urq_label_show_type_t;

typedef struct {
    /// @brief 显示类型
    urq_label_show_type_t show_type;
    /// @brief 偏移半径
    lv_coord_t offset_radius;
    /// @brief 偏移长度
    lv_coord_t offset_len;
    /// @brief 小数位数
    uint8_t decimal_places;
    /// @brief 整数位数
    uint8_t integer_places;
    /// @brief 颜色
    urq_color_ref_t color;
} urq_curve_scale_label_t;

static inline void urq_curve_scale_label_init_inplace(
    urq_curve_scale_label_t *self) __attribute__((__nonnull__(1)));

static inline void urq_curve_scale_label_free_inplace(
    urq_curve_scale_label_t *self) __attribute__((__nonnull__(1)));

void urq_curve_scale_label_init_inplace(urq_curve_scale_label_t *self)
{
    self->show_type = URQ_CURVE_SCALE_SHOW_TYPE_NONE;
    self->offset_radius = 0;
    self->offset_len = 0;
    self->decimal_places = 0;
    self->integer_places = 0;
    urq_color_ref_init_inplace(&self->color);
}

void urq_curve_scale_label_free_inplace(urq_curve_scale_label_t *self)
{
    urq_used(self);
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef
