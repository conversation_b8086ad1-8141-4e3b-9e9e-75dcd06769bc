
#pragma once

#include "urq/preload.h"
#include "urq_conf/common/color.h"

#ifndef URQ__CONF__CURSOR_H
#define URQ__CONF__CURSOR_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 游标颜色
    urq_color_ref_t color;
    /// @brief 游标宽度
    uint8_t cursor_width;
    /// @brief 游标是否显示数据
    bool view_data;
} urq_curve_cursor_t;

static inline void urq_curve_cursor_init_inplace(urq_curve_cursor_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_curve_cursor_free_inplace(urq_curve_cursor_t *self)
    __attribute__((__nonnull__(1)));

void urq_curve_cursor_init_inplace(urq_curve_cursor_t *self)
{
    urq_color_ref_init_inplace(&self->color);
    self->view_data = false;
}

void urq_curve_cursor_free_inplace(urq_curve_cursor_t *self) { urq_used(self); }

#ifdef __cplusplus
}
#endif
#endif
