#pragma once

#include "urq_conf/style/line.h"
#include "urq_conf/style/point.h"

#ifndef URQ__ARC__ARC_H
#define URQ__ARC__ARC_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 弧形
typedef struct {
    /// @brief 起始角度
    lv_coord_t start_angle;
    /// @brief 结束角度
    lv_coord_t end_angle;
    /// @brief 短轴半径
    lv_coord_t sr;
    /// @brief 长轴半径
    lv_coord_t lr;
    /// @brief 中心点
    lv_point_t center;
    /// @brief 弧线配置
    urq_style_line_t *line;
    /// @brief 中心点配置
    urq_style_point_t *point_config;
} urq_arc_t;

static inline void urq_arc_init_inplace(urq_arc_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_arc_free_inplace(urq_arc_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================
void urq_arc_init_inplace(urq_arc_t *self)
{
    self->start_angle = 0;
    self->end_angle = 0;
    self->sr = 0;
    self->lr = 0;
    self->center.x = 0;
    self->center.y = 0;
    self->line = NULL;
    self->point_config = NULL;
}

void urq_arc_free_inplace(urq_arc_t *self)
{
    if (self->line != NULL) {
        urq_style_line_free_inplace(self->line);
        urq_free(self->line);
    }
    if (self->point_config != NULL) {
        urq_style_point_free_inplace(self->point_config);
        urq_free(self->point_config);
    }
}

#ifdef __cplusplus
}
#endif

#endif // URQ__ARC__ARC_H
