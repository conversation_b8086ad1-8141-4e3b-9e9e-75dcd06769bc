#pragma once

#include "lvgl.h"

#ifndef URQ__POSITION_H
#define URQ__POSITION_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    URQ_POSITION_TYPE_UNSPECIFIED = 0,
    URQ_POSITION_TYPE_LEFT_TOP = 1,
    URQ_POSITION_TYPE_CENTER_TOP = 2,
    URQ_POSITION_TYPE_RIGHT_TOP = 3,
    URQ_POSITION_TYPE_LEFT_CENTER = 4,
    URQ_POSITION_TYPE_CENTER_CENTER = 5,
    URQ_POSITION_TYPE_RIGHT_CENTER = 6,
    URQ_POSITION_TYPE_LEFT_BOTTOM = 7,
    URQ_POSITION_TYPE_CENTER_BOTTOM = 8,
    URQ_POSITION_TYPE_RIGHT_BOTTOM = 9
} urq_position_type_t;

typedef struct {
    lv_coord_t x;
    lv_coord_t y;
} urq_position_t;

#ifdef __cplusplus
}
#endif
#endif // URQ__POSITION_H
