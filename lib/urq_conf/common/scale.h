
#pragma once

#include "urq/preload.h"
#include "urq_conf/common/color.h"
#include "urq_conf/common/time.h"
#include "urq_conf/style/line.h"

#ifndef URQ__CURVE__SCALE_H
#define URQ__CURVE__SCALE_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 格线
    urq_style_line_t *line;
    /// @brief 主刻度颜色
    urq_color_ref_t main_color;
    /// @brief 次刻度颜色
    urq_color_ref_t sub_color;
    /// @brief 最大值
    lv_coord_t max_value;
    /// @brief 最小值
    lv_coord_t min_value;
    // @brief 各线距离
    lv_coord_t grid_spacing;
    /// @brief 格线数量
    uint8_t grid_line_cnt;

    /// @brief 主刻度等分
    uint8_t main_tick_count;
    /// @brief 次刻度等分
    uint8_t sub_tick_count;

    /// @brief 主刻度长度
    uint8_t main_tick_len;
    /// @brief 次刻度长度
    uint8_t sub_tick_len;

    /// @brief 主刻度宽度
    uint8_t main_tick_width;
    /// @brief 次刻度宽度
    uint8_t sub_tick_width;
    /// @brief 绘画刻度
    uint8_t draw_tick_len;
} urq_scale_t;

static inline void urq_curve_scale_init_inplace(urq_scale_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_curve_scale_free_inplace(urq_scale_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_curve_scale_printf(urq_scale_t *self)
    __attribute__((__nonnull__(1)));

void urq_curve_scale_init_inplace(urq_scale_t *self)
{
    self->max_value = -1;
    self->min_value = -1;
    urq_color_ref_init_inplace(&self->main_color);
    urq_color_ref_init_inplace(&self->sub_color);
    self->main_tick_len = 0;
    self->sub_tick_len = 0;
    self->main_tick_width = 0;
    self->sub_tick_width = 0;
    self->main_tick_count = 0;
    self->sub_tick_count = 0;
    self->draw_tick_len = 50;
    self->grid_spacing = 0;
    self->line = NULL;
}

void urq_curve_scale_free_inplace(urq_scale_t *self)
{
    if (self->line != NULL) {
        urq_style_line_free_inplace(self->line);
        urq_free(self->line);
    }
}

void urq_curve_scale_printf(urq_scale_t *self)
{
    printf(
        "[urq_curve_scale]: [main_tick_len: %d, sub_tick_len: %d, "
        "main_tick_width: %d, sub_tick_width: %d, main_tick_count: %d, "
        "sub_tick_count: %d, draw_tick_len: %d, grid_spacing: %d, "
        "\n",
        self->main_tick_len, self->sub_tick_len, self->main_tick_width,
        self->sub_tick_width, self->main_tick_count, self->sub_tick_count,
        self->draw_tick_len, self->grid_spacing);
}

#ifdef __cplusplus
}
#endif
#endif
