#pragma once
#include "urq/preload.h"

/// @brief 圆形
typedef struct {
    /// @brief 起始角度
    uint16_t start_angle;
    /// @brief 结束角度
    uint16_t end_angle;
    /// @brief 半径
    int16_t r;
} urq_round_t;

static inline void urq_round_init_inplace(urq_round_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_round_free_inplace(urq_round_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================
void urq_round_init_inplace(urq_round_t *self)
{
    self->start_angle = 0;
    self->end_angle = 0;
    self->r = 0;
}

void urq_round_free_inplace(urq_round_t *self) { urq_used(self); }
