#pragma once

#include "lvgl.h"
#include "urq/preload.h"
#include <stdint.h>

#ifndef URQ__MARGIN_H
#define URQ__MARGIN_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 位置边距
typedef struct {
    lv_coord_t left;
    lv_coord_t top;
    lv_coord_t right;
    lv_coord_t bottom;
} urq_margin_t;

static inline void urq_margin_init_inplace(urq_margin_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_margin_free_inplace(urq_margin_t *self)
    __attribute__((__nonnull__(1)));

// impl
void urq_margin_init_inplace(urq_margin_t *self)
{
    self->left = 0;
    self->top = 0;
    self->right = 0;
    self->bottom = 0;
}

void urq_margin_free_inplace(urq_margin_t *self) { urq_used(self); }

#ifdef __cplusplus
}
#endif

#endif // URQ__MARGIN_H
