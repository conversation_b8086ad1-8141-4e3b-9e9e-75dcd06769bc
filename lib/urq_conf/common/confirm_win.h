#pragma once

#include "urq/preload.h"
#include <stdbool.h>

#ifndef URQ__CONFIRM__WIN_H
#define URQ__CONFIRM__WIN_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    uint16_t id;
    uint16_t wait_time;
    bool timeout_run;
} urq_confirm_win_t;

static inline void urq_confirm_win_init_inplace(urq_confirm_win_t *self)
{
    self->id = 0;
    self->wait_time = 0;
    self->timeout_run = false;
}

static inline void urq_confirm_win_free_inplace(urq_confirm_win_t *self)
{
    urq_used(self);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__CONTROL__WIN_H
