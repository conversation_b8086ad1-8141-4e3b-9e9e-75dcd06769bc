#pragma once

#include "lvgl.h"
#include "urq/compile.h"
#include <stdint.h>

#ifndef URQ_CONF__COMMON__COLOR_H
#define URQ_CONF__COMMON__COLOR_H
#ifdef __cplusplus
extern "C" {
#endif

struct _urq_color_argb_value {
    uint8_t b;
    uint8_t g;
    uint8_t r;
    uint8_t a;
};

/// @brief #AARRGGBB
typedef union {
    uint32_t argb;
#if URQ_COMPILE_ENDIAN == URQ_COMPILE_ENDIAN_LITTLE
    struct _urq_color_argb_value ch;
#else
    struct {
        uint8_t a;
        uint8_t r;
        uint8_t g;
        uint8_t b;
    };
#endif
} urq_color_argb_t;

typedef union {
    uint32_t rgba;
#if URQ_COMPILE_ENDIAN == URQ_COMPILE_ENDIAN_LITTLE
    struct {
        uint8_t a;
        uint8_t b;
        uint8_t g;
        uint8_t r;
    } ch;
#else
    struct _urq_color_argb_value ch;
#endif
} urq_color_rgba_t;

/// @brief Reference to a color
typedef struct {
    /// @brief 颜色
    urq_color_rgba_t rgba;
    /// @brief 0为固定值
    int8_t id;
} urq_color_ref_t;

static inline void urq_color_ref_init_inplace(urq_color_ref_t *self)
{
    self->id = -1;
    self->rgba.rgba = 0x000000FF;
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__COMMON__COLOR_H
