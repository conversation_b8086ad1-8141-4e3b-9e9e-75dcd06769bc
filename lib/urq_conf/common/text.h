#pragma once

#ifndef URQ__TEXT__TEXT_H
#define URQ__TEXT__TEXT_H

#include "urq_conf/style/font.h"
#include <stdbool.h>
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 文本
typedef struct {
    /// @brief 标签ID
    int16_t tag_id;
    /// @brief 字体
    // urq_font_t *font;
    /// @brief 文本
    const char *content;
} urq_text_t;

static inline void urq_text_init_inplace(urq_text_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_text_free_inplace(urq_text_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_text_init_inplace(urq_text_t *self)
{
    self->tag_id = -1;
    // self->font = NULL;
    self->content = NULL;
}

void urq_text_free_inplace(urq_text_t *self)
{
    // if (self->font != NULL) {
    //     urq_font_free_inplace(self->font);
    //     urq_free(self->font);
    // }
    urq_used(self);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__TEXT__TEXT_H
