#pragma once

#include "urq/preload.h"
#include "urq_conf/common/color.h"

#ifndef URQ__GRAPHIC__GRAPHIC_H
#define URQ__GRAPHIC__GRAPHIC_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    URQ_GRAPHIC_TYPE_UNSPECIFIED = 0,
    URQ_GRAPHIC_TYPE_IMAGE = 1,
    URQ_GRAPHIC_TYPE_ICON_FONT_AWESOME = 2,
} urq_graphic_type_t;

/// @brief 图片
typedef struct {
    /// @brief [optional]图片ID
    uint32_t src;
    /// @brief [optional]重新着色
    urq_color_ref_t color;
    /// @brief [optional]图片透明度
    lv_opa_t opacity;
    /// @brief [optional]图片类型
    urq_graphic_type_t graphic_type;
} urq_graphic_t;

static inline void urq_graphic_init_inplace(urq_graphic_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_graphic_free_inplace(urq_graphic_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_graphic_init_inplace(urq_graphic_t *self)
{
    self->opacity = 255;
    self->src = 0;
    self->graphic_type = URQ_GRAPHIC_TYPE_UNSPECIFIED;
    urq_color_ref_init_inplace(&self->color);
}

void urq_graphic_free_inplace(urq_graphic_t *self) { urq_used(self); }

#ifdef __cplusplus
}
#endif

#endif // URQ__GRAPHIC__GRAPHIC_H
