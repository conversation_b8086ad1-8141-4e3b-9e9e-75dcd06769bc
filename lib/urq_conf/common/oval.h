
#pragma once
#include "urq/preload.h"
#include "urq_conf/arc/round.h"

/// @brief 椭圆
typedef struct {
    /// @brief 起始角度
    float start_angle;
    /// @brief 结束角度
    float end_angle;
    /// @brief 短轴半径
    int16_t sr;
    /// @brief 长轴半径
    int16_t lr;
} urq_oval_t;

static inline void urq_oval_init_inplace(urq_oval_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_oval_free_inplace(urq_oval_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================
void urq_oval_init_inplace(urq_oval_t *self)
{
    self->start_angle = 0;
    self->end_angle = 0;
    self->sr = 0;
    self->lr = 0;
}

void urq_oval_free_inplace(urq_oval_t *self) { urq_used(self); }
