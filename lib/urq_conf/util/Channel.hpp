#pragma once

#include "urq_conf/log/verbose.hpp"
#include <condition_variable>
#include <mutex>
#include <queue>

#ifndef URQ_CONF__UTIL__CHANNEL_HPP
#define URQ_CONF__UTIL__CHANNEL_HPP

namespace urq::lib {

template <typename T> class Channel {
    static_assert(
        std::is_move_constructible<T>::value, "T must be move constructible");

    std::mutex m_mutex;
    std::condition_variable m_cv;
    std::queue<T> m_queue;

public:
    /// @brief 发送消息
    inline void send(T &&msg) noexcept
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_queue.push(std::move(msg));
        m_cv.notify_one();
    }

    /// @brief 接收消息
    inline T recv() noexcept
    {
        std::unique_lock<std::mutex> lock(m_mutex);
        m_cv.wait(lock, [this]() {
            log_v("recv, queue size: ", m_queue.size(), log_endl);
            return !m_queue.empty();
        });
        T msg = std::move(m_queue.front());
        m_queue.pop();
        return msg;
    }

    /// @brief 取出最早的内容
    template <typename R = T>
    std::enable_if_t<std::is_default_constructible<R>::value, R>
    front() noexcept
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (m_queue.empty()) {
            return R();
        }
        R value = std::move(m_queue.front());
        m_queue.pop();
        return value;
    }
};

} // namespace urq::lib

#endif // URQ_CONF__UTIL__CHANNEL_HPPCHANNEL_H
