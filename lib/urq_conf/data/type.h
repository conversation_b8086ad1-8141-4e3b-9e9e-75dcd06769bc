#pragma once

#include "urq/preload.h"
#include <stdbool.h>
#include <stdio.h>

#ifndef URQ_CONF__DATA__TYPE_H
#define URQ_CONF__DATA__TYPE_H

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    DATA_TYPE_UNSPECIFIED = 0,
    DATA_TYPE_BOOL = 1,
    DATA_TYPE_UINT8 = 2,
    DATA_TYPE_INT8 = 3,
    DATA_TYPE_UINT16 = 4,
    DATA_TYPE_INT16 = 5,
    DATA_TYPE_UINT32 = 6,
    DATA_TYPE_INT32 = 7,
    DATA_TYPE_FLOAT = 8,
    DATA_TYPE_DOUBLE = 9,
    DATA_TYPE_STRING = 10,
} urq_data_type_t;

typedef struct {
    union {
        bool bool_value;
        uint8_t uint8_value;
        int8_t int8_value;
        uint16_t uint16_value;
        int16_t int16_value;
        uint32_t uint32_value;
        int32_t int32_value;
        float float_value;
        double double_value;
        const char *string_value;
    };
    urq_data_type_t data_format;
} urq_data_t;

static inline int urq_data_init_inplace(urq_data_t *self)
{
    urq_used(self);
    self->data_format = DATA_TYPE_UNSPECIFIED;
    return 0;
}

static inline int urq_data_free_inplace(urq_data_t *self)
{
    if (self->data_format == DATA_TYPE_STRING) {
        if (self->string_value != NULL) {
            urq_free((void *)self->string_value);
        }
    }
    return 0;
}

static inline double urq_data_to_double(void *data, urq_data_type_t type)
{
    switch (type) {
    case DATA_TYPE_UNSPECIFIED:
        return (*(bool *)data) ? 1.0 : 0.0;
    case DATA_TYPE_UINT8:
        return (double)(*(uint8_t *)data);
    case DATA_TYPE_INT8:
        return (double)(*(int8_t *)data);
    case DATA_TYPE_UINT16:
        return (double)(*(uint16_t *)data);
    case DATA_TYPE_INT16:
        return (double)(*(int16_t *)data);
    case DATA_TYPE_UINT32:
        return (double)(*(uint32_t *)data);
    case DATA_TYPE_INT32:
        return (double)(*(int32_t *)data);
    case DATA_TYPE_FLOAT:
        return (double)(*(float *)data);
    case DATA_TYPE_DOUBLE:
        return *(double *)data;
    case DATA_TYPE_STRING:
    default:
        return 0.0;
    }
}

static inline int urq_data_to_int(void *data, urq_data_type_t type)
{
    switch (type) {
    case DATA_TYPE_UNSPECIFIED:
        return (*(bool *)data) ? 1 : 0;
    case DATA_TYPE_UINT8:
        return (int)(*(uint8_t *)data);
    case DATA_TYPE_INT8:
        return (int)(*(int8_t *)data);
    case DATA_TYPE_UINT16:
        return (int)(*(uint16_t *)data);
    case DATA_TYPE_INT16:
        return (int)(*(int16_t *)data);
    case DATA_TYPE_UINT32:
        return (int)(*(uint32_t *)data);
    case DATA_TYPE_INT32:
        return (int)(*(int32_t *)data);
    case DATA_TYPE_FLOAT:
    case DATA_TYPE_DOUBLE:
    case DATA_TYPE_STRING:
    default:
        return 0;
    }
}

static inline void urq_data_to_string(
    void *data, urq_data_type_t type, char *str)
{
    if (type == DATA_TYPE_STRING) {
        strcpy(str, (const char *)data);
    } else if (type > DATA_TYPE_INT32) {
        sprintf(str, "%f", urq_data_to_double(data, type));
    } else {
        sprintf(str, "%d", urq_data_to_int(data, type));
    }
}

#ifdef __cplusplus
}
#endif

#endif // URQ_CONF__DATA__TYPE_H
