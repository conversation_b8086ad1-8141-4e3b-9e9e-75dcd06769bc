#include "circular_link.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

urq_clink_t *urq_clink_create(void)
{
    urq_clink_t *link = (urq_clink_t *)malloc(sizeof(urq_clink_t));
    if (link == NULL)
        return NULL;
    link->head = link->tail = NULL;
    link->size = 0;
    return link;
}

void urq_clink_append(urq_clink_t *link, int v)
{
    if (link == NULL)
        return;
    Node *node = (Node *)malloc(sizeof(Node));
    node->value = v;
    node->next = node->prev = NULL;

    if (link->size == 0) {
        link->head = link->tail = node;
        link->size++;
    } else if (link->size < CLINK_MAX_SIZE) {
        node->prev = link->tail;
        link->tail->next = node;
        link->tail = node;
        link->size++;
    } else {
        // deal head
        Node *tmp = link->head;
        link->head = tmp->next;
        if (link->head) {
            link->head->prev = NULL;
        }
        free(tmp);

        node->prev = link->tail;
        link->tail->next = node;
        link->tail = node;

        if (link->head == NULL) {
            link->head = node;
        }
    }
}

void urq_clink_clean(urq_clink_t *link)
{
    Node *node = link->head;
    while (node) {
        Node *next = node->next;
        free(node);
        node = next;
    }
    free(link);
}

int urq_clink_head_value(urq_clink_t *link)
{
    if (link == NULL)
        return -1;

    if (link->tail != NULL) {
        return link->head->value;
    }
    return -1;
}

int urq_clink_back_value(urq_clink_t *link)
{
    if (link == NULL)
        return -1;
    if (link->tail != NULL) {
        return link->tail->value;
    }
    return -1;
}

int urq_clink_front_value(urq_clink_t *link)
{
    if (link == NULL)
        return -1;
    if (link->tail != NULL) {
        return link->tail->prev->value;
    }
    return -1;
}

void urq_clink_pop_back(urq_clink_t *link)
{
    if (link == NULL || link->tail == NULL)
        return;
    Node *tmp = link->tail;
    if (link->head == link->tail) {
        link->head = link->tail = NULL;
    } else {
        link->tail = tmp->prev;
        link->tail->next = NULL;
    }

    free(tmp);
    link->size--;
}

void urq_clink_pop_front(urq_clink_t *link)
{
    if (link == NULL || link->head == NULL)
        return;

    Node *tmp = link->head;
    link->head = tmp->next;
    if (link->head) {
        link->head->prev = NULL;
    } else {
        link->tail = NULL;
    }
    free(tmp);
    link->size--;
}

void urq_clink_to_string(urq_clink_t *link)
{
    if (link == NULL)
        return;
    printf("urq_clink: ");
    Node *tmp = link->head;
    for (size_t idx = 0; idx < link->size; ++idx) {
        printf("urq_clink: %d", tmp->value);
        tmp = tmp->next;
    }
    printf("\n");
}

int urq_clink_contains(urq_clink_t *link, int id)
{
    if (link == NULL)
        return 0;
    Node *tmp = link->head;
    for (size_t idx = 0; idx < link->size; ++idx) {
        if (tmp->value == id) {
            return 1;
        }
        tmp = tmp->next;
    }
    return 0;
}
