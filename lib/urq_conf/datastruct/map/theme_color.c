#include "urq_conf/datastruct/map/theme_color.h"
#include "urq/errno.h"
#include <errno.h>

urq_theme_color_t *urq_theme_color_new(void)
{
    return kh_init_urq_theme_color();
}

void urq_theme_color_free(urq_theme_color_t *self)
{
    kh_destroy(urq_theme_color, self);
}

void urq_theme_color_resize(urq_theme_color_t *self, uint32_t size)
{
    kh_resize(urq_theme_color, self, size);
}

int urq_theme_color_add(
    urq_theme_color_t *const self, uint16_t id, const urq_color_rgba_t color)
{
    int ret = 0;
    khiter_t k = kh_put_urq_theme_color(self, (khint32_t)id, &ret);
    if (ret == 1) {
        // 初始化数据
        kh_val(self, k) = color;
        return 0;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        case 0:
            errno = URQ_EEXISTS;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }
}

urq_color_rgba_t *urq_theme_color_get(
    const urq_theme_color_t *self, uint32_t id)
{
    khiter_t k = kh_get_urq_theme_color(self, (khint32_t)id);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return NULL;
    }
    return &kh_val(self, k);
}
