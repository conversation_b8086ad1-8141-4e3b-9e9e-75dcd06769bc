#include "urq_conf/datastruct/map/font.h"
#include "urq/errno.h"
#include <errno.h>
#include <stdio.h>

urq_font_map_t *urq_font_map_new(void) { return kh_init_urq_font_map(); }

void urq_font_map_free(urq_font_map_t *self) { kh_destroy(urq_font_map, self); }

void urq_font_map_resize(urq_font_map_t *self, uint32_t size)
{
    kh_resize(urq_font_map, self, size);
}

int urq_font_map_add(urq_font_map_t *const self, int32_t id, uint8_t font_id)
{
    int ret = 0;
    khiter_t k = kh_put_urq_font_map(self, (khint32_t)id, &ret);
    if (ret == 1) {
        // 初始化数据
        kh_val(self, k) = font_id;
        return 0;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        case 0:
            errno = URQ_EEXISTS;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }
}

int8_t urq_font_map_get(const urq_font_map_t *self, uint32_t id)
{

    khiter_t k = kh_get_urq_font_map(self, (khint32_t)id);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return -1;
    }
    return (int8_t)kh_val(self, k);
}
