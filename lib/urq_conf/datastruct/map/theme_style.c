#include "urq_conf/datastruct/map/theme_style.h"
#include "urq/errno.h"
#include <errno.h>
#include <stdio.h>

urq_theme_style_t *urq_theme_style_new(void)
{
    return kh_init_urq_theme_style();
}

void urq_theme_style_free(urq_theme_style_t *self)
{
    for (khiter_t k = kh_begin(self); k != kh_end(self); ++k) {
        if (kh_exist(self, k)) {
            urq_style_t *style = kh_val(self, k); // 释放 value 指针
            urq_style_free_inplace(style);
            urq_free(style); // 释放 value 指针
        }
    }
    kh_destroy(urq_theme_style, self);
}

void urq_theme_style_resize(urq_theme_style_t *self, uint32_t size)
{
    kh_resize(urq_theme_style, self, size);
}

int urq_theme_style_add(
    urq_theme_style_t *const self, int id, const urq_style_t *const style)
{
    int ret = 0;
    khiter_t k = kh_put_urq_theme_style(self, (khint32_t)id, &ret);
    if (ret == 1) {
        // 初始化数据
        kh_val(self, k) = (urq_style_t *)style;
        return 0;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        case 0:
            errno = URQ_EEXISTS;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }
}

urq_style_t *urq_theme_style_get(const urq_theme_style_t *self, uint32_t id)
{

    khiter_t k = kh_get_urq_theme_style(self, (khint32_t)id);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return NULL;
    }
    return kh_val(self, k);
}
