#pragma once

#include "klib/khash.h"
#include "urq_conf/common/color.h"
#include <stdint.h>

#ifndef URQ_CONF__DATASTRUCT__MAP__THEME_COLOR_H
#define URQ_CONF__DATASTRUCT__MAP__THEME_COLOR_H
#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

/// @brief 主题颜色表
KHASH_MAP_INIT_INT(urq_theme_color, urq_color_rgba_t)
#pragma GCC diagnostic pop // 恢复之前的警告状态

/// @brief 主题颜色表
typedef khash_t(urq_theme_color) urq_theme_color_t;

/// @brief 创建新的主题颜色表
///
/// @return 新的主题颜色表
urq_theme_color_t *urq_theme_color_new(void)
    __attribute__((__warn_unused_result__()));

/// @brief 释放主题颜色表
///
/// @param self 主题颜色表
/// @return void
void urq_theme_color_free(urq_theme_color_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 调整大小
///
/// @param self 主题颜色表
/// @param size 大小
/// @return void
void urq_theme_color_resize(urq_theme_color_t *self, uint32_t size)
    __attribute__((__nonnull__(1)));

/// @brief 添加一个主题颜色
///
/// @param self     主题颜色表
/// @return 是否添加成功
int urq_theme_color_add(
    urq_theme_color_t *const self, uint16_t id, const urq_color_rgba_t color)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 获取主题颜色
///
/// @param self      主题颜色表
/// @param id        主题颜色ID
/// @param out_value 主题颜色值
/// @return 是否获取成功
urq_color_rgba_t *urq_theme_color_get(
    const urq_theme_color_t *self, uint32_t id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

#ifdef __cplusplus
}
#endif

#endif // URQ_CONF__DATASTRUCT__MAP__THEME_COLOR_H
