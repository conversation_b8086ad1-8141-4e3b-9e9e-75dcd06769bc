
#pragma once

#include "klib/khash.h"

#ifndef URQ_CONF__DATASTRUCT__MAP__STRING_H
#define URQ_CONF__DATASTRUCT__MAP__STRING_H
#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

KHASH_MAP_INIT_STR(urq_string_map, char *)
#pragma GCC diagnostic pop // 恢复之前的警告状态

typedef khash_t(urq_string_map) urq_string_map_t;

/// @brief 创建 string map
/// @returns string map
static inline urq_string_map_t *urq_string_map_new(void)
    __attribute__((__warn_unused_result__(), __malloc__()));

/// @brief 释放 string map
/// @details 这个方法不会销毁 string，因为通常来说 string 是由父组件管理的
/// @param self string map
/// @returns void
static inline void urq_string_map_free(urq_string_map_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 添加 string
/// @param self string map
/// @param string string
/// @returns 是否成功
static inline int urq_string_map_add(
    urq_string_map_t *self, char *key, char *value)
    __attribute__((__nonnull__(1, 2)));

/// @brief 获取 string
/// @param self string map
/// @param string string
/// @returns string
static inline char *urq_string_map_get(
    urq_string_map_t *self, const char *string)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

// impl

static inline urq_string_map_t *urq_string_map_new(void)
{
    return kh_init_urq_string_map();
}

static inline void urq_string_map_free(urq_string_map_t *self)
{
    kh_destroy_urq_string_map(self);
}

static inline int urq_string_map_add(
    urq_string_map_t *self, char *key, char *value)
{
    int ret;
    khiter_t k = kh_put_urq_string_map(self, key, &ret);
    if (ret < 0) {
        return -1;
    }
    kh_val(self, k) = value;
    return 0;
}

static inline char *urq_string_map_get(
    urq_string_map_t *self, const char *string)
{
    khiter_t k = kh_get_urq_string_map(self, string);
    if (k == kh_end(self)) {
        return NULL;
    }
    return kh_val(self, k);
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__MAP__STRING_MAP_H
