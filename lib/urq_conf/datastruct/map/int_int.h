#pragma once

#include "klib/khash.h"
#include <stdbool.h>
#include <stdint.h>

#ifndef URQ_CONF__DATASTRUCT__MAP__INT_INT_H
#define URQ_CONF__DATASTRUCT__MAP__INT_INT_H

#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

/// @brief lvgl 表
KHASH_MAP_INIT_INT(urq_int_int_map, int)
#pragma GCC diagnostic pop // 恢复之前的警告状态

/// @brief lvgl 表
typedef khash_t(urq_int_int_map) urq_int_int_map_t;

/// @brief 创建新的组件表
///
/// @return 新的组件表
urq_int_int_map_t *urq_int_int_map_new(void)
    __attribute__((__warn_unused_result__()));

/// @brief 释放组件表
///
/// @param self 组件表
/// @return void
void urq_int_int_map_free(urq_int_int_map_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 调整大小
///
/// @param self 组件表
/// @param size 大小
/// @return void
void urq_int_int_map_resize(urq_int_int_map_t *self, uint32_t size)
    __attribute__((__nonnull__(1)));

/// @brief 添加一个 int 组件
///
/// @return 是否添加成功
int urq_int_int_map_add(
    urq_int_int_map_t *const self, int32_t key, int32_t value)
    __attribute__((__nonnull__(1)));

/// @brief 删除一个 int 组件
///
/// @param self 多语言表
/// @param key   多语言ID
/// @return 是否删除成功
void urq_int_int_map_del(urq_int_int_map_t *const self, int key)
    __attribute__((__nonnull__(1)));

/// @brief 获取 int 组件
///
/// @param self      多语言表
/// @param key        组件ID
/// @param out_value 组件
/// @return 是否获取成功
int urq_int_int_map_get(const urq_int_int_map_t *self, int key, int *out_value)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 判断 int 组件是否存在
///
/// @param self 多语言表
/// @param key   组件ID
/// @return 是否存在
bool urq_int_int_map_has(const urq_int_int_map_t *self, int key)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 获取 int 组件数量
///
/// @param self 多语言表
/// @return 组件数量
uint32_t urq_int_int_map_size(const urq_int_int_map_t *self)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

#ifdef __cplusplus
}
#endif

#endif // URQ__UTIL__MAP__INT_INT_H
