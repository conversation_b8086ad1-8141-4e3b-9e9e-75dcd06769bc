
#include "urq_conf/datastruct/map/int_int.h"
#include "urq/errno.h"

urq_int_int_map_t *urq_int_int_map_new(void)
{
    return kh_init_urq_int_int_map();
}

void urq_int_int_map_free(urq_int_int_map_t *self)
{
    kh_destroy_urq_int_int_map(self);
}

void urq_int_int_map_resize(urq_int_int_map_t *self, uint32_t size)
{
    kh_resize_urq_int_int_map(self, size);
}

int urq_int_int_map_add(urq_int_int_map_t *const self, int32_t key, int value)
{
    int ret = 0;
    khiter_t k = kh_put_urq_int_int_map(self, (khint32_t)key, &ret);
    if (ret == 1) {
        // 初始化数据
        kh_val(self, k) = value;
        return 0;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        case 0:
            errno = URQ_EEXISTS;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }
}

void urq_int_int_map_del(urq_int_int_map_t *const self, int key)
{
    khiter_t k = kh_get_urq_int_int_map(self, (khint32_t)key);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
    }
    kh_del_urq_int_int_map(self, k);
}

int urq_int_int_map_get(const urq_int_int_map_t *self, int key, int *out_value)
{
    khiter_t k = kh_get_urq_int_int_map(self, (khint32_t)key);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return -1;
    }
    *out_value = kh_val(self, k);
    return 0;
}

bool urq_int_int_map_has(const urq_int_int_map_t *self, int key)
{
    khiter_t k = kh_get_urq_int_int_map(self, (khint32_t)key);
    return k != kh_end(self);
}

uint32_t urq_int_int_map_size(const urq_int_int_map_t *self)
{
    return kh_size(self);
}
