
#include "urq_conf/datastruct/map/lvgl.h"
#include "urq/errno.h"

urq_lvgl_map_t *urq_lvgl_map_new(void) { return kh_init_urq_lvgl_map(); }

void urq_lvgl_map_free(urq_lvgl_map_t *self) { kh_destroy_urq_lvgl_map(self); }

void urq_lvgl_map_resize(urq_lvgl_map_t *self, uint32_t size)
{
    kh_resize_urq_lvgl_map(self, size);
}

int urq_lvgl_map_add(urq_lvgl_map_t *const self, uint32_t id, lv_obj_t *widget)
{
    int ret = 0;
    khiter_t k = kh_put_urq_lvgl_map(self, (khint32_t)id, &ret);
    if (ret == 1) {
        // 初始化数据
        kh_val(self, k) = widget;
        return 0;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        case 0:
            errno = URQ_EEXISTS;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }
}

int urq_lvgl_map_del(urq_lvgl_map_t *const self, uint32_t id)
{
    khiter_t k = kh_get_urq_lvgl_map(self, (khint32_t)id);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return -1;
    }
    kh_del_urq_lvgl_map(self, k);
    return 0;
}

lv_obj_t *urq_lvgl_map_get(const urq_lvgl_map_t *self, uint32_t id)
{
    khiter_t k = kh_get_urq_lvgl_map(self, (khint32_t)id);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return NULL;
    }
    return kh_val(self, k);
}

bool urq_lvgl_map_has(const urq_lvgl_map_t *self, uint32_t id)
{
    khiter_t k = kh_get_urq_lvgl_map(self, (khint32_t)id);
    return k != kh_end(self);
}

uint32_t urq_lvgl_map_size(const urq_lvgl_map_t *self) { return kh_size(self); }
