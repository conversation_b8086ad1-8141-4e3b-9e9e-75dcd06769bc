
#pragma once

#include "klib/khash.h"
#include "lvgl.h"
#include "urq_conf/widget/keyboard/info.h"
#include <stdint.h>

#ifndef URQ_CONF__DATASTRUCT__MAP__KEYBOARD_INFO_H
#define URQ_CONF__DATASTRUCT__MAP__KEYBOARD_INFO_H
#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

/// @brief 多语言表
/// 页面ID 图元ID 键盘信息
KHASH_MAP_INIT_INT(urq_keyboard_map, urq_keyboard_info_t *)
#pragma GCC diagnostic pop // 恢复之前的警告状态

/// @brief 多语言表
typedef khash_t(urq_keyboard_map) urq_keyboard_map_t;

/// @brief 创建新的组件表
///
/// @return 新的组件表
urq_keyboard_map_t *urq_keyboard_map_new(void)
    __attribute__((__warn_unused_result__()));

/// @brief 释放组件表
///
/// @param self 组件表
/// @return void
void urq_keyboard_map_free(urq_keyboard_map_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 调整大小
///
/// @param self 组件表
/// @param size 大小
/// @return void
void urq_keyboard_map_resize(urq_keyboard_map_t *self, uint32_t size)
    __attribute__((__nonnull__(1)));

/// @brief 添加一个多语言
///
/// @param self 多语言表
/// @param id   多语言ID
/// @param text 多语言文本
/// @return 是否添加成功
int urq_keyboard_map_add(
    urq_keyboard_map_t *const self, uint32_t id, urq_keyboard_info_t *info)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 获取多语言
///
/// @param self      多语言表
/// @param id        组件ID
/// @param out_value 组件
/// @return 是否获取成功
urq_keyboard_info_t *urq_keyboard_map_get(
    const urq_keyboard_map_t *self, uint32_t id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

#ifdef __cplusplus
}
#endif

#endif // URQ_CONF__DATASTRUCT__MAP__KEYBOARD_INFO_H
