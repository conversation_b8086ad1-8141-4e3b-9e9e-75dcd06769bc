#pragma once

#include "urq/preload.h"
#include "urq_conf/data/reference.h"
#include <stdint.h>

#ifndef URQ_CONF__DATASTRUCT__LIST__DATA_H
#define URQ_CONF__DATASTRUCT__LIST__DATA_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    urq_data_reference_t *data;
    size_t size;
} urq_data_list_t;

static inline void urq_data_list_init_inplace(urq_data_list_t *list)
    __attribute__((__nonnull__(1)));

static inline void urq_data_list_free_inplace(urq_data_list_t *list)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_data_list_init_inplace(urq_data_list_t *list)
{
    list->data = NULL;
    list->size = 0;
}

void urq_data_list_free_inplace(urq_data_list_t *list)
{
    urq_free(list->data);
    list->data = NULL;
    list->size = 0;
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__UTIL__LIST__DATA_H
