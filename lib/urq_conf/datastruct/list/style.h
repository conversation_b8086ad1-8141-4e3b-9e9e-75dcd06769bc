#pragma once

#include "urq/preload.h"
#include "urq_conf/style/style.h"

#ifndef URQ_CONF__DATASTRUCT__LIST__STYLE_H
#define URQ_CONF__DATASTRUCT__LIST__STYLE_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    uint16_t size;
    urq_arr_t(urq_style_t *) data;
} urq_style_ptr_list_t;

/// @brief 原地初始化样式列表
static inline void urq_style_ptr_list_init_inplace(urq_style_ptr_list_t *list)
    __attribute__((__nonnull__(1)));

/// @brief 原地释放样式列表
static inline void urq_style_ptr_list_free_inplace(urq_style_ptr_list_t *list)
    __attribute__((__nonnull__(1)));

/// @brief 移动样式列表
static inline void urq_style_ptr_list_move(
    urq_style_ptr_list_t *dst, urq_style_ptr_list_t *src)
    __attribute__((__nonnull__(1, 2)));

// impl

static inline void urq_style_ptr_list_init_inplace(urq_style_ptr_list_t *list)
{
    list->size = 0;
    list->data = NULL;
}

static inline void urq_style_ptr_list_free_inplace(urq_style_ptr_list_t *list)
{
    urq_used(list);
    // if (list->data != NULL) {
    //     for (size_t i = 0; i < list->size; ++i) {
    //         if (list->data[i] != NULL) {
    //             urq_style_free_inplace(list->data[i]);
    //             urq_free(list->data[i]);
    //         }
    //         urq_free(list->data);
    //         list->data = NULL;
    //     }
    // }
}

static inline void urq_style_ptr_list_move(
    urq_style_ptr_list_t *dst, urq_style_ptr_list_t *src)
{
    urq_style_ptr_list_free_inplace(dst);

    dst->size = src->size;
    dst->data = src->data;
    src->size = 0;
    src->data = NULL;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__STYLE__LIST_H
