// gen by cgen/list/v1.py
#pragma once

#include "urq/preload.h"
#include "urq_conf/common/text.h"
#include <stdint.h>

#ifndef URQ_CONF__DATASTRUCT__LIST__TEXT_H
#define URQ_CONF__DATASTRUCT__LIST__TEXT_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief urq_text 列表
typedef struct {
    uint16_t size;                // urq_text 的个数
    urq_arr_t(urq_text_t *) data; // urq_text 的数组
} urq_text_ptr_list_t;

/// @brief 原地初始化列表
static inline void urq_text_ptr_list_init_inplace(urq_text_ptr_list_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 原地释放列表
static inline void urq_text_ptr_list_free_inplace(urq_text_ptr_list_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 移动整个列表
static inline void urq_text_ptr_list_move(
    urq_text_ptr_list_t *dst, //
    urq_text_ptr_list_t *src  //
    ) __attribute__((__nonnull__(1, 2)));

// ========================= impl =========================
//

static inline void urq_text_ptr_list_init_inplace(urq_text_ptr_list_t *self)
{
    self->size = 0;
    self->data = NULL;
}

static inline void urq_text_ptr_list_free_inplace(urq_text_ptr_list_t *self)
{
    urq_used(self);
    // if (self->data != NULL) {
    //     for (int16_t i = 0; i < self->size; ++i) {
    //         if (self->data[i] != NULL) {
    //             urq_text_free_inplace(self->data[i]);
    //             urq_free(self->data[i]);
    //         }
    //     }
    //     urq_free(self->data);
    //     self->data = NULL;
    // }
}

static inline void urq_text_ptr_list_move(
    urq_text_ptr_list_t *dst, urq_text_ptr_list_t *src)
{
    urq_text_ptr_list_free_inplace(dst);
    dst->size = src->size;
    dst->data = src->data;

    src->size = 0;
    src->data = NULL;
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__DATASTRUCT__LIST__TEXT_H
