#pragma once

#include "urq/preload.h"
#include "urq_conf/widget/conf.h"
#include <stddef.h>

#ifndef URQ_CONF__DATASTRUCT__LIST__WIDGET_H
#define URQ_CONF__DATASTRUCT__LIST__WIDGET_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 组件配置数量
typedef struct {
    size_t size;                         // 配置数量
    urq_arr_t(urq_widget_conf_t *) data; // 配置列表
} urq_widget_conf_list_t;

/// @brief 原地初始化配置列表
__attribute__((__nonnull__(1))) static inline void
urq_widget_conf_list_init_inplace(urq_widget_conf_list_t *self)
{
    self->size = 0;
    self->data = NULL;
}

/// @brief 原地释放配置列表
__attribute__((__nonnull__(1))) static inline void
urq_widget_conf_list_free_inplace(urq_widget_conf_list_t *self)
{
    if (self->size > 0) {
        for (size_t i = 0; i < self->size; i++) {
            urq_widget_conf_free_inplace(self->data[i]);
            urq_free(self->data[i]);
        }
    }
    urq_free_if_not_null(self->data);
}

/// @brief 移动列表数据
///
/// 移动列表的操作不会对 dst 列表进行释放
///
/// 移动列表会保证 src 列表执行 free/free_inplace 时不会出现内存泄漏
/// @param dst 目标列表
/// @param src 原列表
/// @returns void
__attribute__((__nonnull__(1, 2))) static inline void urq_widget_conf_list_move(
    urq_widget_conf_list_t *dst, urq_widget_conf_list_t *src)
{
    dst->size = src->size;
    dst->data = src->data;

    src->size = 0;
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__DATASTRUCT__LIST__WIDGET_H
