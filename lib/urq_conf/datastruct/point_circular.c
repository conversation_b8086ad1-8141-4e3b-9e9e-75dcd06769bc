#include "point_circular.h"
#include "urq/preload.h"

// 初始化队列
urq_point_cqueue_t *urq_point_cqueue_create(int capacity)
{
    urq_point_cqueue_t *queue =
        (urq_point_cqueue_t *)urq_malloc(sizeof(urq_point_cqueue_t));
    if (!queue) {
        return NULL;
    }
    queue->array =
        (urq_point_t *)urq_malloc((size_t)capacity * sizeof(urq_point_t));
    if (!queue->array) {
        urq_free(queue);
        return NULL;
    }
    queue->capacity = capacity;
    queue->size = 0;
    queue->front = 0;
    queue->rear = -1;
    return queue;
}

// 检查队列是否为空
bool urq_point_cqueue_is_empty(urq_point_cqueue_t *queue)
{
    return queue->size == 0;
}

// 检查队列是否已满
bool urq_point_cqueue_is_full(urq_point_cqueue_t *queue)
{
    return queue->size == queue->capacity;
}

// 入队操作：如果队列满，自动删除队头元素并添加新元素到队尾
bool urq_point_cqueue_enqueue(urq_point_cqueue_t *queue, urq_point_t value)
{
    if (!queue)
        return false;

    if (urq_point_cqueue_is_full(queue)) {
        // 队列满，移动front，相当于删除队头元素
        queue->front = (queue->front + 1) % queue->capacity;
        queue->size--; // 减少size，为新元素腾出空间
    }

    // 添加新元素到队尾
    queue->rear = (int)(queue->rear + 1) % queue->capacity;
    queue->array[queue->rear] = value;
    queue->size++;
    return true;
}

bool urq_point_cqueue_pop_front(urq_point_cqueue_t *queue)
{

    if (!queue || urq_point_cqueue_is_empty(queue))
        return false;

    queue->front = (queue->front + 1) % queue->capacity;
    queue->size--;
    return true;
}

// 出队操作：从队头移除元素并返回
bool urq_point_cqueue_dequeue(urq_point_cqueue_t *queue, urq_point_t *value)
{
    if (!queue || urq_point_cqueue_is_empty(queue))
        return false;

    *value = queue->array[queue->front];
    queue->front = (queue->front + 1) % queue->capacity;
    queue->size--;
    return true;
}

// 索引取值：根据索引获取元素（索引0对应队头）
bool urq_point_cqueue_get_element_at(
    urq_point_cqueue_t *queue, int index, urq_point_t *value)
{
    if (!queue || index < 0 || index >= queue->size) {
        return false;
    }
    *value = queue->array[(queue->front + index) % queue->capacity];
    return true;
}

// 遍历队列：从队头到队尾打印所有元素
void urq_point_cqueue_traverse(urq_point_cqueue_t *queue)
{
    if (!queue || urq_point_cqueue_is_empty(queue)) {
        return;
    }
    for (int i = 0; i < queue->size; i++) {
    }
}

// 销毁队列
void urq_point_cqueue_destroy(urq_point_cqueue_t *queue)
{
    if (queue) {
        urq_free(queue->array);
        urq_free(queue);
    }
}
