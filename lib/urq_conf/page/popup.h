#pragma once

#include <stddef.h>

#ifndef URQ_CONF__PAGE__POPUP_H
#define URQ_CONF__PAGE__POPUP_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 弹出窗口类型
typedef enum {
    /// @brief 未定义
    URQ_PAGE_POPUP_TYPE_UNSPECIFIED = 0,
    /// @brief 半垄断式弹出(半模态窗口，指点击外部可关闭，默认)
    URQ_PAGE_POPUP_TYPE_HALF_MODEL = 1,
    /// @brief 垄断式弹出(模态窗口)
    URQ_PAGE_POPUP_TYPE_MODEL = 2,
    /// @brief 浮窗式弹出(非模态窗口)
    URQ_PAGE_POPUP_TYPE_FLOATING = 3,
} urq_page_popup_type_t;

#ifdef __cplusplus
}
#endif
#endif
