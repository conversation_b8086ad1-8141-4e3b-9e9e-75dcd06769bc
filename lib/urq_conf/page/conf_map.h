#pragma once

#include "klib/khash.h"
#include "urq_conf/page/group_conf.h"
#include <stdint.h>

#ifndef URQ_CONF__PAGE__CONF_MAP_H
#define URQ_CONF__PAGE__CONF_MAP_H
#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

/// @brief 多语言表
KHASH_MAP_INIT_INT(urq_page_group_conf_map, urq_page_group_conf_t)
#pragma GCC diagnostic pop // 恢复之前的警告状态

/// @brief 多语言表
typedef khash_t(urq_page_group_conf_map) urq_page_group_conf_map_t;

/// @brief 创建新的页面组配置
///
/// @return 页面组配置
urq_page_group_conf_map_t *urq_page_group_conf_map_new(void)
    __attribute__((__warn_unused_result__()));

/// @brief 释放页面组配置
///
/// @param self 页面组配置
/// @return void
void urq_page_group_conf_map_free(urq_page_group_conf_map_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 调整大小
///
/// @param self 页面组配置
/// @param size 大小
/// @return void
void urq_page_group_conf_map_resize(
    urq_page_group_conf_map_t *self, uint32_t size)
    __attribute__((__nonnull__(1)));

/// @brief 添加一个页面组配置
///
/// @param self 页面组配置
/// @param id   页面组ID
/// @param conf 页面组配置
/// @return 是否添加成功
int urq_page_group_conf_map_add(
    urq_page_group_conf_map_t *const self, uint32_t id,
    urq_page_group_conf_t *conf) __attribute__((__nonnull__(1)));

/// @brief 获取页面组配置
///
/// @param self      页面组配置
/// @param id        页面组ID
/// @param out_value 页面组配置
/// @return 是否获取成功
urq_page_group_conf_t *urq_page_group_conf_map_get(
    const urq_page_group_conf_map_t *self, uint32_t id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

#ifdef __cplusplus
}
#endif

#endif // URQ_CONF__PAGE__CONF_MAP_H
