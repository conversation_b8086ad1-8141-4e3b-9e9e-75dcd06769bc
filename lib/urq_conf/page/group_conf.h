#pragma once

#include "urq/preload.h"
#include "urq/vec/i32.h"
#include "urq_conf/common/graphic.h"
#include "urq_conf/common/size.h"
#include "urq_conf/page/popup.h"
#include "urq_conf/style/style.h"
#include <string.h>

#ifndef URQ_CONF__PAGE__GROUP_CONF_H
#define URQ_CONF__PAGE__GROUP_CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    /// @brief 未定义
    URQ_PAGE_TYPE_UNSEPECIFIED = 0,
    /// @brief 基本窗口
    URQ_PAGE_TYPE_NORMAL = 1,
    /// @brief 公共窗口
    URQ_PAGE_TYPE_COMMON = 2,
    /// @brief 键盘窗口
    URQ_PAGE_TYPE_KEYBOARD = 3,
    /// @brief 信息窗口
    URQ_PAGE_TYPE_MESSAGE = 4,
    /// @brief 功能窗口
    URQ_PAGE_TYPE_FUNCTION = 5,
} urq_page_type_t;
/// @brief 画面组配置
typedef struct {
    /// @brief 画面的唯一编号
    urq_page_id_t id;
    /// @brief 画面的名称
    char *name;
    /// @brief 画面的尺寸
    urq_size_t size;
    /// @brief 画面编号
    urq_page_id_t page_no;
    /// @brief 画面的样式
    urq_style_t style;
    /// @brief 背景图片
    urq_graphic_t *graphic;
    /// @brief 组件组列表
    urq_vec_i32_t group_id_list;
    /// @brief 画面类型
    urq_page_type_t type;
    /// @brief 弹出窗口类型
    urq_page_popup_type_t popup_type;
} urq_page_group_conf_t;

/// @brief 原地初始化画面组
/// @param self 需要初始化的画面组
/// @returns void
static inline void urq_page_group_conf_init_inplace(urq_page_group_conf_t *self)
{
    self->id = 0;
    self->name = NULL;
    self->page_no = 0;
    urq_style_init_inplace(&self->style);
    urq_vec_i32_init_inplace(&self->group_id_list);
    self->graphic = NULL;
    self->size.w = 0;
    self->size.h = 0;
}

/// @brief 取消初始化画面组
/// @param self 需要初始化的画面组
/// @returns void
__attribute__((__nonnull__(1))) static inline void
urq_page_group_conf_free_inplace(urq_page_group_conf_t *self)
{
    urq_free_if_not_null(self->name);
    urq_vec_i32_free_inplace(&self->group_id_list);
    if (self->graphic != NULL) {
        urq_graphic_free_inplace(self->graphic);
        urq_free_if_not_null(self->graphic);
    }
    self->size.w = 0;
    self->size.h = 0;
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ_CONF__PAGE__GROUP_CONF_H
