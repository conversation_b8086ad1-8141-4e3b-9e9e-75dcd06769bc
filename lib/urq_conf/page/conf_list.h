#pragma once

// #include "urq_conf/page/conf.h"
#include "urq/preload.h"
#include "urq_conf/page/group_conf.h"

#ifndef URQ_CONF__PAGE__CONF_LIST_H
#define URQ_CONF__PAGE__CONF_LIST_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 页面配置列表
typedef struct {
    size_t size;                 /// @brief 数量
    urq_page_group_conf_t *data; /// @brief 页面配置
} urq_page_conf_list_t;

/// @brief 原地初始化页面配置列表
static inline void urq_page_conf_list_init_inplace(
    urq_page_conf_list_t *const self) __attribute__((__nonnull__(1)));

/// @brief 释放页面配置列表
static inline void urq_page_conf_list_free_inplace(
    urq_page_conf_list_t *const self) __attribute__((__nonnull__(1)));

/// @brief 移动配置列表
static inline void urq_page_conf_list_move(
    urq_page_conf_list_t *const dst, urq_page_conf_list_t *const src)
    __attribute__((__nonnull__(1, 2)));

// impl

static inline void urq_page_conf_list_init_inplace(
    urq_page_conf_list_t *const self)
{
    self->size = 0;
    self->data = NULL;
}

static inline void urq_page_conf_list_free_inplace(
    urq_page_conf_list_t *const self)
{
    for (size_t i = 0; i < self->size; ++i) {
        urq_page_group_conf_free_inplace(&self->data[i]);
    }
    urq_free_if_not_null(self->data);
}

static inline void urq_page_conf_list_move(
    urq_page_conf_list_t *const dst, urq_page_conf_list_t *const src)
{
    urq_page_conf_list_free_inplace(dst);
    dst->size = src->size;
    dst->data = src->data;
    src->size = 0;
    src->data = NULL;
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__PAGE__CONF_LIST_H
