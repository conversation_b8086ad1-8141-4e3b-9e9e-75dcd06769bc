#pragma once

#include "lvgl.h"
#include "urq/preload.h"
#include <stddef.h>

#ifndef URQ_CONF__PROJECT_H
#define URQ_CONF__PROJECT_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 工程配置
typedef struct {
    int local_display_id; // 本地主屏幕
    char *id;             // 工程的唯一 id
    lv_color_t bg_color;  // 背景颜色
} urq_project_conf_t;

/// @brief 原地初始化工程配置
static inline void urq_project_conf_init_inplace(urq_project_conf_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 原地释放工程配置
static inline void urq_project_conf_free_inplace(urq_project_conf_t *self)
    __attribute__((__nonnull__(1)));

// impl

static inline void urq_project_conf_init_inplace(urq_project_conf_t *self)
{
    self->local_display_id = -1;
    self->id = NULL;
}

static inline void urq_project_conf_free_inplace(urq_project_conf_t *self)
{
    urq_free_if_not_null(self->id);
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__PROJECT_H
