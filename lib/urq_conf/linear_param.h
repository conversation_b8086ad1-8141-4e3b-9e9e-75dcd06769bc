#pragma once

#include <stdint.h>

#ifndef URQ_CONF__LINEAR_PARAM_H
#define URQ_CONF__LINEAR_PARAM_H

#ifdef __cplusplus
extern "C" {
#endif

/// linear参数
typedef struct {
    /// @brief 线条长度
    float len;
    /// @brief x轴长度
    float x_len;
    /// @brief y轴长度
    float y_len;
    /// @brief x向量
    float x_vector;
    /// @brief y向量
    float y_vector;
} urq_linear_param_t;

#ifdef __cplusplus
}
#endif
#endif // URQ__LINEAR__PARAM_H
