#pragma once

#include <stddef.h>

#ifndef URQ_CONF__CONN__REQ_H
#define URQ_CONF__CONN__REQ_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    /// @brief 请求关闭连接
    URQ_CONN_REQ_CLOSE = 0,
    URQ_CONN_REQ_ASSET = 1,
    /// @brief 请求写入单个变量
    URQ_CONN_REQ_WRITE_SINGLE = 2,

    /// @brief MAX, auto increment
    URQ_CONN_REQ_MAX,
} urq_conn_req_t;

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ_CONF__CONN__REQ_H
