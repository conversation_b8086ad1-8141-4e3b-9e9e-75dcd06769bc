#pragma once

#include "urq/preload.h"
#include "urq_conf/conn/cb.h"
#include "urq_conf/conn/res.h"
#include "urq_conf/socket/socket.h"
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

#ifndef URQ_CONF__CONN__PARSER_H
#define URQ_CONF__CONN__PARSER_H
#ifdef __cplusplus
extern "C" {
#endif

struct _urq_conn_parser;

/// @brief 解析方法，用于接收到数据时执行的下一步操作
/// @param self 解析器
/// @param ctx    附加数据
/// @returns 是否解析出错了
typedef int(urq_conn_parse_cb_t)(struct _urq_conn_parser *self, void *ctx);

/// @brief 解析方法附加数据的释放方法
typedef void(urq_conn_parse_ctx_free_cb_t)(void *ctx);

/// @brief 解析频骤
typedef enum {
    /// @brief 正在解析操作码
    URQ_CONN_PARSER_PHASE_OP = 0,
    /// @brief 正在解析标志
    URQ_CONN_PARSER_PHASE_FLAG,
    /// @brief 正在解析长度, 1 字节
    URQ_CONN_PARSER_PHASE_LEN_U8,
    /// @brief 正在解析长度，2 字节
    URQ_CONN_PARSER_PHASE_LEN_U16,
    /// @brief 正在解析长度，4 字节
    URQ_CONN_PARSER_PHASE_LEN_U32,
    /// @brief 正在解析数据
    URQ_CONN_PARSER_PHASE_DATA,
} urq_conn_parser_phase_t;

typedef struct _urq_conn_parser {
    /// @brief 需要解析的连接
    urq_socket_t *socket;

    /// @brief 解析阶段
    urq_conn_parser_phase_t phase;

    /// @brief 数据包操作码
    urq_conn_res_t op;
    /// @brief 需要接收的数据包长度
    uint32_t cache_need;
    /// @brief 已经缓存的数据大小
    uint32_t cache_size;
    /// @brief 数据包内容
    uint8_t *cache_data;

    /// @brief 操作码回调
    struct {
        /// @brief 回调上下文
        void *cb_ctx;
        /// @brief 回调
        urq_conn_cb_operator_t cb;
    } operator_cb;

    /// @brief 画面变量回调
    struct {
        void *arg1;
        urq_conn_cb_page_var_t cb;
    } page_var_cb;

} urq_conn_parser_t;

/// @brief 初始化解析器
/// @param self 解析器
/// @returns void
static inline void urq_conn_parser_init_inplace(urq_conn_parser_t *self);

/// @brief 反初始化解析器
/// @param self 解析器
/// @returns void
static inline void urq_conn_parser_free_inplace(urq_conn_parser_t *self);

/// @brief 连接断开时的回调
/// @details 这个方法并不会清除全部的资源，仅仅清除一些缓存数据等待重新连接
/// @param self 解析器
/// @returns void
static inline void urq_conn_parser_on_close(urq_conn_parser_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 解析数据
/// @param self 解析器
/// @returns 是否解析出错了
int urq_conn_parser_parse(urq_conn_parser_t *self)
    __attribute__((__nonnull__(1))) __attribute__((__warn_unused_result__()));

/// @brief 设置变量回调
/// @param self   解析器
/// @param ctx    上下文
/// @param cb     回调
/// @returns void
static inline void urq_conn_parser_set_operator_cb(
    urq_conn_parser_t *self, void *ctx, urq_conn_cb_operator_t cb)
    __attribute__((__nonnull__(1, 3)));

/// @brief 设置变量回调
/// @param self   解析器
/// @param ctx    上下文
/// @param cb     回调
/// @returns void
static inline void urq_conn_parser_set_cb_page_var(
    urq_conn_parser_t *self, void *ctx, urq_conn_cb_page_var_t cb)
    __attribute__((__nonnull__(1, 3)));

// ---------------------------------------------------------

static inline void urq_conn_parser_init_inplace(urq_conn_parser_t *self)
{
    self->phase = URQ_CONN_PARSER_PHASE_OP;
    self->cache_size = 0;
    self->cache_data = NULL;
    self->op = URQ_CONN_RES_MAX;

    self->operator_cb.cb_ctx = NULL;
    self->operator_cb.cb = NULL;

    self->page_var_cb.arg1 = NULL;
    self->page_var_cb.cb = NULL;
}

static inline void urq_conn_parser_free_inplace(urq_conn_parser_t *self)
{
    urq_free_if_not_null(self->cache_data);
}

static inline void urq_conn_parser_on_close(urq_conn_parser_t *self)
{
    self->phase = URQ_CONN_PARSER_PHASE_OP;
    self->cache_size = 0;
    self->cache_data = NULL;
    self->op = URQ_CONN_RES_MAX;
}

static inline void urq_conn_parser_set_operator_cb(
    urq_conn_parser_t *self, void *ctx, urq_conn_cb_operator_t cb)
{
    self->operator_cb.cb_ctx = ctx;
    self->operator_cb.cb = cb;
}

static inline void urq_conn_parser_set_cb_page_var(
    urq_conn_parser_t *self, void *ctx, urq_conn_cb_page_var_t cb)
{
    self->page_var_cb.arg1 = ctx;
    self->page_var_cb.cb = cb;
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__CONN__PARSER_H
