#pragma once

#include "urq_conf/conn/res.h"
#include <stdint.h>

#ifndef URQ_CONF__CONN__CB_H
#define URQ_CONF__CONN__CB_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief page var 回调方法
///
/// @param arg1 回调的参数1
/// @param size 数据大小
/// @param mv_data 数据
/// @returns void
typedef void (*urq_conn_cb_page_var_t)(
    void *arg1, uint32_t size, uint8_t *mv_data);

typedef void (*urq_conn_cb_operator_t)(
    void *ctx, urq_conn_res_t res, uint32_t ext, uint32_t size,
    uint8_t *ref_data);

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ_CONF__CONN__CB_H
