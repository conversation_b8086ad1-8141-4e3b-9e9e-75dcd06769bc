#include "urq_conf/conn/parser.h"
#include "urq/log/verbose.h"
#include "urq/preload.h"
#include "urq_conf/conn/res.h"
#include "urq_conf/socket/socket.h"
#include <stdint.h>

/// @brief parse op
/// @param self 解析器
/// @returns 是否解析出错了
int urq_conn_parser_parse_op(urq_conn_parser_t *self)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief parse flag
/// @param self 解析器
/// @returns 是否解析出错了
int urq_conn_parser_parse_flag(urq_conn_parser_t *self)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief parse len u8
/// @param self 解析器
/// @returns 是否解析出错了
int urq_conn_parser_parse_len_u8(urq_conn_parser_t *self)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief parse len u16
/// @param self 解析器
/// @returns 是否解析出错了
int urq_conn_parser_parse_len_u16(urq_conn_parser_t *self)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief parse len u32
/// @param self 解析器
/// @returns 是否解析出错了
int urq_conn_parser_parse_len_u32(urq_conn_parser_t *self)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief read cache
/// @param self 解析器
/// @returns 是否读取出错了
int urq_conn_parser_read_cache(urq_conn_parser_t *self)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief cache ready
/// @param self 解析器
/// @returns 是否读取出错了
int urq_conn_parser_cache_ready(urq_conn_parser_t *self)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

int urq_conn_parser_parse(urq_conn_parser_t *self)
{
    switch (self->phase) {
    case URQ_CONN_PARSER_PHASE_OP:
        return urq_conn_parser_parse_op(self);
    case URQ_CONN_PARSER_PHASE_FLAG:
        return urq_conn_parser_parse_flag(self);
    case URQ_CONN_PARSER_PHASE_LEN_U8:
        return urq_conn_parser_parse_len_u8(self);
    case URQ_CONN_PARSER_PHASE_LEN_U16:
        return urq_conn_parser_parse_len_u16(self);
    case URQ_CONN_PARSER_PHASE_LEN_U32:
        return urq_conn_parser_parse_len_u32(self);
    case URQ_CONN_PARSER_PHASE_DATA:
        return urq_conn_parser_read_cache(self);
    default:
        log_w("// TODO: 未知的解析阶段\n");
        return -1;
    }
}

int urq_conn_parser_parse_op(urq_conn_parser_t *self)
{
    int enough = 0;
    if (urq_socket_cache_recv(self->socket, 1, &enough)) {
        log_w("// TODO: 数据读取出错了，连接应该是断开了\n");
        return -1;
    }
    if (!enough) {
        return 0;
    }

    uint16_t code = urq_socket_cache_read_u16(self->socket);

    uint8_t crc = urq_socket_cache_read_u8(self->socket);
    urq_used(crc);
    self->op = code;
    self->phase = URQ_CONN_PARSER_PHASE_LEN_U16;
    return urq_conn_parser_parse_len_u16(self);
}

int urq_conn_parser_parse_flag(urq_conn_parser_t *self)
{
    int enough = 0;
    if (urq_socket_cache_recv(self->socket, 1, &enough)) {
        log_w("// TODO: 数据读取出错了，连接应该是断开了\n");
        return -1;
    }
    if (!enough) {
        return 0;
    }

    uint8_t flag;
    urq_socket_cache_read(self->socket, 1, &flag);
    self->phase = URQ_CONN_PARSER_PHASE_LEN_U8;
    return urq_conn_parser_parse_len_u8(self);
}

int urq_conn_parser_parse_len_u8(urq_conn_parser_t *self)
{
    int enough = 0;
    if (urq_socket_cache_recv(self->socket, 1, &enough)) {
        log_w("// TODO: 数据读取出错了，连接应该是断开了\n");
        return -1;
    }
    if (!enough) {
        return 0;
    }

    uint8_t len;
    urq_socket_cache_read(self->socket, 1, &len);
    self->cache_need = len;
    if (self->cache_need == 0xFF) {
        self->phase = URQ_CONN_PARSER_PHASE_LEN_U16;
        return urq_conn_parser_parse_len_u16(self);
    } else {
        self->phase = URQ_CONN_PARSER_PHASE_DATA;
        return urq_conn_parser_read_cache(self);
    }
}

int urq_conn_parser_parse_len_u16(urq_conn_parser_t *self)
{
    int enough = 0;
    if (urq_socket_cache_recv(self->socket, 2, &enough)) {
        log_w("// TODO: 数据读取出错了，连接应该是断开了\n");
        return -1;
    }
    if (!enough) {
        return 0;
    }

    uint16_t len;
    urq_socket_cache_read(self->socket, 2, &len);
    self->cache_need = len;
    if (self->cache_need == 0xFFFF) {
        self->phase = URQ_CONN_PARSER_PHASE_LEN_U32;
        return urq_conn_parser_parse_len_u32(self);
    } else {
        self->phase = URQ_CONN_PARSER_PHASE_DATA;
        return urq_conn_parser_read_cache(self);
    }
}

int urq_conn_parser_parse_len_u32(urq_conn_parser_t *self)
{
    int enough = 0;
    if (urq_socket_cache_recv(self->socket, 4, &enough)) {
        log_w("// TODO: 数据读取出错了，连接应该是断开了\n");
        return -1;
    }
    if (!enough) {
        return 0;
    }

    uint32_t len;
    urq_socket_cache_read(self->socket, 4, &len);
    self->cache_need = len;
    if (self->cache_need == 0) {
        log_w("// TODO: 数据长度为 0，这是不合法的\n");
        return -1;
    }
    self->phase = URQ_CONN_PARSER_PHASE_DATA;
    return urq_conn_parser_read_cache(self);
}

int urq_conn_parser_read_cache(urq_conn_parser_t *self)
{
    uint32_t read_len;
    uint32_t need_len;

    if (self->cache_data == NULL) {
        self->cache_size = 0;
        self->cache_data = urq_malloc(self->cache_need);
    }
    need_len = self->cache_need - self->cache_size;

    if (urq_socket_read(
            self->socket, need_len, self->cache_data + self->cache_size,
            &read_len)) {
        log_w("// TODO: 数据读取出错了，连接应该是断开了\n");
        return -1;
    }
    self->cache_size += read_len;

    if (self->cache_size < self->cache_need) {
        return 0;
    }
    return urq_conn_parser_cache_ready(self);
}

int urq_conn_parser_cache_ready(urq_conn_parser_t *self)
{
    uint32_t size = self->cache_need;
    uint8_t *data = self->cache_data;

    self->cache_data = NULL;
    self->cache_size = 0;
    self->phase = URQ_CONN_PARSER_PHASE_OP;
    // return urq_conn_lwf_parser_add_data(&self->lf_parser, size, data);
    if (self->operator_cb.cb != NULL) {
        self->operator_cb.cb(self->operator_cb.cb_ctx, self->op, 0, size, data);
    }

    return 0;
}
