#pragma once

#include <stddef.h>
#include <stdint.h>
#include <stdlib.h>

#ifndef URQ_CONF__CONN__ASSET_H
#define URQ_CONF__CONN__ASSET_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 资源
typedef struct {
    /// @brief 资源大小
    size_t size;
    /// @brief 资源数据
    uint8_t *data;
} urq_conn_asset_t;

/// @brief 创建资源
__attribute__((__warn_unused_result__()))
__attribute__((__malloc__())) static inline urq_conn_asset_t *
urq_conn_asset_new(size_t size) {
    urq_conn_asset_t *asset =
        (urq_conn_asset_t *)malloc(sizeof(urq_conn_asset_t));
    if (asset == NULL) {
        return NULL;
    }
    asset->size = size;
    asset->data = (uint8_t *)malloc(size);
    if (asset->data == NULL) {
        free(asset);
        return NULL;
    }
    return asset;
}

/// @brief 销毁资源
__attribute__((__nonnull__())) static inline void urq_conn_asset_delete(
    urq_conn_asset_t *asset) {
    free(asset->data);
    free(asset);
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__CONN__ASSET_H
