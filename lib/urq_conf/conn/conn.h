#pragma once

#include "urq/preload.h"
#include "urq_conf/conn/cb.h"
#include "urq_conf/conn/parser.h"
#include "urq_conf/socket/socket.h"
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include <sys/select.h>
#include <sys/un.h>

#ifndef URQ_CONF__CONN__CONN_H
#define URQ_CONF__CONN__CONN_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 前后端的连接
    urq_socket_t socket;
    /// @brief 解析器
    urq_conn_parser_t parser;
} urq_conn_t;

/// @brief  初始化连接
/// @param  self 连接
/// @return void
void urq_conn_init(urq_conn_t *self) __attribute__((__nonnull__(1)));

/// @brief 反初始化连接
/// @param  self 连接
/// @return void
static inline void urq_conn_deinit(urq_conn_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 连接到服务器
/// @param self 连接
/// @returns 是否连接成功
/// -  0: 连接成功
/// - -1: 连接失败
/// - -2: 连接中
static inline int urq_conn_connect(urq_conn_t *self)
    __attribute__((__nonnull__(1))) __attribute__((__warn_unused_result__()));

/// @brief 轮询连接，接收数据
/// @param self 连接
void urq_conn_poll(urq_conn_t *self) __attribute__((__nonnull__(1)));

/// @brief 设置连接，设置连接之前需要先创建连接
/// @param self 连接
/// @param ip   服务器 IP
/// @param port 服务器端口
/// @returns 是否设置成功
static inline int urq_conn_set_host(
    urq_conn_t *self, int32_t iid, uint32_t ip, uint16_t port)
    __attribute__((__nonnull__(1)));

/// @brief 写入数据
/// @param self 连接
/// @param size 数据长度
/// @param data 数据
/// @returns 是否写入成功
static inline int urq_conn_write(
    urq_conn_t *self, uint32_t size, const void *data)
    __attribute__((__nonnull__(1, 3)))
    __attribute__((__warn_unused_result__()));

/// @brief 设置变量回调
/// @param self 连接
/// @param ctx 上下文
/// @param cb  回调
/// @returns void
static inline void urq_conn_set_cb_page_var(
    urq_conn_t *self, void *ctx, urq_conn_cb_page_var_t cb)
    __attribute__((__nonnull__(1, 3)));

/// @brief 设置操作码回调
/// @param self 连接
/// @param ctx  上下文
/// @param cb   回调
/// @returns void
static inline void urq_conn_set_cb_operator(
    urq_conn_t *self, void *ctx, urq_conn_cb_operator_t cb)
    __attribute__((__nonnull__(1, 3)));

// impl

static inline void urq_conn_deinit(urq_conn_t *self)
{
    urq_socket_deinit(&self->socket);
    urq_conn_parser_free_inplace(&self->parser);
}

static inline int urq_conn_connect(urq_conn_t *self)
{
    return urq_socket_connect(&self->socket);
}

static inline int urq_conn_set_host(
    urq_conn_t *self, int32_t iid, uint32_t ip, uint16_t port)
{
    return urq_socket_set_host(&self->socket, iid, ip, port);
}

static inline int urq_conn_write(
    urq_conn_t *self, uint32_t size, const void *data)
{
    return urq_socket_write(&self->socket, size, data);
}

static inline void urq_conn_set_cb_operator(
    urq_conn_t *self, void *ctx, urq_conn_cb_operator_t cb)
{
    urq_conn_parser_set_operator_cb(&self->parser, ctx, cb);
}

static inline void urq_conn_set_cb_page_var(
    urq_conn_t *self, void *ctx, urq_conn_cb_page_var_t cb)
{
    urq_conn_parser_set_cb_page_var(&self->parser, ctx, cb);
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__CONN__CONN_H
