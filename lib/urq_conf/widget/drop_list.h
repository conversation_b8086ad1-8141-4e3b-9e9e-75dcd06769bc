#pragma once

#include "urq/preload.h"
#include "urq_conf/widget/option_list.h"
#include <lvgl/src/misc/lv_area.h>

#ifndef URQ_CONF__WIDGET__DROP_LIST_H
#define URQ_CONF__WIDGET__DROP_LIST_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 滚动模式
typedef enum {
    URQ_ROLLER_LIST_MODE_INFINITE = 0,
    URQ_ROLLER_LIST_MODE_LIMITED = 1,
} urq_roller_list_mode_t;

typedef struct {
    /// @brief 列表
    urq_option_list_conf_t conf;
    /// @brief 下拉内容框颜色
    urq_color_ref_t list_bg_color;
    /// @brief 下拉方向
    lv_dir_t direction;
    /// @brief 下拉图标
    const char *droplist_symbol;
    /// @brief 是否常开
    bool always_open;
} urq_dropList_widget_conf_t;

static inline void urq_dropList_widget_conf_init_inplace(
    urq_dropList_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_dropList_widget_conf_free_inplace(
    urq_dropList_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_dropList_widget_conf_init_inplace(urq_dropList_widget_conf_t *self)
{
    self->direction = LV_DIR_NONE;
    self->always_open = false;
    urq_color_ref_init_inplace(&self->list_bg_color);
    urq_option_list_conf_init_inplace(&self->conf);
}

void urq_dropList_widget_conf_free_inplace(urq_dropList_widget_conf_t *self)
{
    urq_option_list_conf_free_inplace(&self->conf);
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__WIDGET__DROP_LIST_H
