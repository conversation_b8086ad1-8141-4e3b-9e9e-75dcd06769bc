#pragma once

#include "lvgl.h"
#include "urq_conf/widget/curve.h"

#ifndef URQ_CONF__WIDGET__CURVE_TREND_H
#define URQ_CONF__WIDGET__CURVE_TREND_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 曲线配置
    urq_curve_widget_conf_t *conf;
    /// @brief 使用时间标签才可用
    urq_time_format_t time_format;
    /// @brief 使用时间标签才可用 日期格式
    urq_date_format_t date_format;
    /// @brief 暂停恢复时间 max=60s
    uint8_t pause_resume_time;
    /// @brief 曲线显示类型 （实时/历史）
    bool is_history;
    /// @brief 是否使用相对时间
    bool use_relative_time;
    /// @brief 是否使用标签点
    bool use_time_label;
} urq_trend_curve_widget_conf_t;

static inline void urq_trend_curve_widget_conf_init_inplace(
    urq_trend_curve_widget_conf_t *self)
{
    self->conf = NULL;
    self->time_format = URQ_TIME_FORMAT_HMS;
    self->date_format = URQ_DATE_FORMAT_MDY;
    self->pause_resume_time = 0;
    self->is_history = false;
    self->use_relative_time = false;
    self->use_time_label = false;
}

static inline void urq_trend_curve_widget_conf_free_inplace(
    urq_trend_curve_widget_conf_t *self)
{
    self->conf = NULL;
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef
