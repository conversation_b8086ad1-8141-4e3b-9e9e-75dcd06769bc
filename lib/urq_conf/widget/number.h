#pragma once

#include "urq/preload.h"
#include "urq_conf/common/color.h"
#include "urq_conf/data/type.h"
#include "urq_conf/style/font.h"
#include <stdbool.h>

#ifndef URQ_CONF__WIDGET__NUMBER_H
#define URQ_CONF__WIDGET__NUMBER_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 值
    urq_data_t *value;
    /// @brief 最大值
    urq_data_t *max_value;
    /// @brief 最小值
    urq_data_t *min_value;
    /// @brief 字体
    // urq_font_t *font;
    /// @brief 小于最小值颜色
    urq_color_ref_t less_than_min_color;
    /// @brief 大于最大值颜色
    urq_color_ref_t greater_than_max_color;
    /// @brief 数据格式
    // urq_data_format_t data_format;
    /// @brief 键盘页面id
    urq_page_id_t keyboard_page_id;
    /// @brief 小于最小值闪烁时间
    uint8_t less_than_min_flash_time;
    /// @brief 大于最大值闪烁时间
    uint8_t greater_than_max_flash_time;
    /// @brief 整数位数
    uint8_t integer_digits;
    /// @brief 小数位数
    uint8_t decimal_digits;
    /// @brief 密码模式
    bool pwd_mode;
    /// @brief 是否支持输入
    bool support_input;
    /// @brief 是否隐藏前导零
    bool hide_leading_zero;
    /// @brief 是否显示千位分隔符
    bool show_thousands_separator;
    /// @brief 是否隐藏尾随零
    bool hide_trailing_zero;
    /// @brief 是否显示正号
    bool show_plus_sign;
} urq_number_conf_t;

static inline void urq_number_conf_init_inplace(urq_number_conf_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_number_conf_free_inplace(urq_number_conf_t *self)
    __attribute__((__nonnull__(1)));

// ----------------------------------------------------

static inline void urq_number_conf_init_inplace(urq_number_conf_t *self)
{
    self->value = NULL;
    self->max_value = NULL;
    self->min_value = NULL;
    // self->font = NULL;
    urq_color_ref_init_inplace(&self->less_than_min_color);
    urq_color_ref_init_inplace(&self->greater_than_max_color);
    self->keyboard_page_id = 0;
    self->less_than_min_flash_time = 0;
    self->greater_than_max_flash_time = 0;
    self->integer_digits = 0;
    self->decimal_digits = 0;
    self->pwd_mode = false;
    self->support_input = false;
    self->hide_leading_zero = false;
    self->show_thousands_separator = false;
    self->hide_trailing_zero = true;
    self->show_plus_sign = false;
}

static inline void urq_number_conf_free_inplace(urq_number_conf_t *self)
{
    // if (self->font != NULL) {
    //     urq_font_free_inplace(self->font);
    //     urq_free(self->font);
    // }
    if (self->value != NULL) {
        urq_data_free_inplace(self->value);
        urq_free(self->value);
    }
    if (self->max_value != NULL) {
        urq_data_free_inplace(self->max_value);
        urq_free(self->max_value);
    }
    if (self->min_value != NULL) {
        urq_data_free_inplace(self->min_value);
        urq_free(self->min_value);
    }
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ_CONF__WIDGET__NUMBER_H
