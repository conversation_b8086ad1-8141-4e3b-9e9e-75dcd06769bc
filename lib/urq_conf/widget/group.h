#pragma once

#include "urq_conf/datastruct/list/widget.h"
#include <stdint.h>

#ifndef URQ_CONF__WIDGET__GROUP_H
#define URQ_CONF__WIDGET__GROUP_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct urq_group_widget_conf_t {
    urq_widget_conf_list_t widgets;
} urq_group_widget_conf_t;

static inline void urq_group_widget_conf_init_inplace(
    urq_group_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_group_widget_conf_free_inplace(
    urq_group_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_group_widget_conf_init_inplace(urq_group_widget_conf_t *self)
{
    urq_widget_conf_list_init_inplace(&self->widgets);
}

void urq_group_widget_conf_free_inplace(urq_group_widget_conf_t *self)
{
    urq_widget_conf_list_init_inplace(&self->widgets);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__group__CONF_H
