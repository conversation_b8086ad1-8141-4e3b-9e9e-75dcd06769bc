#pragma once

#include "urq_conf/datastruct/list/widget.h"

#ifndef URQ_CONF__WIDGET__GROUP_CONF_H
#define URQ_CONF__WIDGET__GROUP_CONF_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 画面所拥有的组件列表
typedef struct {
    urq_widget_conf_list_t widgets; // 组件列表
} urq_widget_group_conf_t;

/// @brief 原地初始化组件列表
/// @param out_conf 输出配置
static inline void urq_widget_group_init_inplace(
    urq_widget_group_conf_t *const self) __attribute__((__nonnull__(1)));

/// @brief 原地释放组件列表
/// @param out_conf 输出配置
static inline void urq_widget_group_free_inplace(
    urq_widget_group_conf_t *const self) __attribute__((__nonnull__(1)));

// impl

static inline void urq_widget_group_init_inplace(
    urq_widget_group_conf_t *const self)
{
    urq_widget_conf_list_init_inplace(&self->widgets);
}

static inline void urq_widget_group_free_inplace(
    urq_widget_group_conf_t *const self)
{
    urq_widget_conf_list_free_inplace(&self->widgets);
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__WIDGET__GROUP_CONF_H
