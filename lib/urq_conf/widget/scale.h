#pragma once

#include "urq_conf/common/arc.h"
#include "urq_conf/common/color.h"
#include "urq_conf/common/scale.h"
#include "urq_conf/common/scale_label.h"
#include "urq_conf/style/font.h"
#include "urq_conf/style/line.h"
#include "urq_conf/style/point.h"

#ifndef URQ_CONF__WIDGET__SCALE_H
#define URQ_CONF__WIDGET__SCALE_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 刻度尺类型
typedef enum {
    /// @brief 未设置
    URQ_SCALE_TYPE_UNSPECIFIED = 0,
    /// @brief 直线刻度尺
    URQ_SCALE_TYPE_LINE = 1,
    /// @brief 弧形刻度尺
    URQ_SCALE_TYPE_ARC = 2,
} urq_scale_type_t;

/// @brief 刻度对齐方式
typedef enum {
    /// @brief 左对齐（水平刻度尺）/ 上对齐（垂直刻度尺）
    URQ_SCALE_ALIGNMENT_START = 0,
    /// @brief 居中对齐
    URQ_SCALE_ALIGNMENT_CENTER = 1,
    /// @brief 右对齐（水平刻度尺）/ 下对齐（垂直刻度尺）
    URQ_SCALE_ALIGNMENT_END = 2,
} urq_scale_alignment_t;

/// @brief 刻度尺配置
typedef struct {
    /// @brief 刻度尺类型
    urq_scale_type_t type;
    /// @brief 刻度配置
    urq_scale_t scale;
    /// @brief 刻度标签配置
    urq_curve_scale_label_t *scale_label;
    /// @brief 弧形配置
    urq_arc_t *arc;
    /// @brief 直线配置
    urq_style_line_t *line;
    /// @brief 点配置
    lv_point_t *point;
    /// @brief 点数量
    uint8_t point_cnt;
    /// @brief 旋转角度
    lv_coord_t rotation_angle;
} urq_scale_widget_conf_t;

/// @brief 初始化刻度尺配置
/// @param self 配置结构指针
static inline void urq_scale_widget_conf_init_inplace(
    urq_scale_widget_conf_t *self) __attribute__((__nonnull__(1)));

/// @brief 释放刻度尺配置
/// @param self 配置结构指针
static inline void urq_scale_widget_conf_free_inplace(
    urq_scale_widget_conf_t *self) __attribute__((__nonnull__(1)));

void urq_scale_widget_conf_init_inplace(urq_scale_widget_conf_t *self)
{
    self->type = URQ_SCALE_TYPE_LINE;
    urq_curve_scale_init_inplace(&self->scale);
    self->scale_label = NULL;
    self->arc = NULL;
    self->line = NULL;
    self->point = NULL;
    self->point_cnt = 0;
}

void urq_scale_widget_conf_free_inplace(urq_scale_widget_conf_t *self)
{
    urq_curve_scale_free_inplace(&self->scale);

    if (self->line != NULL) {
        urq_style_line_free_inplace(self->line);
        urq_free(self->line);
        self->line = NULL;
    }
    if (self->arc != NULL) {
        urq_arc_free_inplace(self->arc);
        urq_free(self->arc);
        self->arc = NULL;
    }
    if (self->point != NULL) {
        urq_free(self->point);
        self->point = NULL;
    }
    if (self->scale_label != NULL) {
        urq_curve_scale_label_free_inplace(self->scale_label);
        urq_free(self->scale_label);
        self->scale_label = NULL;
    }
}

#ifdef __cplusplus
}
#endif

#endif // URQ_CONF__WIDGET__SCALE_H
