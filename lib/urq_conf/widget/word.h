#pragma once

#include "urq_conf/datastruct/list/text.h"
#include <stdint.h>

#ifndef URQ_CONF__WIDGET__WORD_H
#define URQ_CONF__WIDGET__WORD_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    // @brief 文本
    urq_text_ptr_list_t *texts;
} urq_word_widget_conf_t;

static inline void urq_word_widget_conf_init_inplace(
    urq_word_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_word_widget_conf_free_inplace(
    urq_word_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_word_widget_conf_init_inplace(urq_word_widget_conf_t *self)
{
    self->texts = NULL;
}

void urq_word_widget_conf_free_inplace(urq_word_widget_conf_t *self)
{
    if (self->texts != NULL) {
        urq_text_ptr_list_free_inplace(self->texts);
        urq_free(self->texts);
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__WIDGET__WORD_H
