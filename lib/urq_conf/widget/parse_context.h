#pragma once

#include "urq/platform.h"
#include "urq_conf/datastruct/list/u8.h"
#include "urq_conf/datastruct/map/font.h"
#include "urq_conf/datastruct/map/i18n.h"
#include "urq_conf/datastruct/map/theme_color.h"
#include "urq_conf/datastruct/map/theme_style.h"
#include "urq_conf/style/font.h"
#include "urq_conf/system_variable.h"
#include <stdbool.h>
#include <stddef.h>

#ifndef URQ_CONF__WIDGET__PARSE_CONTEXT_H
#define URQ_CONF__WIDGET__PARSE_CONTEXT_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 配置解析上下文
typedef struct {
    /// @brief 是否是本地工程
    bool is_local;
    /// @brief 当前平台
    urq_platform_t platform;
    // /// @brief 主题列表
    // urq_arr_t(void *) theme;
    /// @brief 多语言文本的表，由 i18n id 映射到文本
    urq_i18n_map_t *i18n_map;
    /// @brief 样式
    urq_theme_style_t *theme_style;
    /// @brief 颜色
    urq_theme_color_t *theme_color;
    /// @brief 语言列表
    urq_u8_list_t language_list;
    /// @brief 字体ID
    urq_font_map_t *font_map;
    ///// @brief  系统变量
    urq_device_var_t var_sys;
} urq_widget_parse_context_t;

/// @brief 原地初始化解析上下文
static inline void urq_widget_parse_context_init_inplace(
    urq_widget_parse_context_t *const self) __attribute__((__nonnull__(1)));

/// @brief 原地释放解析上下文
static inline void urq_widget_parse_context_free_inplace(
    urq_widget_parse_context_t *const self) __attribute__((__nonnull__(1)));

// impl

static inline void urq_widget_parse_context_init_inplace(
    urq_widget_parse_context_t *const self)
{
    self->is_local = false;
    self->platform = URQ_PLATFORM_UNKNOWN;
    self->i18n_map = NULL;
    self->theme_style = NULL;
    self->theme_color = NULL;
    self->font_map = NULL;
    urq_u8_list_init_inplace(&self->language_list);
    urq_device_local_addr_init_inplace(&self->var_sys);
}

static inline void urq_widget_parse_context_free_inplace(
    urq_widget_parse_context_t *const self)
{
    if (self->i18n_map != NULL) {
        urq_i18n_map_free(self->i18n_map);
        self->i18n_map = NULL;
    }
    if (self->theme_style != NULL) {
        urq_theme_style_free(self->theme_style);
        self->theme_style = NULL;
    }
    if (self->theme_color != NULL) {
        urq_theme_color_free(self->theme_color);
        self->theme_color = NULL;
    }

    if (self->font_map != NULL) {
        urq_font_map_free(self->font_map);
        self->font_map = NULL;
    }
    urq_u8_list_free_inplace(&self->language_list);
    urq_device_local_addr_free_inplace(&self->var_sys);
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__WIDGET__PARSE_CONTEXT_H
