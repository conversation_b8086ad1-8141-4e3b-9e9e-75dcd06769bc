#pragma once

#include "urq_conf/common/color.h"
#include "urq_conf/style/bar.h"
#include "urq_conf/style/line.h"
#include "urq_conf/style/point.h"

#ifndef URQ_CONF__WIDGET__CURVE__SERIES_H
#define URQ_CONF__WIDGET__CURVE__SERIES_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief [optional]数据序列线
    urq_style_line_t *line;
    /// @brief [optional]数据序列点
    urq_style_point_t *point;
    /// @brief [optional]数据序列柱状图
    urq_style_bar_t *bar;
    /// @brief 数据序列颜色
    urq_color_ref_t color;
    /// @brief 数据序列最大值
    lv_coord_t y_max_value;
    /// @brief 数据序列最小值
    lv_coord_t y_min_value;
    /// @brief 数据序列最大值
    lv_coord_t x_max_value;
    /// @brief 数据序列最小值
    lv_coord_t x_min_value;
    /// @brief 是否支持比例缩放
    bool support_scale;
    /// @brief 是否显示渐变遮罩
    bool show_fade_mask;
} urq_curve_series_t;

static inline void urq_curve_series_init_inplace(urq_curve_series_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_curve_series_free_inplace(urq_curve_series_t *self)
    __attribute__((__nonnull__(1)));

void urq_curve_series_init_inplace(urq_curve_series_t *self)
{
    urq_color_ref_init_inplace(&self->color);
    self->line = NULL;
    self->point = NULL;
    self->bar = NULL;
    self->support_scale = false;
    self->y_max_value = -1;
    self->y_min_value = -1;
    self->x_max_value = -1;
    self->x_min_value = -1;
    self->show_fade_mask = false;
}

void urq_curve_series_free_inplace(urq_curve_series_t *self)
{
    if (self->line != NULL) {
        urq_style_line_free_inplace(self->line);
        urq_free(self->line);
    }
    if (self->point != NULL) {
        urq_style_point_free_inplace(self->point);
        urq_free(self->point);
    }
    if (self->bar != NULL) {
        urq_style_bar_free_inplace(self->bar);
        urq_free(self->bar);
    }
    self->line = NULL;
    self->point = NULL;
    self->bar = NULL;
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef
