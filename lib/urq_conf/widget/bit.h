#pragma once

#include "urq_conf/datastruct/list/text.h"
#include <stdint.h>

#ifndef URQ_CONF__WIDGET__BIT_H
#define URQ_CONF__WIDGET__BIT_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 文本
    bool a;
} urq_bit_widget_conf_t;

static inline void urq_bit_widget_conf_init_inplace(urq_bit_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_bit_widget_conf_free_inplace(urq_bit_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_bit_widget_conf_init_inplace(urq_bit_widget_conf_t *self)
{
    urq_used(self);
}

void urq_bit_widget_conf_free_inplace(urq_bit_widget_conf_t *self)
{
    urq_used(self);
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__WIDGET__BIT_H
