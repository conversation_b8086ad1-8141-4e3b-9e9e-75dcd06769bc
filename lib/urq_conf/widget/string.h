#pragma once

#include "urq_conf/style/font.h"
#include <stdbool.h>

#ifndef URQ_CONF__WIDGET__STRING_H
#define URQ_CONF__WIDGET__STRING_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    // urq_font_t *font;
    /// @brief 键盘页面id
    urq_page_id_t keyboard_page_id;
    /// @brief 最大字符个数
    uint16_t max_length;
    /// @brief 最小字符个数
    uint16_t min_length;
    /// @brief 值
    const char *value;
    /// @brief 显示滚动条
    bool show_scrollbar;
    /// @brief 密码模式
    bool pwd_mode;
    /// @brief 是否支持输入
    bool support_input;
} urq_string_conf_t;

static inline void urq_string_conf_init_inplace(urq_string_conf_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_string_conf_free_inplace(urq_string_conf_t *self)
    __attribute__((__nonnull__(1)));

// ----------------------------------------------------

static inline void urq_string_conf_init_inplace(urq_string_conf_t *self)
{
    self->value = NULL;
    // self->font = NULL;
    self->max_length = 0;
    self->min_length = 0;
    self->show_scrollbar = false;
    self->pwd_mode = false;
    self->support_input = false;
    self->keyboard_page_id = 0;
}

static inline void urq_string_conf_free_inplace(urq_string_conf_t *self)
{
    // if (self->font != NULL) {
    //     urq_font_free_inplace(self->font);
    //     urq_free(self->font);
    //     self->font = NULL;
    // }

    if (self->value != NULL) {
        urq_free((void *)self->value);
    }
}
#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ_CONF__WIDGET__STRING_H
