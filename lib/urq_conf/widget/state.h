#pragma once

#include "urq/compile.h"
#include "urq_conf/datastruct/list/bool.h"
#include "urq_conf/datastruct/list/i16.h"
#include <stdbool.h>
#include <stdint.h>

#ifndef URQ_CONF__WIDGET__STATE_H
#define URQ__WIDGET_STATE_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 状态
typedef int8_t urq_state_t;

/// @brief 状态数量
typedef int8_t urq_state_count_t;

typedef enum {
    URQ_NO_STATE_WAY_UNSPECIFIED = 0, // 未设置，则默认使用最后一个状态
    URQ_NO_STATE_WAY_USE_LAST = 1,    // 用最后一个状态
    URQ_NO_STATE_WAY_USE_BLANK = 2    // 用空白设置或默认设置
} urq_no_state_way_t;

typedef struct {
    /// @brief 状态值等值匹配
    urq_i16_list_t *state_match_value;
    /// @brief 状态值范围匹配
    urq_i16_list_t *state_range_value;
    /// @brief 状态值组合
    urq_bool_list_t *state_combine_bit;
    /// @brief 没有状态枚举
    urq_no_state_way_t no_state_way;
    /// @brief 状态值偏移
    int16_t state_offset_value;
    /// @brief 自动切换时间
    uint16_t auto_switch_time;
    /// @brief 状态数量
    uint8_t state_count;
    /// @brief 错误状态
    uint8_t error_state;
    /// @brief 当前状态
    urq_state_t cur_state;
    // @brief 自动切换状态
    bool auto_switch;
} urq_widget_state_property_t;

/// @brief 未定义状态
#define URQ_WIDGET_STATE_UNDEFINED -1

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
/// @brief 默认状态
extern urq_state_t URQ_STATE_DEFAULT;
#else
/// @brief 默认状态
#define URQ_STATE_DEFAULT 0
#endif

static inline void urq_widget_state_property_init_inplace(
    urq_widget_state_property_t *script) __attribute__((__nonnull__(1)));

static inline void urq_widget_state_property_free_inplace(
    urq_widget_state_property_t *script) __attribute__((__nonnull__(1)));

/// @brief 检查状态是否可以忽略
/// @param current 当前状态
/// @param next    下一个状态
/// @returns 是否可以忽略
static inline bool urq_widget_state_can_ignore(
    urq_state_t current, urq_state_t next);

// ================================= impl =================================

//

static inline bool urq_widget_state_can_ignore(
    urq_state_t current, urq_state_t next)
{
    if (next < 0 || next == current) {
        return true;
    }
    return false;
}

void urq_widget_state_property_init_inplace(urq_widget_state_property_t *self)
{
    //
    //
    self->auto_switch = false;
    self->error_state = 0;
    self->auto_switch_time = 0;
    self->state_count = 0;
    self->state_offset_value = 0;
    self->state_match_value = NULL;
    self->state_range_value = NULL;
    self->state_combine_bit = NULL;
    self->no_state_way = URQ_NO_STATE_WAY_UNSPECIFIED;
}

void urq_widget_state_property_free_inplace(urq_widget_state_property_t *self)
{ //
    if (self->state_match_value != NULL) {
        urq_i16_list_free_inplace(self->state_match_value);
        urq_free(self->state_match_value);
    }
    if (self->state_range_value != NULL) {
        urq_i16_list_free_inplace(self->state_range_value);
        urq_free(self->state_range_value);
    }
    if (self->state_combine_bit != NULL) {
        urq_bool_list_free_inplace(self->state_combine_bit);
        urq_free(self->state_combine_bit);
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ__STATE_H
