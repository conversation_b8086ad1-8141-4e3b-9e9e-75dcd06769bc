#pragma once

#include "lvgl.h"
#include "urq/preload.h"
#include "urq_conf/datastruct/list/text.h"
#include <stdint.h>

#ifndef URQ_CONF__WIDGET__LABLE_H
#define URQ_CONF__WIDGET__LABLE_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 文本
    urq_text_t *text;
} urq_label_widget_conf_t;

/// @brief 原地初始化 label 组件配置
static inline void urq_label_widget_conf_init_inplace(
    urq_label_widget_conf_t *self) __attribute__((__nonnull__(1)));

/// @brief 原地释放 label 组件配置
static inline void urq_label_widget_conf_free_inplace(
    urq_label_widget_conf_t *self) __attribute__((__nonnull__(1)));

// impl

void urq_label_widget_conf_init_inplace(urq_label_widget_conf_t *self)
{
    self->text = NULL;
}

void urq_label_widget_conf_free_inplace(urq_label_widget_conf_t *self)
{
    if (self->text != NULL) {
        urq_text_free_inplace(self->text);
        urq_free(self->text);
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__WIDGET__LABLE_H
