
#pragma once

#include <stddef.h>

#ifndef URQ_CONF__WIDGET__PROPERTY_TYPE_H
#define URQ_CONF__WIDGET__PROPERTY_TYPE_H

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    URQ_PROPERTY_ID_BIT_STATE = 0,
    /// 位置LEFT
    URQ_PROPERTY_ID_LOCATION_LEFT = 1,
    /// 位置TOP
    URQ_PROPERTY_ID_LOCATION_TOP = 2,
    /// 宽
    URQ_PROPERTY_ID_SIZE_WIDTH = 3,
    /// 高
    URQ_PROPERTY_ID_SIZE_HEIGHT = 4,
    /// 控制地址的条件1(有数组)
    URQ_PROPERTY_ID_ENABLE_CONTROL_COMPARE_LEFT_VALUE = 5,
    /// 控制地址的条件2(有数组)
    URQ_PROPERTY_ID_ENABLE_CONTROL_COMPARE_RIGHT_VALUE1 = 6,
    /// 控制地址的条件2(有数组)
    URQ_PROPERTY_ID_ENABLE_CONTROL_COMPARE_RIGHT_VALUE2 = 7,
    /// 当前状态值
    URQ_PROPERTY_ID_STATE_VALUE = 8,
    /// 当前显示值
    URQ_PROPERTY_ID_DISPLAY_VALUE = 9,
    /// 动作中的页面跳转ID
    URQ_PROPERTY_ID_ACTION_PAGE_ID = 10,
    /// 动作中位操作写入地址
    URQ_PROPERTY_ID_ACTION_BIT_WRITE_ADDRESS = 11,
    /// 动作中位操作脉冲宽度
    URQ_PROPERTY_ID_ACTION_BIT_PULSE_WIDTH = 12,
    /// 动作中字操作写入地址
    URQ_PROPERTY_ID_ACTION_WORD_WRITE_ADDRESS = 13,
    /// 动作中字操作写入值或加减值
    URQ_PROPERTY_ID_ACTION_WORD_WRITE_VALUE = 14,
    /// 动作中字操作最大值
    URQ_PROPERTY_ID_ACTION_WORD_MAX_VALUE = 15,
    /// 动作中字操作最小值
    URQ_PROPERTY_ID_ACTION_WORD_MIN_VALUE = 16,
    /// 截屏动作中的页面ID
    URQ_PROPERTY_ID_ACTION_SCREENSHOT_PAGE_ID = 19,
    /// 截屏动作中的保存路径
    URQ_PROPERTY_ID_ACTION_SCREENSHOT_SAVE_PATH = 20,
    /// 截屏动作中的文件名
    URQ_PROPERTY_ID_ACTION_SCREENSHOT_FILE_NAME = 21,
    /// 复制数据动作中的源地址
    URQ_PROPERTY_ID_ACTION_COPY_DATA_SOURCE_ADDRESS = 22,
    /// 复制数据动作中的目标地址
    URQ_PROPERTY_ID_ACTION_COPY_DATA_TARGET_ADDRESS = 23,
    /// 复制数据动作中的数据长度
    URQ_PROPERTY_ID_ACTION_COPY_DATA_LENGTH = 24,
    /// 播放声音中的声音ID
    URQ_PROPERTY_ID_ACTION_PLAY_SOUND_SOUND_ID = 25,
    /// 播放声音中的持续时长
    URQ_PROPERTY_ID_ACTION_PLAY_SOUND_DURATION = 26,
    /// 延时动作中的延时时长
    URQ_PROPERTY_ID_ACTION_DELAY_MILLISECONDS = 27,
    /// 操作记录中的文本(多语言要用JSON)
    URQ_PROPERTY_ID_ACTION_LOG_TEXT = 28,
    /// 输入动作中的写入地址
    URQ_PROPERTY_ID_ACTION_INPUT_WRITE_ADDRESS = 29,
    /// 输入动作中的最大值
    URQ_PROPERTY_ID_ACTION_INPUT_MAX_VALUE = 30,
    /// 输入动作中的最小值
    URQ_PROPERTY_ID_ACTION_INPUT_MIN_VALUE = 31,
    /// 输入动作中的最短长度
    URQ_PROPERTY_ID_ACTION_INPUT_MIN_LENGTH = 32,
    /// 输入动作中的最长长度
    URQ_PROPERTY_ID_ACTION_INPUT_MAX_LENGTH = 33,
    /// MQTT推送中的主题
    URQ_PROPERTY_ID_ACTION_PUSH_MQTT_TOPIC = 34,
    /// MQTT推送中的消息
    URQ_PROPERTY_ID_ACTION_PUSH_MQTT_MESSAGE = 35,
    /// HTTP调用中的API
    URQ_PROPERTY_ID_ACTION_HTTP_API = 36,
    /// 确认弹窗中的页面ID
    URQ_PROPERTY_ID_CONFIRM_PAGE_ID = 37,
    /// 按下时的声音反馈
    URQ_PROPERTY_ID_PRESS_SOUND_ID = 38,
    /// 按下时的声音反馈持续时长
    URQ_PROPERTY_ID_PRESS_SOUND_DURATION = 39,
    /// 样式背景色（通过这个一定是颜色值，不会是主题颜色）
    URQ_PROPERTY_ID_STYLE_BACKGROUND_COLOR = 40,
} urq_property_id_t;

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__WIDGET__PROPERTY_TYPE_H
