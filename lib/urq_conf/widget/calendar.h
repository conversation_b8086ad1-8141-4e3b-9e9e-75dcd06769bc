#pragma once

#include "urq_conf/common/color.h"
#include "urq_conf/style/font.h"

#ifndef URQ_CONF__WIDGET__CALENDAR_H
#define URQ_CONF__WIDGET__CALENDAR_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    /// @brief
    URQ_DATE_SHOW_TYPE_UNSPECIFIED,
    /// @brief 使用下拉列表显示
    URQ_DATE_SHOW_TYPE_DROPDOWN,
    /// @brief 使用箭头移动
    URQ_DATE_SHOW_TYPE_ARROW,
} urq_date_show_type_t;

typedef struct {
    /// @brief 字体
    // urq_font_t *font;
    /// @brief 高亮日期
    lv_calendar_date_t *hightlight_date;

    /// @brief 星期背景颜色
    urq_color_ref_t week_bg_color;
    /// @brief 星期名称字体颜色
    urq_color_ref_t week_font_color;

    /// @brief 今天颜色
    urq_color_ref_t today_bg_color;
    /// @brief 今天字体颜色
    urq_color_ref_t today_font_color;

    /// @brief 特殊日期高亮颜色
    urq_color_ref_t highlight_color;
    /// @brief 特殊日期高亮字体颜色
    urq_color_ref_t highlight_font_color;
    /// @brief 显示类型
    urq_date_show_type_t type;
    /// @brief 高亮日期数量
    uint16_t hightlight_date_num;

} urq_calendar_widget_conf_t;

static inline void urq_calendar_widget_conf_init_inplace(
    urq_calendar_widget_conf_t *self)
{
    self->type = URQ_DATE_SHOW_TYPE_UNSPECIFIED;
    self->hightlight_date = NULL;
    self->hightlight_date_num = 0;
    urq_color_ref_init_inplace(&self->week_bg_color);
    urq_color_ref_init_inplace(&self->week_font_color);
    urq_color_ref_init_inplace(&self->today_bg_color);
    urq_color_ref_init_inplace(&self->today_font_color);
    urq_color_ref_init_inplace(&self->highlight_color);
    urq_color_ref_init_inplace(&self->highlight_font_color);
    // self->font = NULL;
}

static inline void urq_calendar_widget_conf_free_inplace(
    urq_calendar_widget_conf_t *self)
{
    // if (self->font != NULL) {
    //     urq_font_free_inplace(self->font);
    //     urq_free(self->font);
    // }
    if (self->hightlight_date != NULL) {
        urq_free(self->hightlight_date);
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__WIDGET__CALENDAR_H
