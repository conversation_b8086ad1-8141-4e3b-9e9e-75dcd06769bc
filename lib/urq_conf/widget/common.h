#pragma once

#include "urq_conf/common/confirm_win.h"
#include "urq_conf/datastruct/list/graphic.h"
#include "urq_conf/datastruct/list/style.h"
#include "urq_conf/datastruct/list/text.h"
#include "urq_conf/widget/control.h"
#include "urq_conf/widget/id.h"
#include "urq_conf/widget/state.h"

#ifndef URQ_CONF__WIDGET__COMMON_H
#define URQ_CONF__WIDGET__COMMON_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    // 继承属性
    // 控制属性
    // 状态值匹配
    /// @brief 执行前确认弹窗
    // urq_confirm_param_t *confirm_param;
    /// @brief 声音反馈
    // urq_action_t *play_sound;
    /// @brief 确认窗口
    urq_confirm_win_t confirm_win;
    /// @brief 条件控制
    urq_widget_control_conf_t *control;
    /// @brief 样式
    urq_style_ptr_list_t *styles;
    /// @brief 图片
    urq_graphic_ptr_list_t *graphics;
    /// @brief 文本
    urq_text_ptr_list_t *texts;
    /// @brief 状态属性
    urq_widget_state_property_t *stat_property;
    // 组件ID
    urq_widget_id_t id;
    /// @brief 最小按压时间
    uint16_t min_press_time;
    /// @brief 最小按压间隔
    uint16_t min_press_interval;
    /// @brief 声音反馈
    bool sound_feedback;
    /// @brief 动作
    bool has_action;
} urq_widget_common_conf_t;

static inline void urq_widget_common_conf_init_inplace(
    urq_widget_common_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_widget_common_conf_free_inplace(
    urq_widget_common_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_widget_common_conf_init_inplace(urq_widget_common_conf_t *self)
{
    self->min_press_time = 0;
    self->min_press_interval = 0;
    self->styles = NULL;
    self->graphics = NULL;
    self->stat_property = NULL;
    self->control = NULL;
    self->has_action = false;
    self->texts = NULL;
    urq_confirm_win_init_inplace(&self->confirm_win);
}

void urq_widget_common_conf_free_inplace(urq_widget_common_conf_t *self)
{
    if (self->styles != NULL) {
        urq_style_ptr_list_free_inplace(self->styles);
        urq_free(self->styles);
    }
    if (self->graphics != NULL) {
        urq_graphic_ptr_list_free_inplace(self->graphics);
        urq_free(self->graphics);
    }
    if (self->stat_property != NULL) {
        urq_widget_state_property_free_inplace(self->stat_property);
        urq_free(self->stat_property);
    }
    if (self->control != NULL) {
        urq_widget_control_conf_free_inplace(self->control);
        urq_free(self->control);
    }
    if (self->texts != NULL) {
        urq_text_ptr_list_free_inplace(self->texts);
        urq_free(self->texts);
    }
}

#ifdef __cplusplus
}
#endif
#endif
