#pragma once

#include "urq_conf/common/graphic.h"
#include "urq_conf/common/position.h"
#include "urq_conf/common/size.h"
#include "urq_conf/style/style.h"

#ifndef URQ_CONF__WIDGET__IMAGE_H
#define URQ_CONF__WIDGET__IMAGE_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 图片配置
typedef struct {
    bool auto_generated;
} urq_image_widget_conf_t;

static inline void urq_image_widget_conf_init_inplace(
    urq_image_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_image_widget_conf_free_inplace(
    urq_image_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================
void urq_image_widget_conf_init_inplace(urq_image_widget_conf_t *self)
{
    self->auto_generated = false;
}

void urq_image_widget_conf_free_inplace(urq_image_widget_conf_t *self)
{
    self->auto_generated = false;
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__WIDGET__IMAGE_H
