
#pragma once

#include "urq/preload.h"
#include "urq_conf/data/condition_list.h"
#include "urq_conf/datastruct/list/u8.h"

#ifndef URQ_CONF__WIDGET__CONTROL_H
#define URQ_CONF__WIDGET__CONTROL_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 控制端位置
typedef enum {
    /// 未设置
    TERMINAL_LOCATION_UNSPECIFIED = 0,
    /// 本机
    TERMINAL_LOCATION_LOCAL = 1,
    /// 局域网
    TERMINAL_LOCATION_LAN = 2,
    /// 互联网
    TERMINAL_LOCATION_WAN = 3,
    /// 局域网(匿名访客)
    TERMINAL_LOCATION_LAN_GUEST = 4,
    /// 局域网(已登录用户)
    TERMINAL_LOCATION_LAN_USER = 5,
    /// 互联网(匿名访客)
    TERMINAL_LOCATION_WAN_GUEST = 6,
    /// 互联网(已登录用户)
    TERMINAL_LOCATION_WAN_USER = 7,
} urq_terminal_location_t;

/// @brief 控制端类型
typedef enum {
    /// 未设置
    TERMINAL_TYPE_UNSPECIFIED = 0,
    /// 本机
    TERMINAL_TYPE_LOCAL = 1,
    /// APP
    TERMINAL_TYPE_APP = 2,
    /// PC
    TERMINAL_TYPE_PC = 3,
    /// 其他HMI
    TERMINAL_TYPE_HMI = 4,
    /// 电视大屏
    TERMINAL_TYPE_TV = 5,
} urq_terminal_type_t;

/// @brief 禁用外观时显示
typedef enum {
    /// 未设置
    DISABLE_WIDGET_MODE_UNSPECIFIED = 0,
    /// 禁用时隐藏
    DISABLE_WIDGET_MODE_HIDE = 1,
    /// 禁用但不变灰
    DISABLE_WIDGET_MODE_NORMAL = 2,
    /// 禁用变灰
    DISABLE_WIDGET_MODE_GRAY = 3,
    /// 禁用显示禁用图标
    DISABLE_WIDGET_MODE_ICON = 4,
} urq_disable_widget_mode_t;

typedef struct {
    /// @brief 条件控制
    // urq_data_condition_list_t *data_conditions;
    /// @brief 限控制
    urq_u8_list_t *permission_bits;
    /// @brief 控制端位置
    urq_u8_list_t *terminal_location;
    /// @brief 控制端类型
    urq_u8_list_t *terminal_type;
    /// @brief 禁用外观时显示
    urq_disable_widget_mode_t disable_widget_mode;
    /// @brief 页面ID
    urq_page_id_t page_id;
    /// @brief 条件控制
    bool condition;
} urq_widget_control_conf_t;

static inline void urq_widget_control_conf_init_inplace(
    urq_widget_control_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_widget_control_conf_free_inplace(
    urq_widget_control_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_widget_control_conf_init_inplace(urq_widget_control_conf_t *self)
{
    self->permission_bits = NULL;
    self->terminal_location = NULL;
    self->terminal_type = NULL;
    self->disable_widget_mode = DISABLE_WIDGET_MODE_UNSPECIFIED;
    self->page_id = 0;
    self->condition = false;
}

void urq_widget_control_conf_free_inplace(urq_widget_control_conf_t *self)
{
    //
    if (self->permission_bits != NULL) {
        urq_u8_list_free_inplace(self->permission_bits);
    }
    if (self->terminal_location != NULL) {
        urq_u8_list_free_inplace(self->terminal_location);
    }
    if (self->terminal_type != NULL) {
        urq_u8_list_free_inplace(self->terminal_type);
    }
}

#ifdef __cplusplus
}
#endif
#endif
