#pragma once

#include "urq/preload.h"
#include "urq_conf/widget/option_list.h"

#ifndef URQ_CONF__WIDGET__ROLLER_LIST_H
#define URQ_CONF__WIDGET__ROLLER_LIST_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 列表
    urq_option_list_conf_t list;
    /// @brief 滚动模式
    lv_roller_mode_t mode;
    /// @brief 显示数量
    uint8_t view_count;
    /// @brief 默认索引
    uint8_t def_index;
} urq_rollerList_widget_conf_t;

static inline void urq_rollerList_widget_conf_init_inplace(
    urq_rollerList_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_rollerList_widget_conf_free_inplace(
    urq_rollerList_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_rollerList_widget_conf_init_inplace(urq_rollerList_widget_conf_t *self)
{
    self->mode = LV_ROLLER_MODE_INFINITE;
    self->view_count = 0;
    self->def_index = 0;
    urq_option_list_conf_init_inplace(&self->list);
}

void urq_rollerList_widget_conf_free_inplace(urq_rollerList_widget_conf_t *self)
{
    urq_option_list_conf_free_inplace(&self->list);
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__WIDGET__ROLLER_LIST_H
