#pragma once

#include "urq/preload.h"
#include "urq_conf/common/size.h"

#ifndef URQ_CONF__WIDGET__KEYBOARD__INFO_H
#define URQ_CONF__WIDGET__KEYBOARD__INFO_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 最大值或最长长度元件id
    int32_t max_value_widget_id;
    /// @brief 最小值或最短长度元件id
    int32_t min_value_widget_id;
    /// @brief 当前值元件id
    int32_t current_value_widget_id;
    /// @brief 候选字区域坐标(相对键盘窗口坐标)
    lv_point_t candidate_location;
    /// @brief 候选字区域大小(相对键盘窗口坐标)
    urq_size_t candidate_size;
} urq_keyboard_info_t;

static inline void urq_keyboard_info_init_inplace(urq_keyboard_info_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_keyboard_info_free_inplace(urq_keyboard_info_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_keyboard_info_init_inplace(urq_keyboard_info_t *self)
{
    self->max_value_widget_id = -1;
    self->min_value_widget_id = -1;
    self->current_value_widget_id = -1;
    self->candidate_location.x = 0;
    self->candidate_location.y = 0;
    self->candidate_size.w = 0;
    self->candidate_size.h = 0;
}

void urq_keyboard_info_free_inplace(urq_keyboard_info_t *self)
{
    urq_used(self);
}

#ifdef __cplusplus
}
#endif

#endif
