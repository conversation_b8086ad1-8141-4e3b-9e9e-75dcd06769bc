#pragma once

#include "urq_conf/datastruct/list/graphic.h"
#include "urq_conf/datastruct/list/style.h"
#include <lvgl/src/font/lv_symbol_def.h>
#include <stddef.h>
#include <string.h>

#ifndef URQ_CONF__WIDGET__KEYBOARD__BASE_CONF_H
#define URQ_CONF__WIDGET__KEYBOARD__BASE_CONF_H

typedef enum {
    /// @brief 未设置
    URQ_BUTTON_FUNCTION_CODE_UNSPECIFIED = 0,
    /// @brief 按钮行结束，换新行
    URQ_BUTTON_FUNCTION_CODE_END_LINE = 1,
    /// @brief 清空
    URQ_BUTTON_FUNCTION_CODE_CLEAR = 2,
    /// @brief 确定
    URQ_BUTTON_FUNCTION_CODE_OK = 3,
    /// @brief 取消
    URQ_BUTTON_FUNCTION_CODE_CANCEL = 4,
    /// @brief ENTER
    URQ_BUTTON_FUNCTION_CODE_ENTER = 5,
    /// @brief BACKSPACE
    URQ_BUTTON_FUNCTION_CODE_BACKSPACE = 6,
    /// @brief DELETE
    URQ_BUTTON_FUNCTION_CODE_DELETE = 7,
    /// @brief ESC
    URQ_BUTTON_FUNCTION_CODE_ESCAPE = 8,
    /// @brief 数字加
    URQ_BUTTON_FUNCTION_CODE_NUMBER_ADD = 9,
    /// @brief 数字减
    URQ_BUTTON_FUNCTION_CODE_NUMBER_SUB = 10,
    /// @brief UP
    URQ_BUTTON_FUNCTION_CODE_UP = 11,
    /// @brief DOWN
    URQ_BUTTON_FUNCTION_CODE_DOWN = 12,
    /// @brief LEFT
    URQ_BUTTON_FUNCTION_CODE_LEFT = 13,
    /// @brief RIGHT
    URQ_BUTTON_FUNCTION_CODE_RIGHT = 14,
    /// @brief HOME
    URQ_BUTTON_FUNCTION_CODE_HOME = 15,
    /// @brief END
    URQ_BUTTON_FUNCTION_CODE_END = 16,
    /// @brief PAGE_UP
    URQ_BUTTON_FUNCTION_CODE_PAGE_UP = 17,
    /// @brief PAGE_DOWN
    URQ_BUTTON_FUNCTION_CODE_PAGE_DOWN = 18
} urq_keyboard_func_code_t;

static const char *urq_keyboard_funccode[] = {
    "UNSPECIFIED",
    LV_SYMBOL_NEW_LINE,
    "CLEAR",
    LV_SYMBOL_OK,
    "CANCEL",
    "ENTER",
    LV_SYMBOL_BACKSPACE,
    "DELETE",
    "ESC",
    LV_SYMBOL_PLUS,
    LV_SYMBOL_MINUS,
    LV_SYMBOL_UP,
    LV_SYMBOL_DOWN,
    LV_SYMBOL_LEFT,
    LV_SYMBOL_RIGHT,
    LV_SYMBOL_HOME,
    "END",
    "PAGE UP",
    "PAGE DOWN"};

typedef struct { /// @brief 按钮宽度比列
    /// @brief 按钮样式
    urq_style_ptr_list_t *styles;
    /// @brief 按钮图形
    urq_graphic_ptr_list_t *graphics;
} urq_keyboard_base_conf_t;

static inline void urq_keyboard_base_conf_init_inplace(
    urq_keyboard_base_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_keyboard_base_conf_free_inplace(
    urq_keyboard_base_conf_t *self) __attribute__((__nonnull__(1)));

static inline const char *urq_keyboard_get_funccode_str(
    urq_keyboard_func_code_t code);

// ================= impl =============================

void urq_keyboard_base_conf_init_inplace(urq_keyboard_base_conf_t *self)
{
    self->styles = NULL;
    self->graphics = NULL;
}

void urq_keyboard_base_conf_free_inplace(urq_keyboard_base_conf_t *self)
{
    // self->width = 1;
    if (self->styles != NULL) {
        urq_style_ptr_list_free_inplace(self->styles);
        urq_free(self->styles);
    }
    if (self->graphics != NULL) {
        urq_graphic_ptr_list_free_inplace(self->graphics);
        urq_free(self->graphics);
    }
}

const char *urq_keyboard_get_funccode_str(urq_keyboard_func_code_t code)
{
    if (code < 0 || code >= sizeof(urq_keyboard_funccode) /
                                sizeof(urq_keyboard_funccode[0])) {
        return NULL;
    }
    return urq_keyboard_funccode[code];
}

#endif // URQ_CONF__WIDGET__KEYBOARD__BASE_CONF_H
