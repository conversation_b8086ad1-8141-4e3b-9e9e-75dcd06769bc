#pragma once

#include "urq_conf/common/cursor.h"
#include "urq_conf/common/scale.h"
#include "urq_conf/common/scale_label.h"
#include "urq_conf/common/scroll_direction.h"
#include "urq_conf/common/time.h"
#include "urq_conf/datastruct/list/series.h"
#include "urq_conf/style/font.h"
#include "urq_conf/widget/curve/type.h"

#ifndef URQ_CONF__WIDGET__CURVE_H
#define URQ_CONF__WIDGET__CURVE_H

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    URQ_CURVE_SHOW_TYPE_NONE = 0,
    /// @brief 显示点
    URQ_CURVE_SHOW_TYPE_POINT = 1,
    /// @brief 像素距离
    URQ_CURVE_SHOW_TYPE_PIXEL_DISTANCE = 2,
    /// @brief 时间范围
    URQ_CURVE_SHOW_TYPE_TIME_RANGE = 3,
} urq_curve_show_type_t;

typedef struct {
    /// @brief 数据序列
    urq_curve_series_ptr_list_t series_conf;
    /// @brief 游标
    urq_curve_cursor_t *cursor_conf;
    /// @brief [optional]X轴 刻度
    urq_scale_t *x_scale;
    /// @brief [optional]Y轴 刻度
    urq_scale_t *y_scale;
    /// @brief [optional]Y2轴 刻度
    urq_scale_t *y_scale2;
    /// @brief [optional]X轴 标签
    urq_curve_scale_label_t *x_label;
    /// @brief [optional]Y轴 标签
    urq_curve_scale_label_t *y_label;
    /// @brief [optional]Y2轴 标签
    urq_curve_scale_label_t *y_label2;

    /// @brief [optional]X轴字体
    // urq_font_t *font;
    /// @brief 曲线类型
    urq_curve_type_t type;
    /// @brief 数据更新模式
    lv_chart_update_mode_t data_update_mode;
    /// @brief 绘制方向
    urq_scroll_direction_t draw_direction;
    /// @brief 实时时间格式
    urq_time_format_t lable_time_format;
    /// @brief 实时日期格式
    urq_date_format_t lable_date_format;
    /// @brief 实时时间格式
    urq_time_format_t time_format;
    /// @brief 实时日期格式
    urq_date_format_t date_format;
    /// @brief 时间范围
    urq_curve_show_type_t show_range_type;
    /// @brief 采样点数/时间范围
    uint16_t range_value;
    /// @brief 点间距
    lv_coord_t point_step;
    /// @brief 偏移量
    uint8_t offset_x;
    /// @brief 偏移量
    uint8_t offset_x2;
    /// @brief 偏移量
    uint8_t offset_y;
    /// @brief 数据恢复时间
    uint8_t data_recovery_time;
    /// @brief 是否显示实时时间
    bool use_current_time;
} urq_curve_widget_conf_t;

static inline void urq_curve_widget_conf_init_inplace(
    urq_curve_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_curve_widget_conf_free_inplace(
    urq_curve_widget_conf_t *self) __attribute__((__nonnull__(1)));

void urq_curve_widget_conf_init_inplace(urq_curve_widget_conf_t *self)
{
    // self->font = NULL;
    self->type = URQ_CURVE_TYPE_LINE;
    self->data_update_mode = 0;
    self->draw_direction = URQ_SCROLL_DIRECTION_LEFT_TO_RIGHT;
    self->cursor_conf = NULL;
    self->x_scale = NULL;
    self->y_scale = NULL;
    self->y_scale2 = NULL;
    self->x_label = NULL;
    self->y_label = NULL;
    self->y_label2 = NULL;
    self->use_current_time = false;
    self->time_format = URQ_TIME_FORMAT_HMS;
    self->date_format = URQ_DATE_FORMAT_MDY;
    urq_curve_series_ptr_list_init_inplace(&self->series_conf);
    self->data_recovery_time = 0;
}

void urq_curve_widget_conf_free_inplace(urq_curve_widget_conf_t *self)
{
    // if(self->font != NULL) {
    //     urq_font_free_inplace(self->font);
    //     urq_free(self->font);
    // }
    if (self->x_scale != NULL) {
        urq_curve_scale_free_inplace(self->x_scale);
        urq_free(self->x_scale);
    }
    if (self->y_scale != NULL) {
        urq_curve_scale_free_inplace(self->y_scale);
        urq_free(self->y_scale);
    }
    if (self->y_scale2 != NULL) {
        urq_curve_scale_free_inplace(self->y_scale2);
        urq_free(self->y_scale2);
    }
    if (self->cursor_conf != NULL) {
        urq_curve_cursor_free_inplace(self->cursor_conf);
        urq_free(self->cursor_conf);
    }
    if (self->x_label != NULL) {
        urq_curve_scale_label_free_inplace(self->x_label);
        urq_free(self->x_label);
    }
    if (self->y_label != NULL) {
        urq_curve_scale_label_free_inplace(self->y_label);
        urq_free(self->y_label);
    }
    if (self->y_label2 != NULL) {
        urq_curve_scale_label_free_inplace(self->y_label2);
        urq_free(self->y_label2);
    }
}

#ifdef __cplusplus
}
#endif
#endif
