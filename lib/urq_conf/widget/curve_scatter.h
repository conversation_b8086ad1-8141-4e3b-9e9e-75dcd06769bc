
#pragma once

#include "urq_conf/widget/curve.h"

#ifndef URQ_CONF__WIDGET__CURVE_SCATTER_H
#define URQ_CONF__WIDGET__CURVE_SCATTER_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 曲线
    urq_curve_widget_conf_t *conf;
} urq_scatter_curve_widget_conf_t;

static inline void urq_scatter_curve_widget_conf_init_inplace(
    urq_scatter_curve_widget_conf_t *self)
{
    self->conf = NULL;
}

static inline void urq_scatter_curve_widget_conf_free_inplace(
    urq_scatter_curve_widget_conf_t *self)
{
    self->conf = NULL;
}

#ifdef __cplusplus
}
#endif
#endif
