#pragma once

#include "urq_conf/common/arc.h"
#include "urq_conf/style/line.h"
#include <stdint.h>
#include <stdlib.h>

#ifndef URQ_CONF__WIDGET__ARC_H
#define URQ_CONF__WIDGET__ARC_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    // 未设置
    URQ_ARC_TYPE_UNSPECIFIED = 0,
    // 弧形
    URQ_ARC_TYPE_ARC = 1,
    // 扇形
    URQ_ARC_TYPE_SECTOR = 2,
    // 扇环
    URQ_ARC_TYPE_RING = 3
} urq_arc_type_t;

typedef struct {
    /// @brief 弧形
    urq_arc_t arc;
    /// @brief 内弧
    urq_arc_t inner_arc;
    /// @brief 形状类型
    urq_arc_type_t type;
    /// TODO @brief 旋转角度
    float rotate_angle;
} urq_arc_widget_conf_t;

static inline void urq_arc_widget_conf_init_inplace(urq_arc_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_arc_widget_conf_free_inplace(urq_arc_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_arc_widget_conf_init_inplace(urq_arc_widget_conf_t *self)
{
    self->type = URQ_ARC_TYPE_UNSPECIFIED;
    self->rotate_angle = 0;
    urq_arc_init_inplace(&self->arc);
    urq_arc_init_inplace(&self->inner_arc);
}

void urq_arc_widget_conf_free_inplace(urq_arc_widget_conf_t *self)
{
    urq_arc_free_inplace(&self->arc);
    urq_arc_free_inplace(&self->inner_arc);
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__WIDGET__ARC_H
