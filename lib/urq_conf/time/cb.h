#pragma once

#include "urq/cb/cb.h"
#include "urq/time/time.h"

#ifndef URQ_CONF__TIME__CB_H
#define URQ_CONF__TIME__CB_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 添加回调，参数： 0个
/// @param ms 毫秒
/// @param cb 回调函数
/// @return void
void *urq_time_cb_add0(urq_time_t ms, urq_cb0_t cb);

/// @brief 添加回调，参数： 1个
/// @param ms 毫秒
/// @param cb 回调函数
/// @param free 释放函数
/// @param arg1 参数1
/// @return void
void *urq_time_cb_add1(urq_time_t ms, urq_cb1_t cb, urq_cb1_t free, void *arg1);

/// @brief 添加回调，参数： 2个
/// @param ms   毫秒
/// @param cb   回调函数
/// @param free 释放函数
/// @param arg1 参数1
/// @param arg2 参数2
/// @return void
void *urq_time_cb_add2(
    urq_time_t ms, urq_cb2_t cb, urq_cb2_t free, void *arg1, void *arg2);

/// @brief 执行定时回调
/// @return void
void urq_time_cb_poll(void);

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__TIME__CB_H
