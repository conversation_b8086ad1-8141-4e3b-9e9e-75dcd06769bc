#pragma once

#include "urq/frame/frame.h"
#include "urq/time/time.h"
#include "urq_conf/time/cb.h"

#ifndef URQ_CONF__TIME__UPDATE_H
#define URQ_CONF__TIME__UPDATE_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 更新时间
static inline void urq_time_update()
{
    urq_frame_total += 1;
    urq_time_cb_poll();

    struct timespec ts;
    clock_gettime(CLOCK_REALTIME, &ts);
    urq_time_now_ms =
        ts.tv_sec * 1000 + ts.tv_nsec / 1000000 + urq_time_offset_ms;
}

#ifdef __cplusplus
}
#endif
#endif // URQ_CONF__TIME__UPDATE_H
