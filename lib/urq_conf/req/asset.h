#pragma once

#include "urq_conf/conn/conn.h"
#include "urq_conf/req/header.h"

#ifndef URQ_CONF__REQ__ASSET_H
#define URQ_CONF__REQ__ASSET_H

#ifdef __cplusplus
extern "C" {
#endif

static inline int urq_req_wirte_set_asset(urq_conn_t *const conn)
{

    const uint32_t body_size = 0;
    const uint32_t size = body_size + urq_req_header_size(body_size);

    uint8_t buffer[size];
    urq_buf_writer_t *buf = urq_buf_writer_make(size, buffer);

    urq_req_header_set(buf, URQ_REQ_OP_ASSET, body_size);
    return urq_conn_write(conn, size, buffer);
}

#ifdef __cplusplus
}
#endif // #ifdef __cplusplus
#endif // #ifndef URQ_CONF__REQ__ASSET_H
