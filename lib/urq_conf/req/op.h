#pragma once

#ifndef URQ_CONF__REQ__OP_H
#define URQ_CONF__REQ__OP_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    /// @brief 关闭连接
    URQ_REQ_OP_CLOSE = 0,
    /// @brief 获取资源
    URQ_REQ_OP_ASSET = 1,
    /// @brief 写入单个变量
    URQ_REQ_OP_WRITE = 2,
    /// @brief 设置动作
    URQ_REQ_OP_SET_ACTION = 3,

    /// MAX, auto increment
    URQ_REQ_OP_MAX
} urq_req_op_t;

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ_CONF__REQ__OP_H
