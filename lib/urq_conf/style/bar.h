#pragma once

#include "urq/preload.h"
#include "urq_conf/style/style.h"

#ifndef URQ_CONF__STYLE__BAR_H
#define URQ_CONF__STYLE__BAR_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 柱状图样式
typedef struct {
    /// @brief 样式
    urq_style_t *style;
    /// @brief 圆角
    uint8_t round;
    /// @brief 宽度
    uint8_t width;
} urq_style_bar_t;

static inline void urq_style_bar_init_inplace(urq_style_bar_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_style_bar_free_inplace(urq_style_bar_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

static inline void urq_style_bar_init_inplace(urq_style_bar_t *self)
{
    self->style = NULL;
    self->round = 0;
    self->width = 0;
}

static inline void urq_style_bar_free_inplace(urq_style_bar_t *self)
{
    if (self->style != NULL) {
        urq_style_free_inplace(self->style);
        urq_free(self->style);
    }
    self->style = NULL;
}

#ifdef __cplusplus
}
#endif

#endif
