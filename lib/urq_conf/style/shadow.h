#pragma once

#include "urq/preload.h"
#include "urq_conf/common/color.h"

#ifndef URQ_CONF__STYLE__SHADOW_H
#define URQ_CONF__STYLE__SHADOW_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 阴影
typedef struct {
    /// @brief 颜色
    urq_color_ref_t bg;
    /// @brief 是否启用
    bool enable;
    /// @brief 偏移量
    int16_t offset_x;
    /// @brief 偏移量
    int16_t offset_y;
    /// @brief 扩散
    int16_t spread;
    /// @brief 模糊程度
    int16_t width;
} urq_style_shadow_t;

static inline void urq_style_shadow_init_inplace(urq_style_shadow_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_style_shadow_free_inplace(urq_style_shadow_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

static inline void urq_style_shadow_init_inplace(urq_style_shadow_t *self)
{
    urq_color_ref_init_inplace(&self->bg);
    self->enable = false;
    self->offset_x = 0;
    self->offset_y = 0;
    self->spread = 0;
    self->width = 0;
}

static inline void urq_style_shadow_free_inplace(urq_style_shadow_t *self)
{
    urq_used(self);
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ_CONF__STYLE__SHADOW_H
