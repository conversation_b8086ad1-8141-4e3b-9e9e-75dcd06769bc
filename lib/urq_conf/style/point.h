#pragma once

#include "urq/preload.h"
#include "urq_conf/style/style.h"

#ifndef URQ_CONF__STYLE__POINT_H
#define URQ_CONF__STYLE__POINT_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 点类型
typedef enum {
    /// @brief 未设置
    URQ_STYLE_POINT_TYPE_UNSPECIFIED,
    /// @brief 圆点
    URQ_STYLE_POINT_TYPE_CIRCLE,
    /// @brief 方点
    URQ_STYLE_POINT_TYPE_SQUARE,
    /// @brief 三角
    URQ_STYLE_POINT_TYPE_TRIANGLE,
    /// @brief 菱形
    URQ_STYLE_POINT_TYPE_DIAMOND,
} urq_style_point_type_t;

/// @brief 点样式
typedef struct {
    /// @brief 点类型
    urq_style_point_type_t type;
    /// @brief 边框
    urq_style_t *style;
    /// @brief 宽度或半径
    uint8_t width_or_radius;
    /// @brief 高度
    uint8_t height;
} urq_style_point_t;

static inline void urq_style_point_init_inplace(urq_style_point_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_style_point_free_inplace(urq_style_point_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

static inline void urq_style_point_init_inplace(urq_style_point_t *self)
{
    self->type = URQ_STYLE_POINT_TYPE_UNSPECIFIED;
    self->style = NULL;
    self->width_or_radius = 0;
    self->height = 0;
}

static inline void urq_style_point_free_inplace(urq_style_point_t *self)
{
    urq_used(self);
}

#ifdef __cplusplus
}
#endif

#endif
