#pragma once
#include "urq/style/line.h"
#include <stdint.h>
#include <stdlib.h>

#ifndef URQ__ARC__CONF_H
#define URQ__ARC__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    // 未设置
    URQ_ARC_TYPE_UNSPECIFIED = 0,
    // 弧形
    URQ_ARC_TYPE_ARC = 1,
    // 扇形
    URQ_ARC_TYPE_SECTOR = 2,
    // 扇环
    URQ_ARC_TYPE_RING = 3
} urq_arc_type_t;

typedef struct {
    /// @brief 形状类型
    urq_arc_type_t type;
    /// @brief 中心点
    lv_point_t center;
    /// @brief 长轴半径
    lv_coord_t lr;
    /// @brief 短轴半径
    lv_coord_t sr;
    /// @brief 内长轴半径
    lv_coord_t lr_inner;
    /// @brief 内短轴半径
    lv_coord_t sr_inner;
    /// @brief 旋转角度
    float rotate_angle;
    /// @brief 起始角度
    float start_angle;
    /// @brief 结束角度
    float end_angle;
    /// @brief 线配置
    urq_style_line_t *line;
} urq_arc_widget_conf_t;

static inline void urq_arc_widget_conf_init_inplace(urq_arc_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_arc_widget_conf_free_inplace(urq_arc_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_arc_widget_conf_init_inplace(urq_arc_widget_conf_t *self)
{
    self->type = URQ_ARC_TYPE_UNSPECIFIED;
    self->center.x = 0;
    self->center.y = 0;
    self->lr = 0;
    self->sr = 0;
    self->rotate_angle = 0;
    self->start_angle = 0;
    self->end_angle = 0;
    self->line = NULL;
}

void urq_arc_widget_conf_free_inplace(urq_arc_widget_conf_t *self)
{
    urq_used(self);
    if (self->line) {
        urq_style_line_free_inplace(self->line);
        urq_free(self->line);
    }
    self->line = NULL;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__ARC__CONF_H
