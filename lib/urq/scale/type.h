#pragma once

#ifndef URQ__SCALE__TYPE_H
#define URQ__SCALE__TYPE_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 刻度尺类型
typedef enum {
    /// @brief 水平直线刻度尺
    URQ_SCALE_TYPE_HORIZONTAL = 0,
    /// @brief 垂直直线刻度尺
    URQ_SCALE_TYPE_VERTICAL = 1,
    /// @brief 弧形刻度尺
    URQ_SCALE_TYPE_ARC = 2,
    /// @brief 圆形刻度尺
    URQ_SCALE_TYPE_CIRCLE = 3,
} urq_scale_type_t;

/// @brief 刻度尺方向
typedef enum {
    /// @brief 刻度在上方/左侧
    URQ_SCALE_DIRECTION_TOP_LEFT = 0,
    /// @brief 刻度在下方/右侧
    URQ_SCALE_DIRECTION_BOTTOM_RIGHT = 1,
    /// @brief 刻度在两侧
    URQ_SCALE_DIRECTION_BOTH = 2,
} urq_scale_direction_t;

/// @brief 刻度标签位置
typedef enum {
    /// @brief 标签在刻度外侧
    URQ_SCALE_LABEL_POSITION_OUTSIDE = 0,
    /// @brief 标签在刻度内侧
    URQ_SCALE_LABEL_POSITION_INSIDE = 1,
    /// @brief 标签在刻度中心
    URQ_SCALE_LABEL_POSITION_CENTER = 2,
} urq_scale_label_position_t;

/// @brief 刻度对齐方式
typedef enum {
    /// @brief 左对齐（水平刻度尺）/ 上对齐（垂直刻度尺）
    URQ_SCALE_ALIGNMENT_START = 0,
    /// @brief 居中对齐
    URQ_SCALE_ALIGNMENT_CENTER = 1,
    /// @brief 右对齐（水平刻度尺）/ 下对齐（垂直刻度尺）
    URQ_SCALE_ALIGNMENT_END = 2,
} urq_scale_alignment_t;

#ifdef __cplusplus
}
#endif

#endif // URQ__RULER__TYPE_H
