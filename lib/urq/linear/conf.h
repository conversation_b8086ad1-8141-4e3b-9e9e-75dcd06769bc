#pragma once

#include "urq/bit/conf.h"
#include "urq/style/line.h"
#include <stdint.h>

#ifndef URQ__LINEAR__CONF_H
#define URQ__LINEAR__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    // 未设置
    URQ_LINEAR_TYPE_UNSPECIFIED = 0,
    // 直线
    URQ_LINEAR_TYPE_LINE = 1,
    // 折线
    URQ_LINEAR_TYPE_POLYLINE = 2,
    // 多边形
    URQ_LINEAR_TYPE_POLYGON = 4
} urq_linear_type_t;

typedef struct {
    urq_linear_type_t type;
    /// @brief 线条样式
    urq_style_line_t line;
    /// @brief 线条顶点
    lv_point_t *points;
    /// @brief 线条顶点数量
    uint16_t points_size;
    /// @brief 箭头样式
    // urq_style_arrow_t arrow;
} urq_linear_widget_conf_t;

static inline void urq_linear_widget_conf_init_inplace(
    urq_linear_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_linear_widget_conf_free_inplace(
    urq_linear_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_linear_widget_conf_init_inplace(urq_linear_widget_conf_t *self)
{
    urq_style_line_init_inplace(&self->line);
    self->points = NULL;
    self->points_size = 0;
}

void urq_linear_widget_conf_free_inplace(urq_linear_widget_conf_t *self)
{
    if (self->points != NULL) {
        urq_free(self->points);
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ__BIT__CONF_H
