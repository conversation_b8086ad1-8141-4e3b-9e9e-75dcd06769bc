#pragma once

#include "urq/bit/conf.h"
#include "urq/button/conf.h"
#include "urq/calendar/conf.h"
#include "urq/clone/conf.h"
#include "urq/curve/bar_curve.h"
#include "urq/curve/conf.h"
#include "urq/curve/scatter_curve.h"
#include "urq/curve/trend_curve.h"
#include "urq/curve/xy_curve.h"
#include "urq/dropList/conf.h"
#include "urq/image/conf.h"
#include "urq/keyboard/conf.h"
#include "urq/label/conf.h"
#include "urq/linear/conf.h"
#include "urq/meter/conf.h"
#include "urq/number/conf.h"
#include "urq/rollerList/conf.h"
#include "urq/ruler/conf.h"
#include "urq/size.h"
#include "urq/string/conf.h"
#include "urq/widget/common.h"
#include "urq/widget/id.h"
#include "urq/widget/type.h"
#include "urq/word/conf.h"

#ifndef URQ__WIDGET__CONF_H
#define URQ__WIDGET__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct urq_group_widget_conf_t urq_group_widget_conf_t;

/// @brief 组件配置基类
typedef struct {
    /// @brief 组件 ID， 组件的 id 只在一个页面内唯一
    urq_widget_id_t id;
    /// @brief 组件类型
    urq_widget_type_t type;
    /// @brief 组件的位置
    lv_point_t pos;
    /// @brief 组件的大小
    urq_size_t size;
    /// @brief 组件的通用配置
    urq_widget_common_conf_t *common_conf;
    union {
        void *pointer;
        urq_number_conf_t *number;
        urq_string_conf_t *string;
        urq_label_widget_conf_t *label;
        urq_image_widget_conf_t *image;
        urq_bit_widget_conf_t *bit;
        urq_word_widget_conf_t *word;
        urq_keyboard_widget_conf_t *keyboard;
        urq_button_widget_conf_t *button;
        urq_clone_widget_conf_t *clone;
        urq_curve_widget_conf_t *curve;
        urq_trend_curve_widget_conf_t *trend_curve;
        urq_xy_curve_widget_conf_t *xy_curve;
        urq_bar_curve_widget_conf_t *bar_curve;
        urq_scatter_curve_widget_conf_t *scatter_curve;
        urq_calendar_widget_conf_t *calendar;
        urq_rollerList_widget_conf_t *roller_list;
        urq_group_widget_conf_t *group;
        urq_dropList_widget_conf_t *drop_list;
        urq_meter_widget_conf_t *meter;
        urq_ruler_widget_conf_t *ruler;
        urq_linear_widget_conf_t *linear;
    } config;
} urq_widget_conf_t;

/// @brief 原地初始化组件配置
static inline void urq_widget_conf_init_inplace(urq_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 原地析构组件配置
static inline void urq_widget_conf_free_inplace(urq_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

// ----------------------------------------------------

static inline void urq_widget_conf_init_inplace(urq_widget_conf_t *self)
{
    self->type = URQ_WIDGET_TYPE_MAX;
    self->config.pointer = NULL;
    self->common_conf = NULL;
}

static inline void urq_widget_conf_free_inplace(urq_widget_conf_t *self)
{
    switch (self->type) {
    case URQ_WIDGET_TYPE_MAX:
        return;
    case URQ_WIDGET_TYPE_NUMBER:
        urq_number_conf_free_inplace(self->config.number);
        return;
    case URQ_WIDGET_TYPE_STRING:
        urq_string_conf_free_inplace(self->config.string);
        return;
    case URQ_WIDGET_TYPE_LABEL_WIDGET:
        urq_label_widget_conf_free_inplace(self->config.label);
        return;
    case URQ_WIDGET_TYPE_IMAGE_WIDGET:
        urq_image_widget_conf_free_inplace(self->config.image);
        return;
    case URQ_WIDGET_TYPE_BIT:
        urq_bit_widget_conf_free_inplace(self->config.bit);
        return;
    case URQ_WIDGET_TYPE_WORD:
        urq_word_widget_conf_free_inplace(self->config.word);
        return;
    case URQ_WIDGET_TYPE_KEYBOARD:
        urq_keyboard_widget_conf_free_inplace(self->config.keyboard);
        return;
    case URQ_WIDGET_TYPE_BUTTON:
        urq_button_widget_conf_free_inplace(self->config.button);
        return;
    case URQ_WIDGET_TYPE_CLONE:
        urq_clone_widget_conf_free_inplace(self->config.clone);
        return;
    case URQ_WIDGET_TYPE_TREND_CURVE:
        urq_trend_curve_widget_conf_free_inplace(self->config.trend_curve);
        return;
    case URQ_WIDGET_TYPE_XY_CURVE:
        urq_xy_curve_widget_conf_free_inplace(self->config.xy_curve);
        return;
    case URQ_WIDGET_TYPE_BAR_CURVE:
        urq_bar_curve_widget_conf_free_inplace(self->config.bar_curve);
        return;
    case URQ_WIDGET_TYPE_SCATTER_CURVE:
        urq_scatter_curve_widget_conf_free_inplace(self->config.scatter_curve);
        return;
    case URQ_WIDGET_TYPE_CALENDAR:
        urq_calendar_widget_conf_free_inplace(self->config.calendar);
        return;
    case URQ_WIDGET_TYPE_ROLLER_LIST:
        urq_rollerList_widget_conf_free_inplace(self->config.roller_list);
        return;
    case URQ_WIDGET_TYPE_DROP_LIST:
        urq_dropList_widget_conf_free_inplace(self->config.drop_list);
        return;
    case URQ_WIDGET_TYPE_GROUP:
        // urq_widget_conf_list_init_inplace(self->config.group->widgets);
        return;
    case URQ_WIDGET_TYPE_METER:
        urq_meter_widget_conf_free_inplace(self->config.meter);
        return;
    case URQ_WIDGET_TYPE_RULER:
        urq_ruler_widget_conf_free_inplace(self->config.ruler);
        return;
    case URQ_WIDGET_TYPE_LINEAR:
        urq_linear_widget_conf_free_inplace(self->config.linear);
        return;
    default:
        break;
    }

    if (self->common_conf != NULL) {
        urq_widget_common_conf_free_inplace(self->common_conf);
        urq_free(self->common_conf);
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ__WIDGET__CONF_H
