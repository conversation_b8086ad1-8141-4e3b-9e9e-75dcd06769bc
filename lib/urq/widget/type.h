#pragma once

#ifndef URQ__WIDGET__TYPE_H
#define URQ__WIDGET__TYPE_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 组件类型
typedef enum {
    /// @brief 工程
    URQ_WIDGET_TYPE_PROJECT, // 工程
    /// @brief 画面
    URQ_WIDGET_TYPE_PAGE, // 画面
    // /// @brief 默认布局
    // URQ_WIDGET_TYPE_DEFAULT_LAYOUT_WIDGET,
    /// @brief 静态图片
    URQ_WIDGET_TYPE_IMAGE_WIDGET,
    /// @brief 静态文本
    URQ_WIDGET_TYPE_LABEL_WIDGET,
    /// @brief 文本输入
    URQ_WIDGET_TYPE_STRING,
    /// @brief 数值输入
    URQ_WIDGET_TYPE_NUMBER,
    /// @brief 位显示
    URQ_WIDGET_TYPE_BIT, // bit widget
    /// @brief 字显示
    URQ_WIDGET_TYPE_WORD, // word widget
    /// @brief 矩阵按钮
    URQ_WIDGET_TYPE_KEYBOARD, // keyboard widget
    /// @brief 按钮
    URQ_WIDGET_TYPE_BUTTON, // button widget
    /// @brief 克隆
    URQ_WIDGET_TYPE_CLONE, // clone widget
    /// @brief 趋势图曲线
    URQ_WIDGET_TYPE_TREND_CURVE, // trend curve widget
    /// @brief XY曲线
    URQ_WIDGET_TYPE_XY_CURVE, // xy curve widget
    /// @brief 柱状图
    URQ_WIDGET_TYPE_BAR_CURVE, // bar curve widget
    /// @brief 散点图
    URQ_WIDGET_TYPE_SCATTER_CURVE, // scatter curve widget
    /// @brief 日历
    URQ_WIDGET_TYPE_CALENDAR, // calendar widget
    /// @brief 滚动
    URQ_WIDGET_TYPE_ROLLER_LIST, // roller widget
    /// @brief 下拉
    URQ_WIDGET_TYPE_DROP_LIST, // drop list widget
    //
    URQ_WIDGET_TYPE_GROUP, // group widget
    /// @brief 仪表
    URQ_WIDGET_TYPE_METER, // meter widget
    /// @brief 刻度尺
    URQ_WIDGET_TYPE_SCALE, // scale widget
    /// @brief 线条
    URQ_WIDGET_TYPE_LINEAR, // linear widget
    /// @brief 弧线
    URQ_WIDGET_TYPE_ARC, // arc widget

    // 所有组件的最大值
    URQ_WIDGET_TYPE_MAX, // max
} urq_widget_type_t;

// 啥也不是的组件
#define URQ_WIDGET_TYPE_NONE URQ_WIDGET_TYPE_MAX

// 让模版不报错，没有实际意义
#define URQ_WIDGET_TYPE_TEMPLATE_WIDGET URQ_WIDGET_TYPE_MAX

/// @brief 组件类型字符串
static const char *_URQ_WIDGET_TYPE_STRING[URQ_WIDGET_TYPE_MAX + 1] = {
    "project",  "page",        "image",     "label",     "string",
    "number",   "bit",         "word",      "keyboard",  "button",
    "clone",    "trend_curve", "xy_curve",  "bar_curve", "scatter_curve",
    "calendar", "roller_list", "drop_list", "group",     "meter",
    "ruler",    "linear",      "arc",
};

/// @brief 组件类型字符串
///
/// @param type 组件类型
/// @return 组件类型字符串
__attribute__((__warn_unused_result__())) static inline const char *
urq_widget_type_to_string(urq_widget_type_t type)
{
    return _URQ_WIDGET_TYPE_STRING[type];
}

#ifdef __cplusplus
}
#endif
#endif // URQ__WIDGET__TYPE_H
