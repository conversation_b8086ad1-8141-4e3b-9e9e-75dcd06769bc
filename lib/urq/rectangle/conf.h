#pragma once

#include "urq/style/line.h"

#ifndef URQ__RECTANGLE__CONF_H
#define URQ__RECTANGLE__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    // 未设置
    URQ_CHAMFER_TYPE_UNSPECIFIED = 0,
    // 圆角
    URQ_CHAMFER_TYPE_ROUND = 1,
    // 直角
    URQ_CHAMFER_TYPE_SQUARE = 2,
} urq_chamfer_type_t;

typedef struct {
    /// @brief 倒角类型
    urq_chamfer_type_t chamfer_type;
    /// @brief 倒角半径
    uint8_t chamfer_radius;
    /// @brief 线条配置
    urq_style_line_t line;
} urq_rect_angle_widget_conf_t;

static inline void urq_rect_angle_widget_conf_init_inplace(
    urq_rect_angle_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_rect_angle_widget_conf_free_inplace(
    urq_rect_angle_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_rect_angle_widget_conf_init_inplace(urq_rect_angle_widget_conf_t *self)
{
    urq_style_line_init_inplace(&self->line);
    self->chamfer_type = URQ_CHAMFER_TYPE_UNSPECIFIED;
    self->chamfer_radius = 0;
}

void urq_rect_angle_widget_conf_free_inplace(urq_rect_angle_widget_conf_t *self)
{
    urq_used(self);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__BIT__CONF_H
